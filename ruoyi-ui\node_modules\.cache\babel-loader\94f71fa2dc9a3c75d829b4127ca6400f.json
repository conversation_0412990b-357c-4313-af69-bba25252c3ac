{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\page.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\page.js", "mtime": 1753865796194}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPageContent", "query", "request", "url", "method", "params", "getPageContent", "contentId", "addPageContent", "data", "updatePageContent", "delPageContent", "getPageContentByCode", "pageCode", "getJoinUsContent", "getAboutUsContent", "getUserAgreementContent", "getPrivacyPolicyContent"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/page.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询页面内容管理列表\nexport function listPageContent(query) {\n  return request({\n    url: '/miniapp/content/page/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询页面内容管理详细\nexport function getPageContent(contentId) {\n  return request({\n    url: '/miniapp/content/page/' + contentId,\n    method: 'get'\n  })\n}\n\n// 新增页面内容管理\nexport function addPageContent(data) {\n  return request({\n    url: '/miniapp/content/page',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改页面内容管理\nexport function updatePageContent(data) {\n  return request({\n    url: '/miniapp/content/page',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除页面内容管理\nexport function delPageContent(contentId) {\n  return request({\n    url: '/miniapp/content/page/' + contentId,\n    method: 'delete'\n  })\n}\n\n// 根据页面编码获取页面内容（小程序端）\nexport function getPageContentByCode(pageCode) {\n  return request({\n    url: '/miniapp/content/page/app/getByCode/' + pageCode,\n    method: 'get'\n  })\n}\n\n// 获取加入我们页面内容（小程序端）\nexport function getJoinUsContent() {\n  return request({\n    url: '/miniapp/content/page/app/getJoinUs',\n    method: 'get'\n  })\n}\n\n// 获取关于我们页面内容（小程序端）\nexport function getAboutUsContent() {\n  return request({\n    url: '/miniapp/content/page/app/getAboutUs',\n    method: 'get'\n  })\n}\n\n// 获取用户协议内容（小程序端）\nexport function getUserAgreementContent() {\n  return request({\n    url: '/miniapp/content/page/app/getUserAgreement',\n    method: 'get'\n  })\n}\n\n// 获取隐私协议内容（小程序端）\nexport function getPrivacyPolicyContent() {\n  return request({\n    url: '/miniapp/content/page/app/getPrivacyPolicy',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,SAAS;IACzCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,SAAS,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,SAAS;IACzCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,oBAAoBA,CAACC,QAAQ,EAAE;EAC7C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC,GAAGU,QAAQ;IACtDT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}