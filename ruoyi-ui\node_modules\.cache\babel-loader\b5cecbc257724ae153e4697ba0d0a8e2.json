{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\xiqing\\activity-config.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\xiqing\\activity-config.js", "mtime": 1753694616737}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listActivityConfig", "query", "request", "url", "method", "params", "getActivityConfig", "activityId", "addActivityConfig", "data", "updateActivityConfig", "delActivityConfig", "activityIds", "exportActivityConfig"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/xiqing/activity-config.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询西青金种子路演活动配置列表\r\nexport function listActivityConfig(query) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity-config/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询西青金种子路演活动配置详细\r\nexport function getActivityConfig(activityId) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity-config/' + activityId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增西青金种子路演活动配置\r\nexport function addActivityConfig(data) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity-config',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改西青金种子路演活动配置\r\nexport function updateActivityConfig(data) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity-config',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除西青金种子路演活动配置\r\nexport function delActivityConfig(activityIds) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity-config/' + activityIds,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出西青金种子路演活动配置\r\nexport function exportActivityConfig(query) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity-config/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACC,UAAU,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGI,UAAU;IACpDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,oBAAoBA,CAACD,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,iBAAiBA,CAACC,WAAW,EAAE;EAC7C,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGS,WAAW;IACrDR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,oBAAoBA,CAACZ,KAAK,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}