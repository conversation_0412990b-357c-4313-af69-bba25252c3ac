<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniTechStarMapper">
    
    <resultMap type="MiniTechStar" id="MiniTechStarResult">
        <result property="starId"    column="star_id"    />
        <result property="name"    column="name"    />
        <result property="photoUrl"    column="photo_url"    />
        <result property="topImageUrl"    column="top_image_url"    />
        <result property="companyName"    column="company_name"    />
        <result property="description"    column="description"    />
        <result property="founderIntroduction"    column="founder_introduction"    />
        <result property="companyIntroduction"    column="company_introduction"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniTechStarVo">
        select star_id, name, photo_url, top_image_url, company_name, description, founder_introduction, company_introduction, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_tech_star
    </sql>

    <select id="selectMiniTechStarList" parameterType="MiniTechStar" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniTechStarByStarId" parameterType="Long" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        where star_id = #{starId}
    </select>

    <select id="selectEnabledMiniTechStarList" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectRecommendedMiniTechStarList" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
        limit 10
    </select>
        
    <insert id="insertMiniTechStar" parameterType="MiniTechStar" useGeneratedKeys="true" keyProperty="starId">
        insert into mini_tech_star
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url,</if>
            <if test="topImageUrl != null and topImageUrl != ''">top_image_url,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="founderIntroduction != null and founderIntroduction != ''">founder_introduction,</if>
            <if test="companyIntroduction != null and companyIntroduction != ''">company_introduction,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="photoUrl != null and photoUrl != ''">#{photoUrl},</if>
            <if test="topImageUrl != null and topImageUrl != ''">#{topImageUrl},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="founderIntroduction != null and founderIntroduction != ''">#{founderIntroduction},</if>
            <if test="companyIntroduction != null and companyIntroduction != ''">#{companyIntroduction},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniTechStar" parameterType="MiniTechStar">
        update mini_tech_star
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url = #{photoUrl},</if>
            <if test="topImageUrl != null and topImageUrl != ''">top_image_url = #{topImageUrl},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="founderIntroduction != null">founder_introduction = #{founderIntroduction},</if>
            <if test="companyIntroduction != null">company_introduction = #{companyIntroduction},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where star_id = #{starId}
    </update>

    <delete id="deleteMiniTechStarByStarId" parameterType="Long">
        delete from mini_tech_star where star_id = #{starId}
    </delete>

    <delete id="deleteMiniTechStarByStarIds" parameterType="String">
        delete from mini_tech_star where star_id in 
        <foreach item="starId" collection="array" open="(" separator="," close=")">
            #{starId}
        </foreach>
    </delete>

</mapper> 