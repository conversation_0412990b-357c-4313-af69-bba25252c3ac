//package com.ruoyi.miniapp.controller;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.miniapp.domain.*;
//import com.ruoyi.miniapp.service.*;
//import com.ruoyi.system.service.ISysConfigService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//
//
///**
// * 小程序API综合Controller
// *
// * <AUTHOR>
// * @date 2024-01-01
// */
//@Api(tags = "小程序综合API")
//@RestController
//@RequestMapping("/miniapp/api")
//public class MiniAppApiController extends BaseController
//{
//    @Autowired
//    private IMiniBannerService miniBannerService;
//
//    @Autowired
//    private IMiniNoticeService miniNoticeService;
//
//    @Autowired
//    private IMiniBarrageService miniBarrageService;
//
//    @Autowired
//    private IMiniActivityService miniActivityService;
//
//    @Autowired
//    private IMiniTechStarService miniTechStarService;
//
//    @Autowired
//    private IMiniJobService miniJobService;
//
//    @Autowired
//    private IMiniDemandService miniDemandService;
//
//    @Autowired
//    private IMiniEventService miniEventService;
//
//
//
//    @Autowired
//    private IMiniPageContentService miniPageContentService;
//
//    @Autowired
//    private IMiniDemandCategoryService miniDemandCategoryService;
//
//    @Autowired
//    private IMiniParkService miniParkService;
//
//    @Autowired
//    private ISysConfigService configService;
//
//    @Autowired
//    private IXiqingActivityContentService xiqingActivityContentService;
//
//    /**
//     * 获取首页数据
//     */
//    @ApiOperation("获取首页数据")
//    @PostMapping("/getHomeData")
//    public AjaxResult getHomeData()
//    {
//        Map<String, Object> data = new HashMap<>();
//
//        // 轮播图数据
//        List<MiniBanner> banners = miniBannerService.selectEnabledMiniBannerList();
//        data.put("banners", banners);
//
//        // 滚动通知数据
//        List<MiniNotice> notices = miniNoticeService.selectEnabledMiniNoticeList();
//        data.put("notices", notices);
//
//        // 通过审核的弹幕数据
//        List<MiniBarrage> barrages = miniBarrageService.selectApprovedMiniBarrageList();
//        data.put("barrages", barrages);
//
//        // 推荐活动
//        List<MiniActivity> activities = miniActivityService.selectRecommendedMiniActivityList();
//        data.put("activities", activities);
//
//        // 科技之星
//        List<MiniTechStar> techStars = miniTechStarService.selectRecommendedMiniTechStarList();
//        data.put("techStars", techStars);
//
//        // 推荐招聘
//        List<MiniJob> jobs = miniJobService.selectRecommendedMiniJobList();
//        data.put("jobs", jobs);
//
//        return AjaxResult.success(data);
//    }
//
//    /**
//     * 获取需求对接平台数据
//     */
//    @ApiOperation("获取需求对接平台数据")
//    @PostMapping("/getDemandData")
//    public AjaxResult getDemandData()
//    {
//        Map<String, Object> data = new HashMap<>();
//
//        // 需求分类
//        List<MiniDemandCategory> categories = miniDemandCategoryService.selectEnabledMiniDemandCategoryList();
//        data.put("categories", categories);
//
//        // 推荐需求
//        List<MiniDemand> recommendedDemands = miniDemandService.selectRecommendedMiniDemandList();
//        data.put("recommendedDemands", recommendedDemands);
//
//        // 所有启用的需求
//        List<MiniDemand> allDemands = miniDemandService.selectEnabledMiniDemandList();
//        data.put("allDemands", allDemands);
//
//        return AjaxResult.success(data);
//    }
//
//    /**
//     * 获取活动报名数据
//     */
//    @ApiOperation("获取活动报名数据")
//    @PostMapping("/getEventData")
//    public AjaxResult getEventData()
//    {
//        Map<String, Object> data = new HashMap<>();
//
//        // 正在进行的活动
//        List<MiniEvent> activeEvents = miniEventService.selectActiveMiniEventList();
//        data.put("activeEvents", activeEvents);
//
//        // 所有启用的活动
//        List<MiniEvent> allEvents = miniEventService.selectEnabledMiniEventList();
//        data.put("allEvents", allEvents);
//
//        return AjaxResult.success(data);
//    }
//
//
//
//    /**
//     * 获取页面内容
//     */
//    @ApiOperation("获取页面内容")
//    @PostMapping("/getPageContent")
//    public AjaxResult getPageContent(@ApiParam("页面标识") @RequestBody String pageKey)
//    {
//        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageKey(pageKey);
//        return AjaxResult.success(content);
//    }
//
//    /**
//     * 获取关于我们页面内容
//     */
//    @ApiOperation("获取关于我们页面内容")
//    @PostMapping("/getAboutUs")
//    public AjaxResult getAboutUs()
//    {
//        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageKey("about_us");
//        return AjaxResult.success(content);
//    }
//
//    /**
//     * 获取用户协议内容
//     */
//    @ApiOperation("获取用户协议内容")
//    @PostMapping("/getUserAgreement")
//    public AjaxResult getUserAgreement()
//    {
//        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageKey("user_agreement");
//        return AjaxResult.success(content);
//    }
//
//    /**
//     * 获取隐私协议内容
//     */
//    @ApiOperation("获取隐私协议内容")
//    @PostMapping("/getPrivacyPolicy")
//    public AjaxResult getPrivacyPolicy()
//    {
//        MiniPageContent content = miniPageContentService.selectMiniPageContentByPageKey("privacy_policy");
//        return AjaxResult.success(content);
//    }
//
//    /**
//     * 获取所有页面内容
//     */
//    @ApiOperation("获取所有页面内容")
//    @PostMapping("/getAllPageContents")
//    public AjaxResult getAllPageContents()
//    {
//        List<MiniPageContent> contents = miniPageContentService.selectEnabledMiniPageContentList();
//        return AjaxResult.success(contents);
//    }
//
//    /**
//     * 搜索综合数据
//     */
//    @ApiOperation("搜索综合数据")
//    @PostMapping("/search")
//    public AjaxResult search(@ApiParam("搜索关键词") @RequestBody String keyword)
//    {
//        Map<String, Object> data = new HashMap<>();
//
//        // 这里可以实现跨模块的搜索功能
//        // 搜索活动
//        MiniActivity activityQuery = new MiniActivity();
//        activityQuery.setTitle(keyword);
//        List<MiniActivity> activities = miniActivityService.selectMiniActivityList(activityQuery);
//        data.put("activities", activities);
//
//        // 搜索需求
//        MiniDemand demandQuery = new MiniDemand();
//        demandQuery.setTitle(keyword);
//        List<MiniDemand> demands = miniDemandService.selectMiniDemandList(demandQuery);
//        data.put("demands", demands);
//
//        // 搜索招聘
//        MiniJob jobQuery = new MiniJob();
//        jobQuery.setJobTitle(keyword);
//        List<MiniJob> jobs = miniJobService.selectMiniJobList(jobQuery);
//        data.put("jobs", jobs);
//
//        return AjaxResult.success(data);
//    }
//
//    /**
//     * 获取园区列表
//     */
//    @ApiOperation("获取园区列表")
//    @PostMapping("/getParkList")
//    public AjaxResult getParkList()
//    {
//        List<MiniPark> parks = miniParkService.selectEnabledMiniParkList();
//        return AjaxResult.success(parks);
//    }
//
//    /**
//     * 获取推荐园区列表
//     */
//    @ApiOperation("获取推荐园区列表")
//    @PostMapping("/getRecommendedParkList")
//    public AjaxResult getRecommendedParkList()
//    {
//        List<MiniPark> parks = miniParkService.selectRecommendedMiniParkList();
//        return AjaxResult.success(parks);
//    }
//
//    /**
//     * 获取园区详情
//     */
//    @ApiOperation("获取园区详情")
//    @PostMapping("/getParkDetail")
//    public AjaxResult getParkDetail(@RequestParam Long parkId)
//    {
//        MiniPark park = miniParkService.selectMiniParkByParkId(parkId);
//        if (park == null || !"0".equals(park.getStatus())) {
//            return AjaxResult.error("园区不存在或已停用");
//        }
//        return AjaxResult.success(park);
//    }
//
//    /**
//     * 获取园区简介图片
//     */
//    @ApiOperation("获取园区简介图片")
//    @PostMapping("/getParkIntroImage")
//    public AjaxResult getParkIntroImage()
//    {
//        String introImageUrl = configService.selectConfigByKey("miniapp.park.intro.image");
//        return AjaxResult.success(introImageUrl);
//    }
//
//    /**
//     * 获取西青金种子专区活动内容
//     */
//    @ApiOperation("获取西青金种子专区活动内容")
//    @PostMapping("/getXiqingActivityContent")
//    public AjaxResult getXiqingActivityContent()
//    {
//        XiqingActivityContent content = xiqingActivityContentService.selectEnabledXiqingActivityContent();
//        return AjaxResult.success(content);
//    }
//}