(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20f5a7"],{b2ef:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"专家姓名",prop:"expertName"}},[r("el-input",{attrs:{placeholder:"请输入专家姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.expertName,callback:function(t){e.$set(e.queryParams,"expertName",t)},expression:"queryParams.expertName"}})],1),r("el-form-item",{attrs:{label:"专家职位",prop:"expertTitle"}},[r("el-input",{attrs:{placeholder:"请输入专家职位",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.expertTitle,callback:function(t){e.$set(e.queryParams,"expertTitle",t)},expression:"queryParams.expertTitle"}})],1),r("el-form-item",{attrs:{label:"专家公司",prop:"expertCompany"}},[r("el-input",{attrs:{placeholder:"请输入专家公司",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.expertCompany,callback:function(t){e.$set(e.queryParams,"expertCompany",t)},expression:"queryParams.expertCompany"}})],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:expert:add"],expression:"['miniapp:expert:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:expert:edit"],expression:"['miniapp:expert:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:expert:remove"],expression:"['miniapp:expert:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:expert:export"],expression:"['miniapp:expert:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.expertList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"专家ID",align:"center",prop:"id",width:"80"}}),r("el-table-column",{attrs:{label:"头像",align:"center",prop:"avatarUrl",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("image-preview",{attrs:{src:e.row.avatarUrl,width:50,height:50}})]}}])}),r("el-table-column",{attrs:{label:"专家姓名",align:"center",prop:"expertName"}}),r("el-table-column",{attrs:{label:"专家职位",align:"center",prop:"expertTitle","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"专家公司",align:"center",prop:"expertCompany","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"从业年限",align:"center",prop:"yearsExperience"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(t.row.yearsExperience)+"年")])]}}])}),r("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder"}}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:expert:edit"],expression:"['miniapp:expert:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:expert:remove"],expression:"['miniapp:expert:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"专家姓名",prop:"expertName"}},[r("el-input",{attrs:{placeholder:"请输入专家姓名"},model:{value:e.form.expertName,callback:function(t){e.$set(e.form,"expertName",t)},expression:"form.expertName"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"专家职位",prop:"expertTitle"}},[r("el-input",{attrs:{placeholder:"请输入专家职位"},model:{value:e.form.expertTitle,callback:function(t){e.$set(e.form,"expertTitle",t)},expression:"form.expertTitle"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"专家公司",prop:"expertCompany"}},[r("el-input",{attrs:{placeholder:"请输入专家公司"},model:{value:e.form.expertCompany,callback:function(t){e.$set(e.form,"expertCompany",t)},expression:"form.expertCompany"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"从业年限",prop:"yearsExperience"}},[r("el-input-number",{attrs:{min:0},model:{value:e.form.yearsExperience,callback:function(t){e.$set(e.form,"yearsExperience",t)},expression:"form.yearsExperience"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"头像",prop:"avatarUrl"}},[r("image-upload",{model:{value:e.form.avatarUrl,callback:function(t){e.$set(e.form,"avatarUrl",t)},expression:"form.avatarUrl"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"专家介绍",prop:"expertIntro"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入专家介绍",rows:3},model:{value:e.form.expertIntro,callback:function(t){e.$set(e.form,"expertIntro",t)},expression:"form.expertIntro"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"专业领域",prop:"expertiseFields"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入专业领域",rows:3},model:{value:e.form.expertiseFields,callback:function(t){e.$set(e.form,"expertiseFields",t)},expression:"form.expertiseFields"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"重要成就",prop:"notableAchievements"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入重要成就",rows:3},model:{value:e.form.notableAchievements,callback:function(t){e.$set(e.form,"notableAchievements",t)},expression:"form.notableAchievements"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"联系信息",prop:"contactInfo"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入联系信息",rows:3},model:{value:e.form.contactInfo,callback:function(t){e.$set(e.form,"contactInfo",t)},expression:"form.contactInfo"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"详细描述",prop:"detailedDescription"}},[r("editor",{attrs:{"min-height":300},model:{value:e.form.detailedDescription,callback:function(t){e.$set(e.form,"detailedDescription",t)},expression:"form.detailedDescription"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[r("el-input-number",{attrs:{min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[r("el-radio",{attrs:{label:0}},[e._v("启用")]),r("el-radio",{attrs:{label:1}},[e._v("禁用")])],1)],1)],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=r("5530"),i=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function o(e){return Object(i["a"])({url:"/miniapp/expert/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/miniapp/expert/"+e,method:"get"})}function p(e){return Object(i["a"])({url:"/miniapp/expert",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/miniapp/expert",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/miniapp/expert/"+e,method:"delete"})}var u={name:"Expert",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,expertList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,expertName:null,expertTitle:null,expertCompany:null,status:null},form:{},rules:{expertName:[{required:!0,message:"专家姓名不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.expertList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,expertName:null,expertTitle:null,expertCompany:null,expertIntro:null,avatarUrl:null,expertiseFields:null,yearsExperience:0,notableAchievements:null,contactInfo:null,detailedDescription:null,sortOrder:0,status:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加专家矩阵"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改专家矩阵"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):p(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除专家矩阵编号为"'+r+'"的数据项？').then((function(){return m(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/expert/export",Object(n["a"])({},this.queryParams),"专家矩阵_".concat((new Date).getTime(),".xlsx"))}}},d=u,f=r("2877"),h=Object(f["a"])(d,a,l,!1,null,null,null);t["default"]=h.exports}}]);