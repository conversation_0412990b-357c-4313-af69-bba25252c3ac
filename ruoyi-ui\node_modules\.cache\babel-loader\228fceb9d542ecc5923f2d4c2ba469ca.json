{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue", "mtime": 1753846134162}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}