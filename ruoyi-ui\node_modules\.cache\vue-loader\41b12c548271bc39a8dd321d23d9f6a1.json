{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753847579546}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UGFyaywgZ2V0UGFyaywgZGVsUGFyaywgYWRkUGFyaywgdXBkYXRlUGFyaywgZXhwb3J0UGFyaywgZ2V0UGFya0ludHJvSW1hZ2UsIHVwZGF0ZVBhcmtJbnRyb0ltYWdlIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9wYXJrIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTWluaVBhcmsiLA0KICBkaWN0czogWydzeXNfbm9ybWFsX2Rpc2FibGUnXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOWbreWMuueuoeeQhuihqOagvOaVsOaNrg0KICAgICAgcGFya0xpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5Zut5Yy6566A5LuL5Zu+54mH5a+56K+d5qGGDQogICAgICBpbnRyb0ltYWdlT3BlbjogZmFsc2UsDQogICAgICAvLyDlm63ljLrnroDku4vlm77niYfooajljZUNCiAgICAgIGludHJvSW1hZ2VGb3JtOiB7DQogICAgICAgIGludHJvSW1hZ2VVcmw6ICcnDQogICAgICB9LA0KICAgICAgLy8g5Zu+54mH5Yqg6L2954q25oCBDQogICAgICBpbnRyb0ltYWdlTG9hZGluZzogZmFsc2UsDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcGFya05hbWU6IG51bGwsDQogICAgICAgIHBhcmtDb2RlOiBudWxsLA0KICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgcGFya05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Zut5Yy65ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc29ydE9yZGVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaOkuW6j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouWbreWMuueuoeeQhuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFBhcmsodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFya0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5oyJ6ZKuICovDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvKiog6KGo5Y2V6YeN572uICovDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHBhcmtJZDogbnVsbCwNCiAgICAgICAgcGFya05hbWU6IG51bGwsDQogICAgICAgIHBhcmtDb2RlOiBudWxsLA0KICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwNCiAgICAgICAgY29udGVudDogbnVsbCwNCiAgICAgICAgY292ZXJJbWFnZTogbnVsbCwNCiAgICAgICAgc29ydE9yZGVyOiAwLA0KICAgICAgICBzdGF0dXM6ICIwIiwNCiAgICAgICAgcmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLyoqIOWkmumAieahhumAieS4reaVsOaNriAqLw0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucGFya0lkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlm63ljLrnrqHnkIYiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IHBhcmtJZCA9IHJvdy5wYXJrSWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXRQYXJrKHBhcmtJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Zut5Yy6566h55CGIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnBhcmtJZCAhPT0gbnVsbCAmJiB0aGlzLmZvcm0ucGFya0lkICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIHVwZGF0ZVBhcmsodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRQYXJrKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHBhcmtJZHMgPSByb3cucGFya0lkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Zut5Yy6566h55CG57yW5Y+35Li6IicgKyBwYXJrSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsUGFyayhwYXJrSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Zut5Yy6566h55CG5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgIHJldHVybiBleHBvcnRQYXJrKHRoaXMucXVlcnlQYXJhbXMpOw0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJGRvd25sb2FkLmV4Y2VsKHJlc3BvbnNlLCAn5Zut5Yy6566h55CG5pWw5o2uLnhsc3gnKTsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5o6S5bqP5L+u5pS5ICovDQogICAgaGFuZGxlU29ydENoYW5nZShyb3cpIHsNCiAgICAgIHVwZGF0ZVBhcmsocm93KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5L+u5pS55oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Zut5Yy6566A5LuL5Zu+54mH5LiK5LygICovDQogICAgaGFuZGxlSW50cm9JbWFnZVVwbG9hZCgpIHsNCiAgICAgIC8vIOWFiOmHjee9ruihqOWNleaVsOaNrg0KICAgICAgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7DQogICAgICB0aGlzLmludHJvSW1hZ2VPcGVuID0gdHJ1ZTsNCiAgICAgIC8vIOWKoOi9veW9k+WJjeWbvueJh+aVsOaNrg0KICAgICAgdGhpcy5sb2FkSW50cm9JbWFnZSgpOw0KICAgIH0sDQogICAgLyoqIOWKoOi9veWbreWMuueugOS7i+WbvueJhyAqLw0KICAgIGxvYWRJbnRyb0ltYWdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WKoOi9veWbreWMuueugOS7i+WbvueJhy4uLicpOw0KICAgICAgdGhpcy5pbnRyb0ltYWdlTG9hZGluZyA9IHRydWU7DQoNCiAgICAgIGdldFBhcmtJbnRyb0ltYWdlKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5blm63ljLrnroDku4vlm77niYflk43lupQ6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgLy8g5LyY5YWI6K+75Y+WZGF0YeWtl+aute+8jOWmguaenOayoeacieWImeivu+WPlm1zZ+Wtl+aute+8iOWQkeWQjuWFvOWuue+8iQ0KICAgICAgICAgIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybCA9IHJlc3BvbnNlLmRhdGEgfHwgcmVzcG9uc2UubXNnIHx8ICcnOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCforr7nva7lm77niYdVUkw6JywgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPluWbreWMuueugOS7i+WbvueJh+Wksei0pTonLCByZXNwb25zZS5tc2cpOw0KICAgICAgICAgIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybCA9ICcnOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWbreWMuueugOS7i+WbvueJh+WHuumUmTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybCA9ICcnOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5Yqg6L295Zut5Yy6566A5LuL5Zu+54mH5aSx6LSlIik7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5pbnRyb0ltYWdlTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5Zut5Yy6566A5LuL5Zu+54mHICovDQogICAgY2FuY2VsSW50cm9JbWFnZSgpIHsNCiAgICAgIHRoaXMuaW50cm9JbWFnZU9wZW4gPSBmYWxzZTsNCiAgICAgIC8vIOS4jeimgea4heepuuaVsOaNru+8jOS/neaMgeWOn+acieaVsOaNrg0KICAgICAgLy8gdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5Zut5Yy6566A5LuL5Zu+54mHICovDQogICAgc3VibWl0SW50cm9JbWFnZUZvcm0oKSB7DQogICAgICB1cGRhdGVQYXJrSW50cm9JbWFnZSh0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmwpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlm63ljLrnroDku4vlm77niYfmm7TmlrDmiJDlip8iKTsNCiAgICAgICAgdGhpcy5pbnRyb0ltYWdlT3BlbiA9IGZhbHNlOw0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5pu05paw5Zut5Yy6566A5LuL5Zu+54mH5aSx6LSlIik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDpooTop4jlm63ljLrnroDku4vlm77niYcgKi8NCiAgICBwcmV2aWV3SW50cm9JbWFnZSgpIHsNCiAgICAgIGlmICh0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmwgJiYgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsLnRyaW0oKSAhPT0gJycpIHsNCiAgICAgICAgdGhpcy5wcmV2aWV3SW1hZ2VVcmwgPSB0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmw7DQogICAgICAgIHRoaXMucHJldmlld1Zpc2libGUgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi5pqC5peg5Zu+54mH5Y+v6aKE6KeIIik7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5YWz6Zet5Zu+54mH6aKE6KeIICovDQogICAgY2xvc2VQcmV2aWV3KCkgew0KICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5wcmV2aWV3SW1hZ2VVcmwgPSAnJzsNCiAgICB9LA0KICAgIC8qKiDlm77niYfliqDovb3plJnor6/lpITnkIYgKi8NCiAgICBoYW5kbGVJbWFnZUVycm9yKGV2ZW50KSB7DQogICAgICBjb25zb2xlLmVycm9yKCflm77niYfliqDovb3lpLHotKU6JywgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlm77niYfliqDovb3lpLHotKXvvIzor7fmo4Dmn6Xlm77niYfpk77mjqXmmK/lkKbmnInmlYgiKTsNCiAgICAgIC8vIOWPr+S7peiuvue9ruS4gOS4qum7mOiupOeahOmUmeivr+WbvueJhw0KICAgICAgLy8gZXZlbnQudGFyZ2V0LnNyYyA9ICcvcGF0aC90by9kZWZhdWx0LWVycm9yLWltYWdlLnBuZyc7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6SA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/park", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"园区名称\" prop=\"parkName\">\r\n        <el-input\r\n          v-model=\"queryParams.parkName\"\r\n          placeholder=\"请输入园区名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"园区编码\" prop=\"parkCode\">\r\n        <el-input\r\n          v-model=\"queryParams.parkCode\"\r\n          placeholder=\"请输入园区编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"园区简介\" prop=\"description\">\r\n        <el-input\r\n          v-model=\"queryParams.description\"\r\n          placeholder=\"请输入园区简介\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:park:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:park:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:park:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:park:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-picture\"\r\n          size=\"mini\"\r\n          @click=\"handleIntroImageUpload\"\r\n          v-hasPermi=\"['miniapp:park:edit']\"\r\n        >园区简介图片</el-button>\r\n      </el-col>\r\n\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"parkList\" \r\n      @selection-change=\"handleSelectionChange\"\r\n      row-key=\"parkId\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"园区ID\" align=\"center\" prop=\"parkId\" width=\"80\" />\r\n      <el-table-column label=\"园区名称\" align=\"center\" prop=\"parkName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"园区编码\" align=\"center\" prop=\"parkCode\" width=\"120\" />\r\n      <el-table-column label=\"园区简介\" align=\"center\" prop=\"description\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImage\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.coverImage\" :width=\"80\" :height=\"50\" v-if=\"scope.row.coverImage\"/>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input-number \r\n            v-model=\"scope.row.sortOrder\" \r\n            :min=\"0\" \r\n            size=\"mini\"\r\n            :controls=\"false\"\r\n            @change=\"handleSortChange(scope.row)\"\r\n            style=\"width: 70px;\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"160\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:park:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:park:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改园区管理对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"园区名称\" prop=\"parkName\">\r\n              <el-input v-model=\"form.parkName\" placeholder=\"请输入园区名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"园区编码\" prop=\"parkCode\">\r\n              <el-input v-model=\"form.parkCode\" placeholder=\"请输入园区编码\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"园区简介\" prop=\"description\">\r\n          <el-input v-model=\"form.description\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入园区简介\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"封面图片\" prop=\"coverImage\">\r\n          <image-upload v-model=\"form.coverImage\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"详细内容\" prop=\"content\">\r\n          <editor v-model=\"form.content\" :min-height=\"300\"/>\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 园区简介图片上传对话框 -->\r\n    <el-dialog title=\"园区简介图片管理\" :visible.sync=\"introImageOpen\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"introImageForm\" :model=\"introImageForm\" label-width=\"120px\">\r\n        <el-form-item label=\"当前简介图片\">\r\n          <!-- 加载状态 -->\r\n          <div v-if=\"introImageLoading\" style=\"padding: 20px; text-align: center;\">\r\n            <i class=\"el-icon-loading\" style=\"margin-right: 5px;\"></i>\r\n            <span>正在加载图片信息...</span>\r\n          </div>\r\n\r\n          <!-- 已设置图片 -->\r\n          <div v-else-if=\"introImageForm.introImageUrl && introImageForm.introImageUrl.trim() !== ''\" style=\"padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n              <div>\r\n                <i class=\"el-icon-success\" style=\"color: #67c23a; margin-right: 5px;\"></i>\r\n                <span style=\"color: #409eff;\">已设置园区简介图片</span>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"previewIntroImage\"\r\n              >\r\n                预览图片\r\n              </el-button>\r\n            </div>\r\n            <!-- 图片预览缩略图 -->\r\n            <div style=\"margin-top: 10px;\">\r\n              <img\r\n                :src=\"introImageForm.introImageUrl\"\r\n                style=\"width: 120px; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer; border: 1px solid #dcdfe6;\"\r\n                @click=\"previewIntroImage\"\r\n                @error=\"handleImageError\"\r\n                alt=\"园区简介图片缩略图\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 未设置图片 -->\r\n          <div v-else style=\"padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-warning\" style=\"color: #e6a23c; margin-right: 5px;\"></i>\r\n            <span style=\"color: #e6a23c;\">暂未设置园区简介图片</span>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传新图片\">\r\n          <image-upload v-model=\"introImageForm.introImageUrl\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitIntroImageForm\">确 定</el-button>\r\n        <el-button @click=\"cancelIntroImage\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      title=\"园区简介图片预览\"\r\n      :visible.sync=\"previewVisible\"\r\n      width=\"800px\"\r\n      append-to-body\r\n      :before-close=\"closePreview\"\r\n    >\r\n      <div class=\"image-preview-container\">\r\n        <img\r\n          :src=\"previewImageUrl\"\r\n          class=\"preview-image\"\r\n          alt=\"园区简介图片\"\r\n        />\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closePreview\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"window.open(previewImageUrl, '_blank')\">\r\n          <i class=\"el-icon-zoom-in\"></i> 原始大小查看\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPark, getPark, delPark, addPark, updatePark, exportPark, getParkIntroImage, updateParkIntroImage } from \"@/api/miniapp/park\";\r\n\r\nexport default {\r\n  name: \"MiniPark\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 园区管理表格数据\r\n      parkList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 园区简介图片对话框\r\n      introImageOpen: false,\r\n      // 园区简介图片表单\r\n      introImageForm: {\r\n        introImageUrl: ''\r\n      },\r\n      // 图片加载状态\r\n      introImageLoading: false,\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        parkName: null,\r\n        parkCode: null,\r\n        description: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parkName: [\r\n          { required: true, message: \"园区名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sortOrder: [\r\n          { required: true, message: \"排序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询园区管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPark(this.queryParams).then(response => {\r\n        this.parkList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        parkId: null,\r\n        parkName: null,\r\n        parkCode: null,\r\n        description: null,\r\n        content: null,\r\n        coverImage: null,\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.parkId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加园区管理\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const parkId = row.parkId || this.ids;\r\n      getPark(parkId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改园区管理\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          if (this.form.parkId !== null && this.form.parkId !== undefined) {\r\n            updatePark(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPark(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const parkIds = row.parkId || this.ids;\r\n      this.$modal.confirm('是否确认删除园区管理编号为\"' + parkIds + '\"的数据项？').then(function() {\r\n        return delPark(parkIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有园区管理数据项？').then(() => {\r\n        this.loading = true;\r\n        return exportPark(this.queryParams);\r\n      }).then(response => {\r\n        this.$download.excel(response, '园区管理数据.xlsx');\r\n        this.loading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 排序修改 */\r\n    handleSortChange(row) {\r\n      updatePark(row).then(response => {\r\n        this.$modal.msgSuccess(\"排序修改成功\");\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 园区简介图片上传 */\r\n    handleIntroImageUpload() {\r\n      // 先重置表单数据\r\n      this.introImageForm.introImageUrl = '';\r\n      this.introImageOpen = true;\r\n      // 加载当前图片数据\r\n      this.loadIntroImage();\r\n    },\r\n    /** 加载园区简介图片 */\r\n    loadIntroImage() {\r\n      console.log('开始加载园区简介图片...');\r\n      this.introImageLoading = true;\r\n\r\n      getParkIntroImage().then(response => {\r\n        console.log('获取园区简介图片响应:', response);\r\n        if (response.code === 200) {\r\n          // 优先读取data字段，如果没有则读取msg字段（向后兼容）\r\n          this.introImageForm.introImageUrl = response.data || response.msg || '';\r\n          console.log('设置图片URL:', this.introImageForm.introImageUrl);\r\n        } else {\r\n          console.warn('获取园区简介图片失败:', response.msg);\r\n          this.introImageForm.introImageUrl = '';\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载园区简介图片出错:', error);\r\n        this.introImageForm.introImageUrl = '';\r\n        this.$modal.msgError(\"加载园区简介图片失败\");\r\n      }).finally(() => {\r\n        this.introImageLoading = false;\r\n      });\r\n    },\r\n    /** 取消园区简介图片 */\r\n    cancelIntroImage() {\r\n      this.introImageOpen = false;\r\n      // 不要清空数据，保持原有数据\r\n      // this.introImageForm.introImageUrl = '';\r\n    },\r\n    /** 提交园区简介图片 */\r\n    submitIntroImageForm() {\r\n      updateParkIntroImage(this.introImageForm.introImageUrl).then(response => {\r\n        this.$modal.msgSuccess(\"园区简介图片更新成功\");\r\n        this.introImageOpen = false;\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"更新园区简介图片失败\");\r\n      });\r\n    },\r\n    /** 预览园区简介图片 */\r\n    previewIntroImage() {\r\n      if (this.introImageForm.introImageUrl && this.introImageForm.introImageUrl.trim() !== '') {\r\n        this.previewImageUrl = this.introImageForm.introImageUrl;\r\n        this.previewVisible = true;\r\n      } else {\r\n        this.$modal.msgWarning(\"暂无图片可预览\");\r\n      }\r\n    },\r\n    /** 关闭图片预览 */\r\n    closePreview() {\r\n      this.previewVisible = false;\r\n      this.previewImageUrl = '';\r\n    },\r\n    /** 图片加载错误处理 */\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', this.introImageForm.introImageUrl);\r\n      this.$modal.msgError(\"图片加载失败，请检查图片链接是否有效\");\r\n      // 可以设置一个默认的错误图片\r\n      // event.target.src = '/path/to/default-error-image.png';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 图片预览样式 */\r\n.image-preview-container {\r\n  text-align: center;\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 500px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.preview-image:hover {\r\n  transform: scale(1.02);\r\n}\r\n\r\n/* 缩略图悬停效果 */\r\n.image-preview-container img[alt=\"园区简介图片缩略图\"]:hover {\r\n  opacity: 0.8;\r\n  transform: scale(1.05);\r\n  transition: all 0.3s ease;\r\n}\r\n</style>\r\n"]}]}