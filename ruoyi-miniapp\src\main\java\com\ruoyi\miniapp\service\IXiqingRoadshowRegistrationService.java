package com.ruoyi.miniapp.service;

import java.util.List;
import com.ruoyi.miniapp.domain.XiqingRoadshowRegistration;

/**
 * 西青金种子路演报名管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface IXiqingRoadshowRegistrationService 
{
    /**
     * 查询西青金种子路演报名管理
     * 
     * @param registrationId 西青金种子路演报名管理主键
     * @return 西青金种子路演报名管理
     */
    public XiqingRoadshowRegistration selectXiqingRoadshowRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询西青金种子路演报名管理列表
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 西青金种子路演报名管理集合
     */
    public List<XiqingRoadshowRegistration> selectXiqingRoadshowRegistrationList(XiqingRoadshowRegistration xiqingRoadshowRegistration);

    /**
     * 新增西青金种子路演报名管理
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 结果
     */
    public int insertXiqingRoadshowRegistration(XiqingRoadshowRegistration xiqingRoadshowRegistration);

    /**
     * 修改西青金种子路演报名管理
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 结果
     */
    public int updateXiqingRoadshowRegistration(XiqingRoadshowRegistration xiqingRoadshowRegistration);

    /**
     * 批量删除西青金种子路演报名管理
     * 
     * @param registrationIds 需要删除的西青金种子路演报名管理主键集合
     * @return 结果
     */
    public int deleteXiqingRoadshowRegistrationByRegistrationIds(Long[] registrationIds);

    /**
     * 删除西青金种子路演报名管理信息
     * 
     * @param registrationId 西青金种子路演报名管理主键
     * @return 结果
     */
    public int deleteXiqingRoadshowRegistrationByRegistrationId(Long registrationId);
}
