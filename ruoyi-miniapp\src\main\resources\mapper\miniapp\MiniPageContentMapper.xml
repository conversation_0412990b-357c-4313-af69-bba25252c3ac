<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniPageContentMapper">

    <resultMap type="MiniPageContent" id="MiniPageContentResult">
        <result property="contentId"    column="content_id"    />
        <result property="pageCode"    column="page_code"    />
        <result property="pageTitle"    column="page_title"    />
        <result property="pageContent"    column="page_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniPageContentVo">
        select content_id, page_code, page_title, page_content, create_by, create_time, update_by, update_time, remark from mini_page_content
    </sql>

    <select id="selectMiniPageContentList" parameterType="MiniPageContent" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        <where>
            <if test="pageCode != null  and pageCode != ''"> and page_code = #{pageCode}</if>
            <if test="pageTitle != null  and pageTitle != ''"> and page_title like concat('%', #{pageTitle}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectMiniPageContentByContentId" parameterType="Long" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where content_id = #{contentId}
    </select>

    <select id="selectMiniPageContentByPageCode" parameterType="String" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where page_code = #{pageCode}
        limit 1
    </select>

    <insert id="insertMiniPageContent" parameterType="MiniPageContent" useGeneratedKeys="true" keyProperty="contentId">
        insert into mini_page_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pageCode != null and pageCode != ''">page_code,</if>
            <if test="pageTitle != null and pageTitle != ''">page_title,</if>
            <if test="pageContent != null and pageContent != ''">page_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pageCode != null and pageCode != ''">#{pageCode},</if>
            <if test="pageTitle != null and pageTitle != ''">#{pageTitle},</if>
            <if test="pageContent != null and pageContent != ''">#{pageContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniPageContent" parameterType="MiniPageContent">
        update mini_page_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="pageCode != null and pageCode != ''">page_code = #{pageCode},</if>
            <if test="pageTitle != null and pageTitle != ''">page_title = #{pageTitle},</if>
            <if test="pageContent != null and pageContent != ''">page_content = #{pageContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where content_id = #{contentId}
    </update>

    <delete id="deleteMiniPageContentByContentId" parameterType="Long">
        delete from mini_page_content where content_id = #{contentId}
    </delete>

    <delete id="deleteMiniPageContentByContentIds" parameterType="String">
        delete from mini_page_content where content_id in
        <foreach item="contentId" collection="array" open="(" separator="," close=")">
            #{contentId}
        </foreach>
    </delete>

    <select id="selectEnabledMiniPageContentList" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        order by create_time desc
    </select>

    <select id="selectMiniPageContentByPageKey" parameterType="String" resultMap="MiniPageContentResult">
        <include refid="selectMiniPageContentVo"/>
        where page_code = #{pageKey}
    </select>

</mapper>