{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=template&id=58dd3dcc&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753847579546}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}