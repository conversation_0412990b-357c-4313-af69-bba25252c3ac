package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.XiqingActivityContent;
import org.apache.ibatis.annotations.Mapper;

/**
 * 西青金种子专区活动内容管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Mapper
public interface XiqingActivityContentMapper 
{
    /**
     * 查询西青金种子专区活动内容管理
     * 
     * @param contentId 西青金种子专区活动内容管理主键
     * @return 西青金种子专区活动内容管理
     */
    public XiqingActivityContent selectXiqingActivityContentByContentId(Long contentId);

    /**
     * 查询西青金种子专区活动内容管理列表
     * 
     * @param xiqingActivityContent 西青金种子专区活动内容管理
     * @return 西青金种子专区活动内容管理集合
     */
    public List<XiqingActivityContent> selectXiqingActivityContentList(XiqingActivityContent xiqingActivityContent);

    /**
     * 修改西青金种子专区活动内容管理
     * 
     * @param xiqingActivityContent 西青金种子专区活动内容管理
     * @return 结果
     */
    public int updateXiqingActivityContent(XiqingActivityContent xiqingActivityContent);

    /**
     * 获取启用的专区活动内容（小程序端调用）
     * 
     * @return 西青金种子专区活动内容管理
     */
    public XiqingActivityContent selectEnabledXiqingActivityContent();
}
