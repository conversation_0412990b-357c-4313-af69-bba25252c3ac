(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-574c9d94"],{"0b8f":function(e,t,a){"use strict";a("1a3f")},"1a3f":function(e,t,a){},aed8:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[a("el-form-item",{attrs:{label:"活动标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入活动标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),a("el-form-item",{attrs:{label:"活动地点",prop:"location"}},[a("el-input",{attrs:{placeholder:"请输入活动地点",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.location,callback:function(t){e.$set(e.queryParams,"location",t)},expression:"queryParams.location"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"活动状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"正常",value:"0"}}),a("el-option",{attrs:{label:"停用",value:"1"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:guidance:add"],expression:"['miniapp:haitang:guidance:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:guidance:remove"],expression:"['miniapp:haitang:guidance:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:guidance:export"],expression:"['miniapp:haitang:guidance:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.guidanceList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"活动ID",align:"center",prop:"eventId",width:"80"}}),a("el-table-column",{attrs:{label:"活动标题",align:"center",prop:"title","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"活动描述",align:"center",prop:"description","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"封面图片",align:"center",prop:"coverImage",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.coverImage?a("image-preview",{attrs:{src:t.row.coverImage,width:50,height:50}}):e._e()]}}])}),a("el-table-column",{attrs:{label:"活动地点",align:"center",prop:"location","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"开始时间",align:"center",prop:"startTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.startTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"结束时间",align:"center",prop:"endTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.endTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"80"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"180","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:guidance:edit"],expression:"['miniapp:haitang:guidance:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:guidance:remove"],expression:"['miniapp:haitang:guidance:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"活动标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入活动标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"活动地点",prop:"location"}},[a("el-input",{attrs:{placeholder:"请输入活动地点"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}})],1)],1)],1),a("el-form-item",{attrs:{label:"活动描述",prop:"description"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入活动描述",rows:3},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),a("el-form-item",{attrs:{label:"封面图片",prop:"coverImage"}},[a("image-upload",{model:{value:e.form.coverImage,callback:function(t){e.$set(e.form,"coverImage",t)},expression:"form.coverImage"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"开始时间",prop:"startTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.startTime,callback:function(t){e.$set(e.form,"startTime",t)},expression:"form.startTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"结束时间",prop:"endTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.endTime,callback:function(t){e.$set(e.form,"endTime",t)},expression:"form.endTime"}})],1)],1)],1),a("el-form-item",{attrs:{label:"报名截止时间",prop:"registrationDeadline"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择报名截止时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.registrationDeadline,callback:function(t){e.$set(e.form,"registrationDeadline",t)},expression:"form.registrationDeadline"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[a("el-input-number",{attrs:{min:0,max:9999},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:"0"}},[e._v("正常")]),a("el-radio",{attrs:{label:"1"}},[e._v("停用")])],1)],1)],1)],1),a("el-form-item",{attrs:{label:"报名表单配置",prop:"formFields"}},[a("div",{staticClass:"form-fields-config"},[a("div",{staticClass:"form-fields-toolbar"},[a("div",{staticClass:"toolbar-left"},[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addFormField}},[e._v(" 添加字段 ")]),a("el-dropdown",{attrs:{size:"small"},on:{command:e.handleTemplateCommand}},[a("el-button",{attrs:{size:"small"}},[e._v(" 预设模板"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"basic"}},[e._v("基础信息模板")]),a("el-dropdown-item",{attrs:{command:"tech"}},[e._v("技术分享模板")]),a("el-dropdown-item",{attrs:{command:"guidance"}},[e._v("项目指导模板")]),a("el-dropdown-item",{attrs:{command:"clear"}},[e._v("清空所有字段")])],1)],1)],1),a("div",{staticClass:"toolbar-right"},[a("el-button",{attrs:{size:"small",icon:"el-icon-view"},on:{click:e.previewForm}},[e._v(" 预览表单 ")])],1)]),e.formFieldsList.length>0?a("div",{staticClass:"form-fields-list"},e._l(e.formFieldsList,(function(t,i){return a("div",{key:i,staticClass:"form-field-item"},[a("div",{staticClass:"field-header"},[a("div",{staticClass:"field-info"},[a("i",{staticClass:"field-icon",class:e.getFieldIcon(t.type)}),a("span",{staticClass:"field-label"},[e._v(e._s(t.label||"未命名字段"))]),t.required?a("el-tag",{attrs:{size:"mini",type:"danger"}},[e._v("必填")]):a("el-tag",{attrs:{size:"mini",type:"info"}},[e._v("选填")])],1),a("div",{staticClass:"field-actions"},[a("el-button",{attrs:{type:"text",size:"mini",disabled:0===i,icon:"el-icon-arrow-up"},on:{click:function(t){return e.moveField(i,-1)}}}),a("el-button",{attrs:{type:"text",size:"mini",disabled:i===e.formFieldsList.length-1,icon:"el-icon-arrow-down"},on:{click:function(t){return e.moveField(i,1)}}}),a("el-button",{staticClass:"danger-btn",attrs:{type:"text",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.removeFormField(i)}}})],1)]),a("div",{staticClass:"field-content"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"field-item"},[a("label",[e._v("字段标签")]),a("el-input",{attrs:{placeholder:"显示给用户的标签，如：姓名、电话等",size:"small"},on:{input:function(a){return e.updateFieldName(t,a)}},model:{value:t.label,callback:function(a){e.$set(t,"label",a)},expression:"field.label"}})],1)]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"field-item"},[a("label",[e._v("字段类型")]),a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择类型",size:"small"},model:{value:t.type,callback:function(a){e.$set(t,"type",a)},expression:"field.type"}},[a("el-option",{attrs:{label:"📝 文本输入",value:"input"}}),a("el-option",{attrs:{label:"📄 多行文本",value:"textarea"}}),a("el-option",{attrs:{label:"🔢 数字输入",value:"number"}}),a("el-option",{attrs:{label:"📧 邮箱",value:"email"}}),a("el-option",{attrs:{label:"📞 电话",value:"tel"}}),a("el-option",{attrs:{label:"🔘 单选",value:"radio"}}),a("el-option",{attrs:{label:"☑️ 多选",value:"checkbox"}}),a("el-option",{attrs:{label:"📋 下拉选择",value:"select"}}),a("el-option",{attrs:{label:"📅 日期",value:"date"}})],1)],1)])],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("div",{staticClass:"field-item"},[a("label",[e._v("是否必填")]),a("el-switch",{model:{value:t.required,callback:function(a){e.$set(t,"required",a)},expression:"field.required"}})],1)]),["radio","checkbox","select"].includes(t.type)?a("el-col",{attrs:{span:20}},[a("div",{staticClass:"field-item"},[a("label",[e._v("选项配置")]),a("el-input",{attrs:{placeholder:"用逗号分隔选项，如：选项1,选项2,选项3",size:"small"},model:{value:t.options,callback:function(a){e.$set(t,"options",a)},expression:"field.options"}}),t.options?a("div",{staticClass:"options-preview"},e._l(t.options.split(","),(function(t,i){return a("el-tag",{key:i,staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{size:"mini"}},[e._v(" "+e._s(t.trim())+" ")])})),1):e._e()],1)]):e._e()],1)],1)])})),0):a("div",{staticClass:"empty-state"},[a("i",{staticClass:"el-icon-document-add"}),a("p",[e._v('暂无表单字段，点击"添加字段"开始配置')])])])]),a("el-dialog",{attrs:{title:"表单预览",visible:e.previewDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.previewDialogVisible=t}}},[a("div",{staticClass:"form-preview"},[a("div",{staticClass:"preview-header"},[a("h3",[e._v(e._s(e.form.title||"活动报名表"))]),a("p",[e._v(e._s(e.form.description||"请填写以下信息完成报名"))])]),a("div",{staticClass:"preview-form"},e._l(e.formFieldsList,(function(t,i){return a("div",{key:i,staticClass:"preview-field"},[a("label",{staticClass:"preview-label"},[e._v(" "+e._s(t.label)+" "),t.required?a("span",{staticClass:"required"},[e._v("*")]):e._e()]),a("div",{staticClass:"preview-input"},["input"===t.type||"email"===t.type||"tel"===t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,size:"small",disabled:""}}):"textarea"===t.type?a("el-input",{attrs:{type:"textarea",placeholder:"请输入"+t.label,size:"small",disabled:""}}):"number"===t.type?a("el-input-number",{attrs:{placeholder:"请输入"+t.label,size:"small",disabled:""}}):"radio"===t.type?a("el-radio-group",{attrs:{disabled:""}},e._l(t.options.split(","),(function(t,i){return a("el-radio",{key:i,attrs:{label:t.trim()}},[e._v(" "+e._s(t.trim())+" ")])})),1):"checkbox"===t.type?a("el-checkbox-group",{attrs:{disabled:""}},e._l(t.options.split(","),(function(t,i){return a("el-checkbox",{key:i,attrs:{label:t.trim()}},[e._v(" "+e._s(t.trim())+" ")])})),1):"select"===t.type?a("el-select",{attrs:{placeholder:"请选择"+t.label,size:"small",disabled:""}},e._l(t.options.split(","),(function(e,t){return a("el-option",{key:t,attrs:{label:e.trim(),value:e.trim()}})})),1):"date"===t.type?a("el-date-picker",{attrs:{type:"date",placeholder:"请选择"+t.label,size:"small",disabled:""}}):e._e()],1)])})),0)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.previewDialogVisible=!1}}},[e._v("关闭")])],1)]),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],r=a("5530"),o=(a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("0643"),a("a573"),a("b775"));function n(e){var t=Object(r["a"])(Object(r["a"])({},e),{},{eventType:"guidance"});return Object(o["a"])({url:"/miniapp/event/list",method:"post",data:t})}function s(e){return Object(o["a"])({url:"/miniapp/event/getInfo",method:"post",data:e})}function c(e){var t=Object(r["a"])(Object(r["a"])({},e),{},{eventType:"guidance"});return Object(o["a"])({url:"/miniapp/event/add",method:"post",data:t})}function d(e){var t=Object(r["a"])(Object(r["a"])({},e),{},{eventType:"guidance"});return Object(o["a"])({url:"/miniapp/event/edit",method:"post",data:t})}function m(e){return Object(o["a"])({url:"/miniapp/event/remove",method:"post",data:e})}var p={name:"Guidance",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,guidanceList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,location:null,status:null},form:{},formFieldsList:[],previewDialogVisible:!1,rules:{title:[{required:!0,message:"活动标题不能为空",trigger:"blur"}],coverImage:[{required:!0,message:"封面图片不能为空",trigger:"blur"}],startTime:[{required:!0,message:"开始时间不能为空",trigger:"change"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.guidanceList=t.rows,e.total=t.total,e.loading=!1})).catch((function(t){console.error("获取指导活动列表失败:",t),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={eventId:null,title:null,coverImage:null,description:null,location:null,startTime:null,endTime:null,registrationDeadline:null,formFields:null,sortOrder:0,status:"0",remark:null},this.formFieldsList=[],this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.eventId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加项目指导活动"},handleUpdate:function(e){var t=this;this.reset();var a=e.eventId||this.ids;s(a).then((function(e){if(t.form=e.data,t.form.formFields)try{t.formFieldsList=JSON.parse(t.form.formFields)}catch(a){t.formFieldsList=[]}t.open=!0,t.title="修改项目指导活动"})).catch((function(e){console.error("获取指导活动详情失败:",e),t.$modal.msgError("获取指导活动详情失败")}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){t&&(e.form.formFields=JSON.stringify(e.formFieldsList),null!=e.form.eventId?d(e.form).then((function(){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})).catch((function(t){console.error("修改指导活动失败:",t),e.$modal.msgError("修改指导活动失败")})):c(e.form).then((function(){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})).catch((function(t){console.error("新增指导活动失败:",t),e.$modal.msgError("新增指导活动失败")})))}))},handleDelete:function(e){var t=this,a=e.eventId||this.ids;this.$modal.confirm('是否确认删除项目指导活动编号为"'+a+'"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/event/export",Object(r["a"])({},this.queryParams),"guidance_".concat((new Date).getTime(),".xlsx"))},addFormField:function(){this.formFieldsList.push({label:"",name:"",type:"input",required:!1,options:""})},removeFormField:function(e){this.formFieldsList.splice(e,1)},getFieldIcon:function(e){var t={input:"el-icon-edit",textarea:"el-icon-document",number:"el-icon-s-data",email:"el-icon-message",tel:"el-icon-phone",radio:"el-icon-circle-check",checkbox:"el-icon-check",select:"el-icon-arrow-down",date:"el-icon-date"};return t[e]||"el-icon-edit"},updateFieldName:function(e,t){var a={"姓名":"name","真实姓名":"real_name","联系电话":"phone","手机号":"mobile","电话":"phone","邮箱":"email","邮箱地址":"email","年龄":"age","性别":"gender","地址":"address","详细地址":"address","公司":"company","所在公司":"company","职位":"position","工作岗位":"position","备注":"remark","说明":"remark","身份证":"id_card","身份证号":"id_card","学校":"school","专业":"major","班级":"class","学号":"student_id","工作经验":"experience","工作年限":"work_years","紧急联系人":"emergency_contact","紧急联系人电话":"emergency_phone","个人简介":"introduction","自我介绍":"introduction","期望收获":"expectations","参与原因":"reason","特长技能":"skills","兴趣爱好":"hobbies","技术方向":"tech_direction","出生日期":"birthday","项目名称":"project_name","项目描述":"project_description","技术栈":"tech_stack","项目阶段":"project_stage","团队规模":"team_size","指导需求":"guidance_needs","期望指导方向":"guidance_direction","其他需求":"other_needs"};a[t]?e.name=a[t]:e.name=t.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g,"_").replace(/_{2,}/g,"_").replace(/^_|_$/g,"").toLowerCase()||"field_"+Date.now()},moveField:function(e,t){var a=e+t;if(a>=0&&a<this.formFieldsList.length){var i=this.formFieldsList.splice(e,1)[0];this.formFieldsList.splice(a,0,i)}},handleTemplateCommand:function(e){var t=this;if("clear"!==e){var a={basic:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!1,options:""}],tech:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!0,options:""},{label:"所在公司",name:"company",type:"input",required:!1,options:""},{label:"技术方向",name:"tech_direction",type:"select",required:!0,options:"前端开发,后端开发,移动开发,人工智能,数据分析"},{label:"工作年限",name:"experience",type:"radio",required:!0,options:"1年以内,1-3年,3-5年,5年以上"},{label:"期望收获",name:"expectations",type:"textarea",required:!1,options:""}],guidance:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!0,options:""},{label:"项目名称",name:"project_name",type:"input",required:!0,options:""},{label:"项目阶段",name:"project_stage",type:"radio",required:!0,options:"概念阶段,开发阶段,测试阶段,上线阶段"},{label:"团队规模",name:"team_size",type:"select",required:!0,options:"1人,2-3人,4-5人,6-10人,10人以上"},{label:"指导需求",name:"guidance_needs",type:"checkbox",required:!0,options:"技术指导,商业模式,融资建议,市场推广,团队管理"},{label:"项目描述",name:"project_description",type:"textarea",required:!0,options:""}]};a[e]&&(this.formFieldsList=a[e],this.$message.success("模板应用成功"))}else this.$confirm("确定要清空所有字段吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.formFieldsList=[],t.$message.success("已清空所有字段")}))},previewForm:function(){0!==this.formFieldsList.length?this.previewDialogVisible=!0:this.$message.warning("请先添加表单字段")}}},u=p,f=(a("0b8f"),a("2877")),h=Object(f["a"])(u,i,l,!1,null,"1ab421f9",null);t["default"]=h.exports}}]);