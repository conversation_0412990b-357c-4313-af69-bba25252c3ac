<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniUserFollowMapper">
    
    <resultMap type="MiniUserFollow" id="MiniUserFollowResult">
        <result property="followId"    column="follow_id"    />
        <result property="followerId"    column="follower_id"    />
        <result property="followedId"    column="followed_id"    />
        <result property="followTime"    column="follow_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="MiniUserFollow" id="MiniUserFollowWithUserInfoResult" extends="MiniUserFollowResult">
        <association property="followerInfo" javaType="com.ruoyi.miniapp.domain.MiniUserFollow$FollowerInfo">
            <result property="userId" column="follower_user_id"/>
            <result property="userName" column="follower_user_name"/>
            <result property="nickName" column="follower_nick_name"/>
            <result property="avatar" column="follower_avatar"/>
            <result property="portraitUrl" column="follower_portrait_url"/>
            <result property="realName" column="follower_real_name"/>
            <result property="currentCompany" column="follower_current_company"/>
            <result property="positionTitle" column="follower_position_title"/>
            <result property="graduateSchool" column="follower_graduate_school"/>
            <result property="major" column="follower_major"/>
            <result property="college" column="follower_college"/>
        </association>
        <association property="followedInfo" javaType="com.ruoyi.miniapp.domain.MiniUserFollow$FollowedInfo">
            <result property="userId" column="followed_user_id"/>
            <result property="userName" column="followed_user_name"/>
            <result property="nickName" column="followed_nick_name"/>
            <result property="avatar" column="followed_avatar"/>
            <result property="portraitUrl" column="followed_portrait_url"/>
            <result property="realName" column="followed_real_name"/>
            <result property="currentCompany" column="followed_current_company"/>
            <result property="positionTitle" column="followed_position_title"/>
            <result property="followerCount" column="followed_follower_count"/>
            <result property="isMutualFollow" column="is_mutual_follow"/>
            <result property="graduateSchool" column="followed_graduate_school"/>
            <result property="major" column="followed_major"/>
            <result property="college" column="followed_college"/>
        </association>
    </resultMap>

    <sql id="selectMiniUserFollowVo">
        select follow_id, follower_id, followed_id, follow_time, status, create_by, create_time, update_by, update_time from mini_user_follow
    </sql>

    <select id="selectMiniUserFollowList" parameterType="MiniUserFollow" resultMap="MiniUserFollowResult">
        <include refid="selectMiniUserFollowVo"/>
        <where>  
            <if test="followerId != null "> and follower_id = #{followerId}</if>
            <if test="followedId != null "> and followed_id = #{followedId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by follow_time desc
    </select>
    
    <select id="selectMiniUserFollowByFollowId" parameterType="Long" resultMap="MiniUserFollowResult">
        <include refid="selectMiniUserFollowVo"/>
        where follow_id = #{followId}
    </select>

    <select id="selectFollowRelation" resultMap="MiniUserFollowResult">
        <include refid="selectMiniUserFollowVo"/>
        where follower_id = #{followerId} and followed_id = #{followedId}
    </select>

    <select id="selectMyFollowingList" resultMap="MiniUserFollowWithUserInfoResult">
        select 
            f.follow_id, f.follower_id, f.followed_id, f.follow_time, f.status,
            f.create_by, f.create_time, f.update_by, f.update_time,
            u.user_id as followed_user_id,
            u.user_name as followed_user_name,
            u.nick_name as followed_nick_name,
            u.avatar as followed_avatar,
            u.portrait_url as followed_portrait_url,
            u.real_name as followed_real_name,
            u.graduate_school as followed_graduate_school,
            u.major as followed_major,
            u.college as followed_college,
            u.current_company as followed_current_company,
            u.position_title as followed_position_title,
            u.follower_count as followed_follower_count,
            CASE WHEN mf.follow_id IS NOT NULL THEN 1 ELSE 0 END as is_mutual_follow
        from mini_user_follow f
        left join sys_user u on f.followed_id = u.user_id
        left join mini_user_follow mf on f.followed_id = mf.follower_id and f.follower_id = mf.followed_id and mf.status = '0'
        where f.follower_id = #{followerId} and f.status = '0' and u.user_type = '01' and u.status = '0'
        order by f.follow_time desc
    </select>

    <select id="selectMyFollowersList" resultMap="MiniUserFollowWithUserInfoResult">
        select 
            f.follow_id, f.follower_id, f.followed_id, f.follow_time, f.status,
            f.create_by, f.create_time, f.update_by, f.update_time,
            u.user_id as follower_user_id,
            u.user_name as follower_user_name,
            u.nick_name as follower_nick_name,
            u.avatar as follower_avatar,
            u.portrait_url as follower_portrait_url,
            u.real_name as follower_real_name,
            u.graduate_school as follower_graduate_school,
            u.major as follower_major,
            u.college as follower_college,
            u.current_company as follower_current_company,
            u.position_title as follower_position_title,
            CASE WHEN mf.follow_id IS NOT NULL THEN 1 ELSE 0 END as is_mutual_follow
        from mini_user_follow f
        left join sys_user u on f.follower_id = u.user_id
        left join mini_user_follow mf on f.follower_id = mf.followed_id and f.followed_id = mf.follower_id and mf.status = '0'
        where f.followed_id = #{followedId} and f.status = '0' and u.user_type = '01' and u.status = '0'
        order by f.follow_time desc
    </select>

    <select id="countFollowingByUserId" resultType="int">
        select count(*) from mini_user_follow 
        where follower_id = #{userId} and status = '0'
    </select>

    <select id="countFollowersByUserId" resultType="int">
        select count(*) from mini_user_follow 
        where followed_id = #{userId} and status = '0'
    </select>

    <select id="checkMutualFollow" resultType="boolean">
        select count(*) = 2 from mini_user_follow 
        where ((follower_id = #{userId1} and followed_id = #{userId2}) 
               or (follower_id = #{userId2} and followed_id = #{userId1})) 
        and status = '0'
    </select>

    <select id="batchSelectFollowStatus" resultMap="MiniUserFollowResult">
        <include refid="selectMiniUserFollowVo"/>
        where follower_id = #{followerId} 
        and followed_id in
        <foreach item="followedId" collection="followedIds" open="(" separator="," close=")">
            #{followedId}
        </foreach>
    </select>
        
    <insert id="insertMiniUserFollow" parameterType="MiniUserFollow" useGeneratedKeys="true" keyProperty="followId">
        insert into mini_user_follow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="followerId != null">follower_id,</if>
            <if test="followedId != null">followed_id,</if>
            <if test="followTime != null">follow_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="followerId != null">#{followerId},</if>
            <if test="followedId != null">#{followedId},</if>
            <if test="followTime != null">#{followTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMiniUserFollow" parameterType="MiniUserFollow">
        update mini_user_follow
        <trim prefix="SET" suffixOverrides=",">
            <if test="followerId != null">follower_id = #{followerId},</if>
            <if test="followedId != null">followed_id = #{followedId},</if>
            <if test="followTime != null">follow_time = #{followTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where follow_id = #{followId}
    </update>

    <update id="updateFollowStatus">
        update mini_user_follow 
        set status = #{status}, update_time = now()
        where follower_id = #{followerId} and followed_id = #{followedId}
    </update>

    <delete id="deleteMiniUserFollowByFollowId" parameterType="Long">
        delete from mini_user_follow where follow_id = #{followId}
    </delete>

    <delete id="deleteMiniUserFollowByFollowIds" parameterType="String">
        delete from mini_user_follow where follow_id in 
        <foreach item="followId" collection="array" open="(" separator="," close=")">
            #{followId}
        </foreach>
    </delete>
</mapper>
