{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\docking\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\docking\\index.vue", "mtime": 1753633472180}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REb2NraW5nLCBkZWxEb2NraW5nIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9kb2NraW5nIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRG9ja2luZyIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDpnIDmsYLlr7nmjqXooajmoLzmlbDmja4KICAgICAgZG9ja2luZ0xpc3Q6IFtdLAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgZGVtYW5kVGl0bGU6IG51bGwsCiAgICAgICAgdXNlck5hbWU6IG51bGwsCiAgICAgICAgc3RhdHVzOiBudWxsLAogICAgICAgIGRvY2tpbmdUaW1lOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOivpuaDheW8ueeqlwogICAgICBkZXRhaWxEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g5b2T5YmN5p+l55yL55qE5a+55o6l6K6w5b2VCiAgICAgIGN1cnJlbnREb2NraW5nOiB7fSwKICAgICAgLy8g6IGU57O76K6w5b2V5by556qXCiAgICAgIGNvbnRhY3REaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g6IGU57O76K6w5b2V6KGo5Y2VCiAgICAgIGNvbnRhY3RGb3JtOiB7CiAgICAgICAgZG9ja2luZ0lkOiBudWxsLAogICAgICAgIGlzQ29udGFjdGVkOiAnMCcsCiAgICAgICAgY29udGFjdFJlc3VsdDogJycsCiAgICAgICAgY29udGFjdE5vdGVzOiAnJywKICAgICAgICBjb250YWN0VGltZTogJycKICAgICAgfSwKICAgICAgLy8g6IGU57O76K6w5b2V6KGo5Y2V6aqM6K+BCiAgICAgIGNvbnRhY3RSdWxlczogewogICAgICAgIGlzQ29udGFjdGVkOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5piv5ZCm5bey6IGU57O7IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LpnIDmsYLlr7nmjqXliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zID0ge307CiAgICAgIGlmIChudWxsICE9IHRoaXMuZGF0ZVJhbmdlICYmICcnICE9IHRoaXMuZGF0ZVJhbmdlKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXNbImJlZ2luRG9ja2luZ1RpbWUiXSA9IHRoaXMuZGF0ZVJhbmdlWzBdOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJlbmREb2NraW5nVGltZSJdID0gdGhpcy5kYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgbGlzdERvY2tpbmcodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kb2NraW5nTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIC8vIOiwg+ivle+8muaJk+WNsOesrOS4gOadoeaVsOaNruafpeeci2RlbWFuZFRpdGxl5a2X5q61CiAgICAgICAgaWYgKHJlc3BvbnNlLnJvd3MgJiYgcmVzcG9uc2Uucm93cy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygn56ys5LiA5p2h5a+55o6l6K6w5b2V5pWw5o2uOicsIHJlc3BvbnNlLnJvd3NbMF0pOwogICAgICAgICAgY29uc29sZS5sb2coJ+mcgOaxguagh+mimOWtl+autTonLCByZXNwb25zZS5yb3dzWzBdLmRlbWFuZFRpdGxlKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5kb2NraW5nSWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgCiAgICAvKiog5p+l55yL6K+m5oOFICovCiAgICBoYW5kbGVEZXRhaWwocm93KSB7CiAgICAgIHRoaXMuY3VycmVudERvY2tpbmcgPSByb3c7CiAgICAgIHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAoKICAgIC8qKiDogZTns7vorrDlvZUgKi8KICAgIGhhbmRsZUNvbnRhY3Qocm93KSB7CiAgICAgIHRoaXMuY29udGFjdEZvcm0gPSB7CiAgICAgICAgZG9ja2luZ0lkOiByb3cuZG9ja2luZ0lkLAogICAgICAgIGlzQ29udGFjdGVkOiByb3cuaXNDb250YWN0ZWQgfHwgJzAnLAogICAgICAgIGNvbnRhY3RSZXN1bHQ6IHJvdy5jb250YWN0UmVzdWx0IHx8ICcnLAogICAgICAgIGNvbnRhY3ROb3Rlczogcm93LmNvbnRhY3ROb3RlcyB8fCAnJywKICAgICAgICBjb250YWN0VGltZTogcm93LmNvbnRhY3RUaW1lIHx8ICcnCiAgICAgIH07CiAgICAgIHRoaXMuY29udGFjdERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKCiAgICAvKiog5o+Q5Lqk6IGU57O76K6w5b2V6KGo5Y2VICovCiAgICBzdWJtaXRDb250YWN0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siY29udGFjdEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDlpoLmnpzpgInmi6nlt7LogZTns7vkvYbmsqHmnInorr7nva7ogZTns7vml7bpl7TvvIzkvb/nlKjlvZPliY3ml7bpl7QKICAgICAgICAgIGlmICh0aGlzLmNvbnRhY3RGb3JtLmlzQ29udGFjdGVkID09PSAnMScgJiYgIXRoaXMuY29udGFjdEZvcm0uY29udGFjdFRpbWUpIHsKICAgICAgICAgICAgdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZSA9IHRoaXMucGFyc2VUaW1lKG5ldyBEYXRlKCksICd7eX0te219LXtkfSB7aH06e2l9OntzfScpOwogICAgICAgICAgfQoKICAgICAgICAgIHRoaXMuJGh0dHAucHV0KCcvbWluaWFwcC9kb2NraW5nL3VwZGF0ZUNvbnRhY3RTdGF0dXMnLCB0aGlzLmNvbnRhY3RGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6IGU57O76K6w5b2V5pu05paw5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5jb250YWN0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLmRhdGEubXNnIHx8ICLmm7TmlrDlpLHotKUiKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi572R57uc6ZSZ6K+vIik7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBkb2NraW5nSWRzID0gcm93LmRvY2tpbmdJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5a+55o6l6K6w5b2V57yW5Y+35Li6IicgKyBkb2NraW5nSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxEb2NraW5nKGRvY2tpbmdJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL2RvY2tpbmcvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGRvY2tpbmdfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9CiAgfQp9Owo="}, null]}