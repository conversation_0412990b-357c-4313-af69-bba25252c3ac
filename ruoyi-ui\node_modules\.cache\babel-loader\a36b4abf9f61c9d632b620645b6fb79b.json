{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753847579546}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKdmFyIF9wYXJrID0gcmVxdWlyZSgiQC9hcGkvbWluaWFwcC9wYXJrIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiTWluaVBhcmsiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5Zut5Yy6566h55CG6KGo5qC85pWw5o2uCiAgICAgIHBhcmtMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDlm63ljLrnroDku4vlm77niYflr7nor53moYYKICAgICAgaW50cm9JbWFnZU9wZW46IGZhbHNlLAogICAgICAvLyDlm63ljLrnroDku4vlm77niYfooajljZUKICAgICAgaW50cm9JbWFnZUZvcm06IHsKICAgICAgICBpbnRyb0ltYWdlVXJsOiAnJwogICAgICB9LAogICAgICAvLyDlm77niYfliqDovb3nirbmgIEKICAgICAgaW50cm9JbWFnZUxvYWRpbmc6IGZhbHNlLAogICAgICAvLyDlm77niYfpooTop4gKICAgICAgcHJldmlld1Zpc2libGU6IGZhbHNlLAogICAgICBwcmV2aWV3SW1hZ2VVcmw6ICcnLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYXJrTmFtZTogbnVsbCwKICAgICAgICBwYXJrQ29kZTogbnVsbCwKICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBwYXJrTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWbreWMuuWQjeensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBzb3J0T3JkZXI6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmjpLluo/kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Llm63ljLrnrqHnkIbliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAoMCwgX3BhcmsubGlzdFBhcmspKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMucGFya0xpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5oyJ6ZKuICovY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLyoqIOihqOWNlemHjee9riAqL3Jlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHBhcmtJZDogbnVsbCwKICAgICAgICBwYXJrTmFtZTogbnVsbCwKICAgICAgICBwYXJrQ29kZTogbnVsbCwKICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwKICAgICAgICBjb250ZW50OiBudWxsLAogICAgICAgIGNvdmVySW1hZ2U6IG51bGwsCiAgICAgICAgc29ydE9yZGVyOiAwLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnBhcmtJZDsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOWbreWMuueuoeeQhiI7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgcGFya0lkID0gcm93LnBhcmtJZCB8fCB0aGlzLmlkczsKICAgICAgKDAsIF9wYXJrLmdldFBhcmspKHBhcmtJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXMyLm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzMi50aXRsZSA9ICLkv67mlLnlm63ljLrnrqHnkIYiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczMuZm9ybS5wYXJrSWQgIT09IG51bGwgJiYgX3RoaXMzLmZvcm0ucGFya0lkICE9PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgKDAsIF9wYXJrLnVwZGF0ZVBhcmspKF90aGlzMy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICgwLCBfcGFyay5hZGRQYXJrKShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgcGFya0lkcyA9IHJvdy5wYXJrSWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOWbreWMuueuoeeQhue8luWPt+S4uiInICsgcGFya0lkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcGFyay5kZWxQYXJrKShwYXJrSWRzKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LmdldExpc3QoKTsKICAgICAgICBfdGhpczQuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqL2hhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Zut5Yy6566h55CG5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LmxvYWRpbmcgPSB0cnVlOwogICAgICAgIHJldHVybiAoMCwgX3BhcmsuZXhwb3J0UGFyaykoX3RoaXM1LnF1ZXJ5UGFyYW1zKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczUuJGRvd25sb2FkLmV4Y2VsKHJlc3BvbnNlLCAn5Zut5Yy6566h55CG5pWw5o2uLnhsc3gnKTsKICAgICAgICBfdGhpczUubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOaOkuW6j+S/ruaUuSAqL2hhbmRsZVNvcnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNvcnRDaGFuZ2Uocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICAoMCwgX3BhcmsudXBkYXRlUGFyaykocm93KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNi4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5L+u5pS55oiQ5YqfIik7CiAgICAgICAgX3RoaXM2LmdldExpc3QoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWbreWMuueugOS7i+WbvueJh+S4iuS8oCAqL2hhbmRsZUludHJvSW1hZ2VVcGxvYWQ6IGZ1bmN0aW9uIGhhbmRsZUludHJvSW1hZ2VVcGxvYWQoKSB7CiAgICAgIC8vIOWFiOmHjee9ruihqOWNleaVsOaNrgogICAgICB0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmwgPSAnJzsKICAgICAgdGhpcy5pbnRyb0ltYWdlT3BlbiA9IHRydWU7CiAgICAgIC8vIOWKoOi9veW9k+WJjeWbvueJh+aVsOaNrgogICAgICB0aGlzLmxvYWRJbnRyb0ltYWdlKCk7CiAgICB9LAogICAgLyoqIOWKoOi9veWbreWMuueugOS7i+WbvueJhyAqL2xvYWRJbnRyb0ltYWdlOiBmdW5jdGlvbiBsb2FkSW50cm9JbWFnZSgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vliqDovb3lm63ljLrnroDku4vlm77niYcuLi4nKTsKICAgICAgdGhpcy5pbnRyb0ltYWdlTG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfcGFyay5nZXRQYXJrSW50cm9JbWFnZSkoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5blm63ljLrnroDku4vlm77niYflk43lupQ6JywgcmVzcG9uc2UpOwogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIC8vIOS8mOWFiOivu+WPlmRhdGHlrZfmrrXvvIzlpoLmnpzmsqHmnInliJnor7vlj5Ztc2flrZfmrrXvvIjlkJHlkI7lhbzlrrnvvIkKICAgICAgICAgIF90aGlzNy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gcmVzcG9uc2UuZGF0YSB8fCByZXNwb25zZS5tc2cgfHwgJyc7CiAgICAgICAgICBjb25zb2xlLmxvZygn6K6+572u5Zu+54mHVVJMOicsIF90aGlzNy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS53YXJuKCfojrflj5blm63ljLrnroDku4vlm77niYflpLHotKU6JywgcmVzcG9uc2UubXNnKTsKICAgICAgICAgIF90aGlzNy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3lm63ljLrnroDku4vlm77niYflh7rplJk6JywgZXJyb3IpOwogICAgICAgIF90aGlzNy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7CiAgICAgICAgX3RoaXM3LiRtb2RhbC5tc2dFcnJvcigi5Yqg6L295Zut5Yy6566A5LuL5Zu+54mH5aSx6LSlIik7CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNy5pbnRyb0ltYWdlTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Y+W5raI5Zut5Yy6566A5LuL5Zu+54mHICovY2FuY2VsSW50cm9JbWFnZTogZnVuY3Rpb24gY2FuY2VsSW50cm9JbWFnZSgpIHsKICAgICAgdGhpcy5pbnRyb0ltYWdlT3BlbiA9IGZhbHNlOwogICAgICAvLyDkuI3opoHmuIXnqbrmlbDmja7vvIzkv53mjIHljp/mnInmlbDmja4KICAgICAgLy8gdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7CiAgICB9LAogICAgLyoqIOaPkOS6pOWbreWMuueugOS7i+WbvueJhyAqL3N1Ym1pdEludHJvSW1hZ2VGb3JtOiBmdW5jdGlvbiBzdWJtaXRJbnRyb0ltYWdlRm9ybSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgICgwLCBfcGFyay51cGRhdGVQYXJrSW50cm9JbWFnZSkodGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzOC4kbW9kYWwubXNnU3VjY2Vzcygi5Zut5Yy6566A5LuL5Zu+54mH5pu05paw5oiQ5YqfIik7CiAgICAgICAgX3RoaXM4LmludHJvSW1hZ2VPcGVuID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzOC4kbW9kYWwubXNnRXJyb3IoIuabtOaWsOWbreWMuueugOS7i+WbvueJh+Wksei0pSIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6aKE6KeI5Zut5Yy6566A5LuL5Zu+54mHICovcHJldmlld0ludHJvSW1hZ2U6IGZ1bmN0aW9uIHByZXZpZXdJbnRyb0ltYWdlKCkgewogICAgICBpZiAodGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsICYmIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybC50cmltKCkgIT09ICcnKSB7CiAgICAgICAgdGhpcy5wcmV2aWV3SW1hZ2VVcmwgPSB0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmw7CiAgICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IHRydWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi5pqC5peg5Zu+54mH5Y+v6aKE6KeIIik7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5YWz6Zet5Zu+54mH6aKE6KeIICovY2xvc2VQcmV2aWV3OiBmdW5jdGlvbiBjbG9zZVByZXZpZXcoKSB7CiAgICAgIHRoaXMucHJldmlld1Zpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5wcmV2aWV3SW1hZ2VVcmwgPSAnJzsKICAgIH0sCiAgICAvKiog5Zu+54mH5Yqg6L296ZSZ6K+v5aSE55CGICovaGFuZGxlSW1hZ2VFcnJvcjogZnVuY3Rpb24gaGFuZGxlSW1hZ2VFcnJvcihldmVudCkgewogICAgICBjb25zb2xlLmVycm9yKCflm77niYfliqDovb3lpLHotKU6JywgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKTsKICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWbvueJh+WKoOi9veWksei0pe+8jOivt+ajgOafpeWbvueJh+mTvuaOpeaYr+WQpuacieaViCIpOwogICAgICAvLyDlj6/ku6Xorr7nva7kuIDkuKrpu5jorqTnmoTplJnor6/lm77niYcKICAgICAgLy8gZXZlbnQudGFyZ2V0LnNyYyA9ICcvcGF0aC90by9kZWZhdWx0LWVycm9yLWltYWdlLnBuZyc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_park", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "parkList", "title", "open", "introImageOpen", "introImageForm", "introImageUrl", "introImageLoading", "previewVisible", "previewImageUrl", "queryParams", "pageNum", "pageSize", "parkName", "parkCode", "description", "status", "form", "rules", "required", "message", "trigger", "sortOrder", "created", "getList", "methods", "_this", "listPark", "then", "response", "rows", "cancel", "reset", "parkId", "content", "coverImage", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getPark", "submitForm", "_this3", "$refs", "validate", "valid", "undefined", "updatePark", "$modal", "msgSuccess", "addPark", "handleDelete", "_this4", "parkIds", "confirm", "delPark", "catch", "handleExport", "_this5", "exportPark", "$download", "excel", "handleSortChange", "_this6", "handleIntroImageUpload", "loadIntroImage", "_this7", "console", "log", "getParkIntroImage", "code", "msg", "warn", "error", "msgError", "finally", "cancelIntroImage", "submitIntroImageForm", "_this8", "updateParkIntroImage", "previewIntroImage", "trim", "msgWarning", "closePreview", "handleImageError", "event"], "sources": ["src/views/miniapp/park/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"园区名称\" prop=\"parkName\">\r\n        <el-input\r\n          v-model=\"queryParams.parkName\"\r\n          placeholder=\"请输入园区名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"园区编码\" prop=\"parkCode\">\r\n        <el-input\r\n          v-model=\"queryParams.parkCode\"\r\n          placeholder=\"请输入园区编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"园区简介\" prop=\"description\">\r\n        <el-input\r\n          v-model=\"queryParams.description\"\r\n          placeholder=\"请输入园区简介\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:park:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:park:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:park:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:park:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-picture\"\r\n          size=\"mini\"\r\n          @click=\"handleIntroImageUpload\"\r\n          v-hasPermi=\"['miniapp:park:edit']\"\r\n        >园区简介图片</el-button>\r\n      </el-col>\r\n\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"parkList\" \r\n      @selection-change=\"handleSelectionChange\"\r\n      row-key=\"parkId\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"园区ID\" align=\"center\" prop=\"parkId\" width=\"80\" />\r\n      <el-table-column label=\"园区名称\" align=\"center\" prop=\"parkName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"园区编码\" align=\"center\" prop=\"parkCode\" width=\"120\" />\r\n      <el-table-column label=\"园区简介\" align=\"center\" prop=\"description\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImage\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.coverImage\" :width=\"80\" :height=\"50\" v-if=\"scope.row.coverImage\"/>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input-number \r\n            v-model=\"scope.row.sortOrder\" \r\n            :min=\"0\" \r\n            size=\"mini\"\r\n            :controls=\"false\"\r\n            @change=\"handleSortChange(scope.row)\"\r\n            style=\"width: 70px;\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"160\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:park:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:park:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改园区管理对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"园区名称\" prop=\"parkName\">\r\n              <el-input v-model=\"form.parkName\" placeholder=\"请输入园区名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"园区编码\" prop=\"parkCode\">\r\n              <el-input v-model=\"form.parkCode\" placeholder=\"请输入园区编码\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"园区简介\" prop=\"description\">\r\n          <el-input v-model=\"form.description\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入园区简介\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"封面图片\" prop=\"coverImage\">\r\n          <image-upload v-model=\"form.coverImage\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"详细内容\" prop=\"content\">\r\n          <editor v-model=\"form.content\" :min-height=\"300\"/>\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 园区简介图片上传对话框 -->\r\n    <el-dialog title=\"园区简介图片管理\" :visible.sync=\"introImageOpen\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"introImageForm\" :model=\"introImageForm\" label-width=\"120px\">\r\n        <el-form-item label=\"当前简介图片\">\r\n          <!-- 加载状态 -->\r\n          <div v-if=\"introImageLoading\" style=\"padding: 20px; text-align: center;\">\r\n            <i class=\"el-icon-loading\" style=\"margin-right: 5px;\"></i>\r\n            <span>正在加载图片信息...</span>\r\n          </div>\r\n\r\n          <!-- 已设置图片 -->\r\n          <div v-else-if=\"introImageForm.introImageUrl && introImageForm.introImageUrl.trim() !== ''\" style=\"padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n              <div>\r\n                <i class=\"el-icon-success\" style=\"color: #67c23a; margin-right: 5px;\"></i>\r\n                <span style=\"color: #409eff;\">已设置园区简介图片</span>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"previewIntroImage\"\r\n              >\r\n                预览图片\r\n              </el-button>\r\n            </div>\r\n            <!-- 图片预览缩略图 -->\r\n            <div style=\"margin-top: 10px;\">\r\n              <img\r\n                :src=\"introImageForm.introImageUrl\"\r\n                style=\"width: 120px; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer; border: 1px solid #dcdfe6;\"\r\n                @click=\"previewIntroImage\"\r\n                @error=\"handleImageError\"\r\n                alt=\"园区简介图片缩略图\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 未设置图片 -->\r\n          <div v-else style=\"padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-warning\" style=\"color: #e6a23c; margin-right: 5px;\"></i>\r\n            <span style=\"color: #e6a23c;\">暂未设置园区简介图片</span>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传新图片\">\r\n          <image-upload v-model=\"introImageForm.introImageUrl\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitIntroImageForm\">确 定</el-button>\r\n        <el-button @click=\"cancelIntroImage\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      title=\"园区简介图片预览\"\r\n      :visible.sync=\"previewVisible\"\r\n      width=\"800px\"\r\n      append-to-body\r\n      :before-close=\"closePreview\"\r\n    >\r\n      <div class=\"image-preview-container\">\r\n        <img\r\n          :src=\"previewImageUrl\"\r\n          class=\"preview-image\"\r\n          alt=\"园区简介图片\"\r\n        />\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closePreview\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"window.open(previewImageUrl, '_blank')\">\r\n          <i class=\"el-icon-zoom-in\"></i> 原始大小查看\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPark, getPark, delPark, addPark, updatePark, exportPark, getParkIntroImage, updateParkIntroImage } from \"@/api/miniapp/park\";\r\n\r\nexport default {\r\n  name: \"MiniPark\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 园区管理表格数据\r\n      parkList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 园区简介图片对话框\r\n      introImageOpen: false,\r\n      // 园区简介图片表单\r\n      introImageForm: {\r\n        introImageUrl: ''\r\n      },\r\n      // 图片加载状态\r\n      introImageLoading: false,\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        parkName: null,\r\n        parkCode: null,\r\n        description: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parkName: [\r\n          { required: true, message: \"园区名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sortOrder: [\r\n          { required: true, message: \"排序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询园区管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPark(this.queryParams).then(response => {\r\n        this.parkList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        parkId: null,\r\n        parkName: null,\r\n        parkCode: null,\r\n        description: null,\r\n        content: null,\r\n        coverImage: null,\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.parkId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加园区管理\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const parkId = row.parkId || this.ids;\r\n      getPark(parkId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改园区管理\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          if (this.form.parkId !== null && this.form.parkId !== undefined) {\r\n            updatePark(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPark(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const parkIds = row.parkId || this.ids;\r\n      this.$modal.confirm('是否确认删除园区管理编号为\"' + parkIds + '\"的数据项？').then(function() {\r\n        return delPark(parkIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有园区管理数据项？').then(() => {\r\n        this.loading = true;\r\n        return exportPark(this.queryParams);\r\n      }).then(response => {\r\n        this.$download.excel(response, '园区管理数据.xlsx');\r\n        this.loading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 排序修改 */\r\n    handleSortChange(row) {\r\n      updatePark(row).then(response => {\r\n        this.$modal.msgSuccess(\"排序修改成功\");\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 园区简介图片上传 */\r\n    handleIntroImageUpload() {\r\n      // 先重置表单数据\r\n      this.introImageForm.introImageUrl = '';\r\n      this.introImageOpen = true;\r\n      // 加载当前图片数据\r\n      this.loadIntroImage();\r\n    },\r\n    /** 加载园区简介图片 */\r\n    loadIntroImage() {\r\n      console.log('开始加载园区简介图片...');\r\n      this.introImageLoading = true;\r\n\r\n      getParkIntroImage().then(response => {\r\n        console.log('获取园区简介图片响应:', response);\r\n        if (response.code === 200) {\r\n          // 优先读取data字段，如果没有则读取msg字段（向后兼容）\r\n          this.introImageForm.introImageUrl = response.data || response.msg || '';\r\n          console.log('设置图片URL:', this.introImageForm.introImageUrl);\r\n        } else {\r\n          console.warn('获取园区简介图片失败:', response.msg);\r\n          this.introImageForm.introImageUrl = '';\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载园区简介图片出错:', error);\r\n        this.introImageForm.introImageUrl = '';\r\n        this.$modal.msgError(\"加载园区简介图片失败\");\r\n      }).finally(() => {\r\n        this.introImageLoading = false;\r\n      });\r\n    },\r\n    /** 取消园区简介图片 */\r\n    cancelIntroImage() {\r\n      this.introImageOpen = false;\r\n      // 不要清空数据，保持原有数据\r\n      // this.introImageForm.introImageUrl = '';\r\n    },\r\n    /** 提交园区简介图片 */\r\n    submitIntroImageForm() {\r\n      updateParkIntroImage(this.introImageForm.introImageUrl).then(response => {\r\n        this.$modal.msgSuccess(\"园区简介图片更新成功\");\r\n        this.introImageOpen = false;\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"更新园区简介图片失败\");\r\n      });\r\n    },\r\n    /** 预览园区简介图片 */\r\n    previewIntroImage() {\r\n      if (this.introImageForm.introImageUrl && this.introImageForm.introImageUrl.trim() !== '') {\r\n        this.previewImageUrl = this.introImageForm.introImageUrl;\r\n        this.previewVisible = true;\r\n      } else {\r\n        this.$modal.msgWarning(\"暂无图片可预览\");\r\n      }\r\n    },\r\n    /** 关闭图片预览 */\r\n    closePreview() {\r\n      this.previewVisible = false;\r\n      this.previewImageUrl = '';\r\n    },\r\n    /** 图片加载错误处理 */\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', this.introImageForm.introImageUrl);\r\n      this.$modal.msgError(\"图片加载失败，请检查图片链接是否有效\");\r\n      // 可以设置一个默认的错误图片\r\n      // event.target.src = '/path/to/default-error-image.png';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 图片预览样式 */\r\n.image-preview-container {\r\n  text-align: center;\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 500px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.preview-image:hover {\r\n  transform: scale(1.02);\r\n}\r\n\r\n/* 缩略图悬停效果 */\r\n.image-preview-container img[alt=\"园区简介图片缩略图\"]:hover {\r\n  opacity: 0.8;\r\n  transform: scale(1.05);\r\n  transition: all 0.3s ease;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AA6SA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,cAAA;QACAC,aAAA;MACA;MACA;MACAC,iBAAA;MACA;MACAC,cAAA;MACAC,eAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,SAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA/B,OAAA;MACA,IAAAgC,cAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAzB,QAAA,GAAA4B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA1B,KAAA,GAAA6B,QAAA,CAAA7B,KAAA;QACA0B,KAAA,CAAA/B,OAAA;MACA;IACA;IACA,WACAoC,MAAA,WAAAA,OAAA;MACA,KAAA5B,IAAA;MACA,KAAA6B,KAAA;IACA;IACA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAAf,IAAA;QACAgB,MAAA;QACApB,QAAA;QACAC,QAAA;QACAC,WAAA;QACAmB,OAAA;QACAC,UAAA;QACAb,SAAA;QACAN,MAAA;QACAoB,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5B,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAe,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,cACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,GAAA,GAAA6C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,MAAA;MAAA;MACA,KAAApC,MAAA,GAAA4C,SAAA,CAAAG,MAAA;MACA,KAAA9C,QAAA,IAAA2C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAA7B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA4C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA;MACA,IAAAC,MAAA,GAAAc,GAAA,CAAAd,MAAA,SAAArC,GAAA;MACA,IAAAqD,aAAA,EAAAhB,MAAA,EAAAL,IAAA,WAAAC,QAAA;QACAmB,MAAA,CAAA/B,IAAA,GAAAY,QAAA,CAAAnC,IAAA;QACAsD,MAAA,CAAA7C,IAAA;QACA6C,MAAA,CAAA9C,KAAA;MACA;IACA;IACA,WACAgD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAnC,IAAA,CAAAoC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAlC,IAAA,CAAAgB,MAAA,aAAAkB,MAAA,CAAAlC,IAAA,CAAAgB,MAAA,KAAAsB,SAAA;YACA,IAAAC,gBAAA,EAAAL,MAAA,CAAAlC,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAsB,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAA3B,OAAA;YACA;UACA;YACA,IAAAmC,aAAA,EAAAR,MAAA,CAAAlC,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAsB,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAA3B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAoC,YAAA,WAAAA,aAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,OAAA,GAAAf,GAAA,CAAAd,MAAA,SAAArC,GAAA;MACA,KAAA6D,MAAA,CAAAM,OAAA,oBAAAD,OAAA,aAAAlC,IAAA;QACA,WAAAoC,aAAA,EAAAF,OAAA;MACA,GAAAlC,IAAA;QACAiC,MAAA,CAAArC,OAAA;QACAqC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,MAAA,CAAAM,OAAA,qBAAAnC,IAAA;QACAuC,MAAA,CAAAxE,OAAA;QACA,WAAAyE,gBAAA,EAAAD,MAAA,CAAAzD,WAAA;MACA,GAAAkB,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAAE,SAAA,CAAAC,KAAA,CAAAzC,QAAA;QACAsC,MAAA,CAAAxE,OAAA;MACA,GAAAsE,KAAA;IACA;IACA,WACAM,gBAAA,WAAAA,iBAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAAhB,gBAAA,EAAAT,GAAA,EAAAnB,IAAA,WAAAC,QAAA;QACA2C,MAAA,CAAAf,MAAA,CAAAC,UAAA;QACAc,MAAA,CAAAhD,OAAA;MACA;IACA;IACA,eACAiD,sBAAA,WAAAA,uBAAA;MACA;MACA,KAAApE,cAAA,CAAAC,aAAA;MACA,KAAAF,cAAA;MACA;MACA,KAAAsE,cAAA;IACA;IACA,eACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAtE,iBAAA;MAEA,IAAAuE,uBAAA,IAAAlD,IAAA,WAAAC,QAAA;QACA+C,OAAA,CAAAC,GAAA,gBAAAhD,QAAA;QACA,IAAAA,QAAA,CAAAkD,IAAA;UACA;UACAJ,MAAA,CAAAtE,cAAA,CAAAC,aAAA,GAAAuB,QAAA,CAAAnC,IAAA,IAAAmC,QAAA,CAAAmD,GAAA;UACAJ,OAAA,CAAAC,GAAA,aAAAF,MAAA,CAAAtE,cAAA,CAAAC,aAAA;QACA;UACAsE,OAAA,CAAAK,IAAA,gBAAApD,QAAA,CAAAmD,GAAA;UACAL,MAAA,CAAAtE,cAAA,CAAAC,aAAA;QACA;MACA,GAAA2D,KAAA,WAAAiB,KAAA;QACAN,OAAA,CAAAM,KAAA,gBAAAA,KAAA;QACAP,MAAA,CAAAtE,cAAA,CAAAC,aAAA;QACAqE,MAAA,CAAAlB,MAAA,CAAA0B,QAAA;MACA,GAAAC,OAAA;QACAT,MAAA,CAAApE,iBAAA;MACA;IACA;IACA,eACA8E,gBAAA,WAAAA,iBAAA;MACA,KAAAjF,cAAA;MACA;MACA;IACA;IACA,eACAkF,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,0BAAA,OAAAnF,cAAA,CAAAC,aAAA,EAAAsB,IAAA,WAAAC,QAAA;QACA0D,MAAA,CAAA9B,MAAA,CAAAC,UAAA;QACA6B,MAAA,CAAAnF,cAAA;MACA,GAAA6D,KAAA,WAAAiB,KAAA;QACAK,MAAA,CAAA9B,MAAA,CAAA0B,QAAA;MACA;IACA;IACA,eACAM,iBAAA,WAAAA,kBAAA;MACA,SAAApF,cAAA,CAAAC,aAAA,SAAAD,cAAA,CAAAC,aAAA,CAAAoF,IAAA;QACA,KAAAjF,eAAA,QAAAJ,cAAA,CAAAC,aAAA;QACA,KAAAE,cAAA;MACA;QACA,KAAAiD,MAAA,CAAAkC,UAAA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAApF,cAAA;MACA,KAAAC,eAAA;IACA;IACA,eACAoF,gBAAA,WAAAA,iBAAAC,KAAA;MACAlB,OAAA,CAAAM,KAAA,iBAAA7E,cAAA,CAAAC,aAAA;MACA,KAAAmD,MAAA,CAAA0B,QAAA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}