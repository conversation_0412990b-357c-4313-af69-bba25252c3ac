{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=style&index=2&id=369868a7&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753760187026}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5keW5hbWljLWZpZWxkcy1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMTVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTZlNmU2Ow0KfQ0KDQoubW9kdWxlLWdyb3VwIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLm1vZHVsZS10aXRsZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDllZmY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoubW9kdWxlLXRpdGxlIGkgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi8qIOWIhuexu+e7hOagt+W8jyAqLw0KLmNhdGVnb3J5LWdyb3VwIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmNhdGVnb3J5LXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOWVmZjsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5jYXRlZ29yeS10aXRsZSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGNvbG9yOiAjNDA5ZWZmOw0KfQ0KDQouY2F0ZWdvcnktZGVzY3JpcHRpb24gew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBjb2xvcjogIzY2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KfQ0KDQovKiDliqjmgIHlrZfmrrXlnoLnm7TluIPlsYDmoLflvI8gKi8NCi5keW5hbWljLWZpZWxkLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmR5bmFtaWMtZmllbGQtaXRlbTpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsgLyog5L+d5oyB5bqV6YOo6Ze06Led77yM6YG/5YWN5LiO5LiL5pa55YWD57Sg6YeN5ZCIICovDQp9DQoNCi8qIOWtl+auteagh+etvuagt+W8jyAqLw0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtbGFiZWwgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi8qIOW/heWhq+Wtl+auteagh+ivhiAqLw0KLmR5bmFtaWMtZmllbGQtaXRlbSAucmVxdWlyZWQtbWFyayB7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCn0NCg0KLyog5a2X5q615YaF5a655Yy65Z+fICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi8qIOihqOWNleaOp+S7tuagt+W8jyAqLw0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtY29udGVudCAuZWwtaW5wdXQsDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IC5lbC10ZXh0YXJlYSwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLXNlbGVjdCwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLWlucHV0LW51bWJlciwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLWRhdGUtZWRpdG9yLA0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtY29udGVudCAuZWwtdGltZS1waWNrZXIgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog5Y2V6YCJ5qGG5ZKM5aSa6YCJ5qGG5biD5bGAICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IC5lbC1yYWRpby1ncm91cCwNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLWNoZWNrYm94LWdyb3VwIHsNCiAgd2lkdGg6IDEwMCU7DQogIGxpbmUtaGVpZ2h0OiAxLjg7DQp9DQoNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmZpZWxkLWNvbnRlbnQgLmVsLXJhZGlvLA0KLmR5bmFtaWMtZmllbGQtaXRlbSAuZmllbGQtY29udGVudCAuZWwtY2hlY2tib3ggew0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCn0NCg0KLyog5paH5Lu25LiK5Lyg57uE5Lu2ICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5maWVsZC1jb250ZW50IC5lbC11cGxvYWQgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmVsLWRpdmlkZXIgew0KICBtYXJnaW46IDEwcHggMCAyMHB4IDA7DQp9DQoNCi8qIOWTjeW6lOW8j+W4g+WxgOS8mOWMliAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5keW5hbWljLWZpZWxkLWl0ZW0gLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICAgIHdpZHRoOiAxMDBweCAhaW1wb3J0YW50Ow0KICAgIHRleHQtYWxpZ246IGxlZnQ7DQogIH0NCg0KICAuZHluYW1pYy1maWVsZC1pdGVtIC5lbC1yYWRpbywNCiAgLmR5bmFtaWMtZmllbGQtaXRlbSAuZWwtY2hlY2tib3ggew0KICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIH0NCn0NCg0KLyog5LiK5Lyg57uE5Lu25qC35byP5LyY5YyWICovDQouZWwtdXBsb2FkX190aXAgew0KICBjb2xvcjogIzk5OTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tdG9wOiA1cHg7DQp9DQoNCi5keW5hbWljLWZpZWxkLWl0ZW0gLmVsLXVwbG9hZCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouZHluYW1pYy1maWVsZC1pdGVtIC5lbC11cGxvYWQtbGlzdCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoNCi8qIOaWh+S7tuS4iuS8oOebuOWFs+agt+W8jyAqLw0KLnVwbG9hZGVkLWZpbGVzLWxpc3Qgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KICBwYWRkaW5nOiAxMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi51cGxvYWRlZC1maWxlcy10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi51cGxvYWRlZC1maWxlLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA1cHggMDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi51cGxvYWRlZC1maWxlLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi51cGxvYWRlZC1maWxlLWl0ZW0gaSB7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBjb2xvcjogIzQwOWVmZjsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouZmlsZS1saW5rIHsNCiAgZmxleDogMTsNCiAgY29sb3I6ICM0MDllZmY7DQogIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5maWxlLWxpbms6aG92ZXIgew0KICBjb2xvcjogIzY2YjFmZjsNCiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7DQp9DQoNCi5yZW1vdmUtZmlsZS1idG4gew0KICBtYXJnaW4tbGVmdDogMTBweDsNCiAgY29sb3I6ICNmNTZjNmM7DQp9DQoNCi5yZW1vdmUtZmlsZS1idG46aG92ZXIgew0KICBjb2xvcjogI2Y3ODk4OTsNCn0NCg0KLyog6Z2Z5oCB5YaF5a655qC35byPICovDQouc3RhdGljLWNvbnRlbnQgew0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KfQ0KDQovKiDlt7LlrZjlnKjmlofku7bmmL7npLrmoLflvI8gKi8NCi5leGlzdGluZy1maWxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmZpbGUtZGlzcGxheSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOw0KICBib3JkZXI6IDFweCBzb2xpZCAjYjNkOGZmOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGdhcDogOHB4Ow0KfQ0KDQouZmlsZS1kaXNwbGF5IC5lbC1pY29uLWRvY3VtZW50IHsNCiAgY29sb3I6ICM0MDllZmY7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmZpbGUtZGlzcGxheSAuZmlsZS1saW5rIHsNCiAgZmxleDogMTsNCiAgY29sb3I6ICM0MDllZmY7DQogIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouZmlsZS1kaXNwbGF5IC5maWxlLWxpbms6aG92ZXIgew0KICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsNCn0NCg0KLmZpbGUtZGlzcGxheSAucmVtb3ZlLWZpbGUtYnRuIHsNCiAgY29sb3I6ICNmNTZjNmM7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi8qIOWKqOaAgeWtl+auteaVtOS9k+W4g+WxgOS8mOWMliAqLw0KLmR5bmFtaWMtZmllbGRzLXNlY3Rpb24gLmVsLXJvdyB7DQogIG1hcmdpbi1sZWZ0OiAtMTBweDsNCiAgbWFyZ2luLXJpZ2h0OiAtMTBweDsNCn0NCg0KLmR5bmFtaWMtZmllbGRzLXNlY3Rpb24gLmVsLWNvbCB7DQogIHBhZGRpbmctbGVmdDogMTBweDsNCiAgcGFkZGluZy1yaWdodDogMTBweDsNCn0NCg0KLyog5LyY5YyW6KGo5Y2V6aqM6K+B6ZSZ6K+v5o+Q56S655qE5pi+56S6ICovDQouZHluYW1pYy1maWVsZC1pdGVtIC5lbC1mb3JtLWl0ZW1fX2Vycm9yIHsNCiAgcG9zaXRpb246IHN0YXRpYzsNCiAgbWFyZ2luLXRvcDogMnB4Ow0KICBwYWRkaW5nLXRvcDogMnB4Ow0KfQ0KDQovKiDlr7nmjqXmg4XlhrXmmL7npLrmoLflvI8gKi8NCi5kb2NraW5nLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDRweDsNCn0NCg0KLmNvbnRhY3Qtc3RhdHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouY29udGFjdGVkIHsNCiAgY29sb3I6ICM2N2MyM2E7DQp9DQoNCi51bmNvbnRhY3RlZCB7DQogIGNvbG9yOiAjZTZhMjNjOw0KfQ0KDQovKiDlsZXlvIDlhoXlrrnmoLflvI8gKi8NCi5leHBhbmQtY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luOiAxMHB4IDA7DQp9DQoNCi5leHBhbmQtY29udGVudCBoNCB7DQogIG1hcmdpbjogMCAwIDE1cHggMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLm5vLWRvY2tpbmcgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOWvueaOpeivpuaDheihqOagvOagt+W8jyAqLw0KLmV4cGFuZC1jb250ZW50IC5lbC10YWJsZSB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLmV4cGFuZC1jb250ZW50IC5lbC10YWJsZSB0aCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQp9DQo="}, null]}