{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index_v1.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index_v1.vue", "mtime": 1753943808698}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFuZWxHcm91cCBmcm9tICcuL2Rhc2hib2FyZC9QYW5lbEdyb3VwJw0KaW1wb3J0IExpbmVDaGFydCBmcm9tICcuL2Rhc2hib2FyZC9MaW5lQ2hhcnQnDQppbXBvcnQgUmFkZGFyQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvUmFkZGFyQ2hhcnQnDQppbXBvcnQgUGllQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvUGllQ2hhcnQnDQppbXBvcnQgQmFyQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvQmFyQ2hhcnQnDQoNCmNvbnN0IGxpbmVDaGFydERhdGEgPSB7DQogIG5ld1Zpc2l0aXM6IHsNCiAgICBleHBlY3RlZERhdGE6IFsxMDAsIDEyMCwgMTYxLCAxMzQsIDEwNSwgMTYwLCAxNjVdLA0KICAgIGFjdHVhbERhdGE6IFsxMjAsIDgyLCA5MSwgMTU0LCAxNjIsIDE0MCwgMTQ1XQ0KICB9LA0KICBtZXNzYWdlczogew0KICAgIGV4cGVjdGVkRGF0YTogWzIwMCwgMTkyLCAxMjAsIDE0NCwgMTYwLCAxMzAsIDE0MF0sDQogICAgYWN0dWFsRGF0YTogWzE4MCwgMTYwLCAxNTEsIDEwNiwgMTQ1LCAxNTAsIDEzMF0NCiAgfSwNCiAgcHVyY2hhc2VzOiB7DQogICAgZXhwZWN0ZWREYXRhOiBbODAsIDEwMCwgMTIxLCAxMDQsIDEwNSwgOTAsIDEwMF0sDQogICAgYWN0dWFsRGF0YTogWzEyMCwgOTAsIDEwMCwgMTM4LCAxNDIsIDEzMCwgMTMwXQ0KICB9LA0KICBzaG9wcGluZ3M6IHsNCiAgICBleHBlY3RlZERhdGE6IFsxMzAsIDE0MCwgMTQxLCAxNDIsIDE0NSwgMTUwLCAxNjBdLA0KICAgIGFjdHVhbERhdGE6IFsxMjAsIDgyLCA5MSwgMTU0LCAxNjIsIDE0MCwgMTMwXQ0KICB9DQp9DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0luZGV4JywNCiAgY29tcG9uZW50czogew0KICAgIFBhbmVsR3JvdXAsDQogICAgTGluZUNoYXJ0LA0KICAgIFJhZGRhckNoYXJ0LA0KICAgIFBpZUNoYXJ0LA0KICAgIEJhckNoYXJ0DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxpbmVDaGFydERhdGE6IGxpbmVDaGFydERhdGEubmV3VmlzaXRpcw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZVNldExpbmVDaGFydERhdGEodHlwZSkgew0KICAgICAgdGhpcy5saW5lQ2hhcnREYXRhID0gbGluZUNoYXJ0RGF0YVt0eXBlXQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index_v1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index_v1.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n\r\n    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />\r\n\r\n    <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\r\n      <line-chart :chart-data=\"lineChartData\" />\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"32\">\r\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\r\n        <div class=\"chart-wrapper\">\r\n          <raddar-chart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\r\n        <div class=\"chart-wrapper\">\r\n          <pie-chart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\r\n        <div class=\"chart-wrapper\">\r\n          <bar-chart />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PanelGroup from './dashboard/PanelGroup'\r\nimport LineChart from './dashboard/LineChart'\r\nimport RaddarChart from './dashboard/Raddar<PERSON>hart'\r\nimport PieChart from './dashboard/PieChart'\r\nimport BarChart from './dashboard/BarChart'\r\n\r\nconst lineChartData = {\r\n  newVisitis: {\r\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\r\n    actualData: [120, 82, 91, 154, 162, 140, 145]\r\n  },\r\n  messages: {\r\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\r\n    actualData: [180, 160, 151, 106, 145, 150, 130]\r\n  },\r\n  purchases: {\r\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\r\n    actualData: [120, 90, 100, 138, 142, 130, 130]\r\n  },\r\n  shoppings: {\r\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\r\n    actualData: [120, 82, 91, 154, 162, 140, 130]\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Index',\r\n  components: {\r\n    PanelGroup,\r\n    LineChart,\r\n    RaddarChart,\r\n    PieChart,\r\n    BarChart\r\n  },\r\n  data() {\r\n    return {\r\n      lineChartData: lineChartData.newVisitis\r\n    }\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.lineChartData = lineChartData[type]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 32px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n</style>\r\n"]}]}