{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue", "mtime": 1753846134162}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVtYW5kQ2F0ZWdvcnksIGdldERlbWFuZENhdGVnb3J5LCBkZWxEZW1hbmRDYXRlZ29yeSwgYWRkRGVtYW5kQ2F0ZWdvcnksIHVwZGF0ZURlbWFuZENhdGVnb3J5IH0gZnJvbSAiQC9hcGkvbWluaWFwcC9kZW1hbmRjYXRlZ29yeSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk1pbmlEZW1hbmRDYXRlZ29yeSIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6ZyA5rGC57G75Z6L6KGo5qC85pWw5o2uDQogICAgICBjYXRlZ29yeUxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNhdGVnb3J5TmFtZTogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY2F0ZWdvcnlOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuexu+Wei+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGNhdGVnb3J5Q29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnsbvlnovmoIfor4bkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHBhdHRlcm46IC9eW2EtekEtWl1bYS16QS1aMC05X10qJC8sIG1lc3NhZ2U6ICLnsbvlnovmoIfor4blv4Xpobvku6XlrZfmr43lvIDlpLTvvIzlj6rog73ljIXlkKvlrZfmr43jgIHmlbDlrZflkozkuIvliJLnur8iLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzdGF0dXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi54q25oCB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V6YWN572u55u45YWzDQogICAgICBmb3JtQ29uZmlnT3BlbjogZmFsc2UsDQogICAgICBwcmV2aWV3T3BlbjogZmFsc2UsDQogICAgICBjdXJyZW50Q2F0ZWdvcnlJZDogbnVsbCwNCiAgICAgIGZvcm1GaWVsZHNMaXN0OiBbXSwgLy8g5L+d55WZ5YW85a655oCnDQogICAgICBmb3JtTW9kdWxlc0xpc3Q6IFtdLCAvLyDmlrDnmoTmqKHlnZfljJbnu5PmnoQNCiAgICAgIHNlbGVjdGVkVGVtcGxhdGU6ICcnLA0KICAgICAgcHJldmlld0RhdGE6IHt9LCAvLyDpooTop4jljLrln5/nmoTooajljZXmlbDmja4NCiAgICAgIHByZXZpZXdEaWFsb2dEYXRhOiB7fSwgLy8g6aKE6KeI5a+56K+d5qGG55qE6KGo5Y2V5pWw5o2uDQogICAgICAvLyDlm77moIfnm7jlhbMNCiAgICAgIGljb25VcGxvYWRlck9wZW46IGZhbHNlLA0KICAgICAgY3VycmVudE1vZHVsZUluZGV4OiAtMSwNCiAgICAgIHVwbG9hZGVkSWNvbjogJycNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQoNCiAgICAvKiog5p+l6K+i6ZyA5rGC57G75Z6L5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0RGVtYW5kQ2F0ZWdvcnkodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2F0ZWdvcnlMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCg0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGNhdGVnb3J5SWQ6IG51bGwsDQogICAgICAgIGNhdGVnb3J5TmFtZTogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlDb2RlOiBudWxsLA0KICAgICAgICBjYXRlZ29yeVNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlJY29uOiBudWxsLA0KICAgICAgICBjYXRlZ29yeURlc2M6IG51bGwsDQogICAgICAgIHNvcnRPcmRlcjogMCwNCiAgICAgICAgc3RhdHVzOiAiMCIsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY2F0ZWdvcnlJZCk7DQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6ZyA5rGC57G75Z6LIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBjYXRlZ29yeUlkID0gcm93LmNhdGVnb3J5SWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXREZW1hbmRDYXRlZ29yeShjYXRlZ29yeUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnpnIDmsYLnsbvlnosiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY2F0ZWdvcnlJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZW1hbmRDYXRlZ29yeSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERlbWFuZENhdGVnb3J5KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5SWRzID0gcm93LmNhdGVnb3J5SWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpnIDmsYLnsbvlnovnvJblj7fkuLoiJyArIGNhdGVnb3J5SWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsRGVtYW5kQ2F0ZWdvcnkoY2F0ZWdvcnlJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL2RlbWFuZGNhdGVnb3J5L2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYOmcgOaxguexu+Wei+aVsOaNrl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDmjpLluo/kv67mlLkgKi8NCiAgICBoYW5kbGVTb3J0Q2hhbmdlKHJvdykgew0KICAgICAgdXBkYXRlRGVtYW5kQ2F0ZWdvcnkocm93KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5L+u5pS55oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDooajljZXphY3nva7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVGb3JtQ29uZmlnKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Q2F0ZWdvcnlJZCA9IHJvdy5jYXRlZ29yeUlkOw0KICAgICAgdGhpcy5mb3JtRmllbGRzTGlzdCA9IFtdOw0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbXTsNCg0KICAgICAgLy8g5aaC5p6c5bey5pyJ6YWN572u77yM6Kej5p6QSlNPTg0KICAgICAgaWYgKHJvdy5mb3JtRmllbGRzKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcGFyc2VkRGF0YSA9IEpTT04ucGFyc2Uocm93LmZvcm1GaWVsZHMpOw0KDQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65paw55qE5qih5Z2X5YyW57uT5p6EDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkRGF0YSkgJiYgcGFyc2VkRGF0YS5sZW5ndGggPiAwICYmIHBhcnNlZERhdGFbMF0uaGFzT3duUHJvcGVydHkoJ25hbWUnKSkgew0KICAgICAgICAgICAgLy8g5paw55qE5qih5Z2X5YyW57uT5p6EDQogICAgICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdCA9IHBhcnNlZERhdGE7DQogICAgICAgICAgICAvLyDnoa7kv53miYDmnInlrZfmrrXpg73mnIkgaGlkZGVuIOWxnuaApw0KICAgICAgICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QuZm9yRWFjaChtb2R1bGUgPT4gew0KICAgICAgICAgICAgICBpZiAobW9kdWxlLmZpZWxkcykgew0KICAgICAgICAgICAgICAgIG1vZHVsZS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAoZmllbGQuaGlkZGVuID09PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KGZpZWxkLCAnaGlkZGVuJywgZmFsc2UpOw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkRGF0YSkpIHsNCiAgICAgICAgICAgIC8vIOaXp+eahOWtl+auteWIl+ihqOe7k+aehO+8jOi9rOaNouS4uuaooeWdl+WMlue7k+aehA0KICAgICAgICAgICAgdGhpcy5mb3JtRmllbGRzTGlzdCA9IHBhcnNlZERhdGE7DQogICAgICAgICAgICBpZiAocGFyc2VkRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIC8vIOS4uuaXp+Wtl+autea3u+WKoCBoaWRkZW4g5bGe5oCnDQogICAgICAgICAgICAgIHBhcnNlZERhdGEuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGZpZWxkLmhpZGRlbiA9PT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRzZXQoZmllbGQsICdoaWRkZW4nLCBmYWxzZSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbew0KICAgICAgICAgICAgICAgIG5hbWU6ICfln7rnoYDkv6Hmga8nLA0KICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnJywNCiAgICAgICAgICAgICAgICBmaWVsZHM6IHBhcnNlZERhdGENCiAgICAgICAgICAgICAgfV07DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6KGo5Y2V6YWN572u5aSx6LSlOicsIGUpOw0KICAgICAgICAgIHRoaXMuZm9ybUZpZWxkc0xpc3QgPSBbXTsNCiAgICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5pbml0UHJldmlld0RhdGEoKTsNCiAgICB9LA0KDQogICAgLyoqIOWIneWni+WMlumihOiniOaVsOaNriAqLw0KICAgIGluaXRQcmV2aWV3RGF0YSgpIHsNCiAgICAgIHRoaXMucHJldmlld0RhdGEgPSB7fTsNCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0LmZvckVhY2gobW9kdWxlID0+IHsNCiAgICAgICAgaWYgKG1vZHVsZS5maWVsZHMpIHsNCiAgICAgICAgICBtb2R1bGUuZmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUpIHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgPT09ICdjaGVja2JveCcpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdID0gW107DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ251bWJlcicpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdID0gbnVsbDsNCiAgICAgICAgICAgICAgfSBlbHNlIGlmIChmaWVsZC50eXBlID09PSAnZGF0ZScgfHwgZmllbGQudHlwZSA9PT0gJ3RpbWUnKSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSA9IG51bGw7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSA9ICcnOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5re75Yqg6KGo5Y2V5qih5Z2XICovDQogICAgYWRkRm9ybU1vZHVsZSgpIHsNCiAgICAgIGNvbnN0IG5ld01vZHVsZSA9IHsNCiAgICAgICAgbmFtZTogJ+aWsOaooeWdlycsDQogICAgICAgIGRlc2NyaXB0aW9uOiAnJywNCiAgICAgICAgZmllbGRzOiBbXSwNCiAgICAgICAgaWNvbjogJycgLy8g6buY6K6k5peg5Zu+5qCH77yM6ZyA6KaB55So5oi35LiK5LygDQogICAgICB9Ow0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QucHVzaChuZXdNb2R1bGUpOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5qih5Z2XICovDQogICAgcmVtb3ZlTW9kdWxlKG1vZHVsZUluZGV4KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTliKDpmaTor6XmqKHlnZflj4rlhbbmiYDmnInlrZfmrrXlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdC5zcGxpY2UobW9kdWxlSW5kZXgsIDEpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5qih5Z2X5LiK56e7ICovDQogICAgbW92ZU1vZHVsZVVwKG1vZHVsZUluZGV4KSB7DQogICAgICBpZiAobW9kdWxlSW5kZXggPiAwKSB7DQogICAgICAgIGNvbnN0IHRlbXAgPSB0aGlzLmZvcm1Nb2R1bGVzTGlzdFttb2R1bGVJbmRleF07DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1Nb2R1bGVzTGlzdCwgbW9kdWxlSW5kZXgsIHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4IC0gMV0pOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtTW9kdWxlc0xpc3QsIG1vZHVsZUluZGV4IC0gMSwgdGVtcCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmqKHlnZfkuIvnp7sgKi8NCiAgICBtb3ZlTW9kdWxlRG93bihtb2R1bGVJbmRleCkgew0KICAgICAgaWYgKG1vZHVsZUluZGV4IDwgdGhpcy5mb3JtTW9kdWxlc0xpc3QubGVuZ3RoIC0gMSkgew0KICAgICAgICBjb25zdCB0ZW1wID0gdGhpcy5mb3JtTW9kdWxlc0xpc3RbbW9kdWxlSW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtTW9kdWxlc0xpc3QsIG1vZHVsZUluZGV4LCB0aGlzLmZvcm1Nb2R1bGVzTGlzdFttb2R1bGVJbmRleCArIDFdKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybU1vZHVsZXNMaXN0LCBtb2R1bGVJbmRleCArIDEsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6aqM6K+B5qih5Z2X5ZCN56ewICovDQogICAgdmFsaWRhdGVNb2R1bGVOYW1lKG1vZHVsZSkgew0KICAgICAgaWYgKCFtb2R1bGUubmFtZSB8fCBtb2R1bGUubmFtZS50cmltKCkgPT09ICcnKSB7DQogICAgICAgIG1vZHVsZS5uYW1lID0gJ+acquWRveWQjeaooeWdlyc7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlkJHmqKHlnZfmt7vliqDlrZfmrrUgKi8NCiAgICBhZGRGaWVsZFRvTW9kdWxlKG1vZHVsZUluZGV4KSB7DQogICAgICBjb25zdCBuZXdGaWVsZCA9IHsNCiAgICAgICAgbGFiZWw6ICcnLA0KICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgdHlwZTogJ2lucHV0JywNCiAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgICBoaWRkZW46IGZhbHNlLCAvLyDpu5jorqTkuI3pmpDol48NCiAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWlJywNCiAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgIH07DQogICAgICBpZiAoIXRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XS5maWVsZHMpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XSwgJ2ZpZWxkcycsIFtdKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XS5maWVsZHMucHVzaChuZXdGaWVsZCk7DQogICAgICAvLyDmm7TmlrDpooTop4jmlbDmja4NCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0UHJldmlld0RhdGEoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5Zu+5qCH5ZG95LukICovDQogICAgaGFuZGxlSWNvbkNvbW1hbmQoY29tbWFuZCwgbW9kdWxlSW5kZXgpIHsNCiAgICAgIHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID0gbW9kdWxlSW5kZXg7DQoNCiAgICAgIGlmIChjb21tYW5kID09PSAndXBsb2FkJykgew0KICAgICAgICB0aGlzLnVwbG9hZGVkSWNvbiA9ICcnOw0KICAgICAgICB0aGlzLmljb25VcGxvYWRlck9wZW4gPSB0cnVlOw0KICAgICAgfSBlbHNlIGlmIChjb21tYW5kID09PSAncmVtb3ZlJykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtTW9kdWxlc0xpc3RbbW9kdWxlSW5kZXhdLCAnaWNvbicsICcnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaJk+W8gOWbvuagh+S4iuS8oOWZqCAqLw0KICAgIG9wZW5JY29uVXBsb2FkZXIobW9kdWxlSW5kZXgpIHsNCiAgICAgIHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID0gbW9kdWxlSW5kZXg7DQogICAgICB0aGlzLnVwbG9hZGVkSWNvbiA9ICcnOw0KICAgICAgdGhpcy5pY29uVXBsb2FkZXJPcGVuID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOehruiupOWbvuagh+S4iuS8oCAqLw0KICAgIGNvbmZpcm1JY29uVXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID49IDAgJiYgdGhpcy51cGxvYWRlZEljb24pIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybU1vZHVsZXNMaXN0W3RoaXMuY3VycmVudE1vZHVsZUluZGV4XSwgJ2ljb24nLCB0aGlzLnVwbG9hZGVkSWNvbik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zu+5qCH5LiK5Lyg5oiQ5YqfJyk7DQogICAgICB9DQogICAgICB0aGlzLmljb25VcGxvYWRlck9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID0gLTE7DQogICAgICB0aGlzLnVwbG9hZGVkSWNvbiA9ICcnOw0KICAgIH0sDQoNCiAgICAvKiog5Y+W5raI5Zu+5qCH5LiK5LygICovDQogICAgY2FuY2VsSWNvblVwbG9hZCgpIHsNCiAgICAgIHRoaXMuaWNvblVwbG9hZGVyT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50TW9kdWxlSW5kZXggPSAtMTsNCiAgICAgIHRoaXMudXBsb2FkZWRJY29uID0gJyc7DQogICAgfSwNCg0KICAgIC8qKiDlrZfmrrXnsbvlnovlj5jljJbml7bnmoTlpITnkIYgKi8NCiAgICBvbkZpZWxkVHlwZUNoYW5nZShmaWVsZCkgew0KICAgICAgLy8g5qC55o2u5a2X5q6157G75Z6L6K6+572u6buY6K6k55qEcGxhY2Vob2xkZXINCiAgICAgIGNvbnN0IHBsYWNlaG9sZGVyTWFwID0gew0KICAgICAgICAnaW5wdXQnOiAn6K+36L6T5YWlJywNCiAgICAgICAgJ3RleHRhcmVhJzogJ+ivt+i+k+WFpScsDQogICAgICAgICdudW1iZXInOiAn6K+36L6T5YWl5pWw5a2XJywNCiAgICAgICAgJ3RlbCc6ICfor7fovpPlhaXnlLXor53lj7fnoIEnLA0KICAgICAgICAnZW1haWwnOiAn6K+36L6T5YWl6YKu566x5Zyw5Z2AJywNCiAgICAgICAgJ3JhZGlvJzogJycsDQogICAgICAgICdjaGVja2JveCc6ICcnLA0KICAgICAgICAnc2VsZWN0JzogJ+ivt+mAieaLqScsDQogICAgICAgICdkYXRlJzogJ+ivt+mAieaLqeaXpeacnycsDQogICAgICAgICd0aW1lJzogJ+ivt+mAieaLqeaXtumXtCcsDQogICAgICAgICdmaWxlJzogJ+eCueWHu+S4iuS8oCcsDQogICAgICAgICdzdGF0aWMnOiAnJw0KICAgICAgfTsNCg0KICAgICAgaWYgKCFmaWVsZC5wbGFjZWhvbGRlciB8fCBmaWVsZC5wbGFjZWhvbGRlciA9PT0gJycpIHsNCiAgICAgICAgZmllbGQucGxhY2Vob2xkZXIgPSBwbGFjZWhvbGRlck1hcFtmaWVsZC50eXBlXSB8fCAn6K+36L6T5YWlJzsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5piv6Z2Z5oCB5bGV56S65a2X5q6177yM6K6+572u6buY6K6k5YaF5a655ZKM5riF6Zmk5b+F5aGr54q25oCBDQogICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ3N0YXRpYycpIHsNCiAgICAgICAgZmllbGQucmVxdWlyZWQgPSBmYWxzZTsNCiAgICAgICAgaWYgKCFmaWVsZC5zdGF0aWNDb250ZW50KSB7DQogICAgICAgICAgZmllbGQuc3RhdGljQ29udGVudCA9ICfov5nph4zmmK/pnZnmgIHlsZXnpLrlhoXlrrknOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlrZfmrrXpmpDol4/nirbmgIHlj5jljJbml7bnmoTlpITnkIYgKi8NCiAgICBvbkZpZWxkSGlkZGVuQ2hhbmdlKCkgew0KICAgICAgLy8g6Kem5Y+R6KeG5Zu+5pu05paw77yM5peg6ZyA6aKd5aSW5aSE55CGDQogICAgfSwNCg0KICAgIC8qKiDku47mqKHlnZfliKDpmaTlrZfmrrUgKi8NCiAgICByZW1vdmVGaWVsZEZyb21Nb2R1bGUobW9kdWxlSW5kZXgsIGZpZWxkSW5kZXgpIHsNCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XS5maWVsZHMuc3BsaWNlKGZpZWxkSW5kZXgsIDEpOw0KICAgIH0sDQoNCiAgICAvKiog5qih5Z2X5YaF5a2X5q615LiK56e7ICovDQogICAgbW92ZUZpZWxkVXBJbk1vZHVsZShtb2R1bGVJbmRleCwgZmllbGRJbmRleCkgew0KICAgICAgY29uc3QgZmllbGRzID0gdGhpcy5mb3JtTW9kdWxlc0xpc3RbbW9kdWxlSW5kZXhdLmZpZWxkczsNCiAgICAgIGlmIChmaWVsZEluZGV4ID4gMCkgew0KICAgICAgICBjb25zdCB0ZW1wID0gZmllbGRzW2ZpZWxkSW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQoZmllbGRzLCBmaWVsZEluZGV4LCBmaWVsZHNbZmllbGRJbmRleCAtIDFdKTsNCiAgICAgICAgdGhpcy4kc2V0KGZpZWxkcywgZmllbGRJbmRleCAtIDEsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5qih5Z2X5YaF5a2X5q615LiL56e7ICovDQogICAgbW92ZUZpZWxkRG93bkluTW9kdWxlKG1vZHVsZUluZGV4LCBmaWVsZEluZGV4KSB7DQogICAgICBjb25zdCBmaWVsZHMgPSB0aGlzLmZvcm1Nb2R1bGVzTGlzdFttb2R1bGVJbmRleF0uZmllbGRzOw0KICAgICAgaWYgKGZpZWxkSW5kZXggPCBmaWVsZHMubGVuZ3RoIC0gMSkgew0KICAgICAgICBjb25zdCB0ZW1wID0gZmllbGRzW2ZpZWxkSW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQoZmllbGRzLCBmaWVsZEluZGV4LCBmaWVsZHNbZmllbGRJbmRleCArIDFdKTsNCiAgICAgICAgdGhpcy4kc2V0KGZpZWxkcywgZmllbGRJbmRleCArIDEsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5re75Yqg6KGo5Y2V5a2X5q6177yI5YW85a655pen5pa55rOV77yJICovDQogICAgYWRkRm9ybUZpZWxkKCkgew0KICAgICAgLy8g5aaC5p6c5rKh5pyJ5qih5Z2X77yM5YWI5Yib5bu65LiA5Liq6buY6K6k5qih5Z2XDQogICAgICBpZiAodGhpcy5mb3JtTW9kdWxlc0xpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuYWRkRm9ybU1vZHVsZSgpOw0KICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdFswXS5uYW1lID0gJ+WfuuehgOS/oeaBryc7DQogICAgICB9DQoNCiAgICAgIC8vIOa3u+WKoOWIsOesrOS4gOS4quaooeWdlw0KICAgICAgdGhpcy5hZGRGaWVsZFRvTW9kdWxlKDApOw0KICAgIH0sDQoNCg0KDQogICAgLyoqIOeUn+aIkOWtl+auteWQjeensCAqLw0KICAgIGdlbmVyYXRlRmllbGROYW1lKGZpZWxkKSB7DQogICAgICBpZiAoZmllbGQubGFiZWwpIHsNCiAgICAgICAgLy8g5omp5bGV55qE5Lit5paH6L2s6Iux5paH5pig5bCEDQogICAgICAgIGNvbnN0IG5hbWVNYXAgPSB7DQogICAgICAgICAgLy8g5Z+656GA5L+h5oGvDQogICAgICAgICAgJ+Wnk+WQjSc6ICduYW1lJywNCiAgICAgICAgICAn6IGU57O75Lq6JzogJ2NvbnRhY3RfbmFtZScsDQogICAgICAgICAgJ+iBlOezu+eUteivnSc6ICdwaG9uZScsDQogICAgICAgICAgJ+aJi+acuuWPtyc6ICdwaG9uZScsDQogICAgICAgICAgJ+eUteivnSc6ICdwaG9uZScsDQogICAgICAgICAgJ+mCrueusSc6ICdlbWFpbCcsDQogICAgICAgICAgJ+mCrueuseWcsOWdgCc6ICdlbWFpbCcsDQogICAgICAgICAgJ+WFrOWPuCc6ICdjb21wYW55JywNCiAgICAgICAgICAn5YWs5Y+45ZCN56ewJzogJ2NvbXBhbnlfbmFtZScsDQogICAgICAgICAgJ+iBjOS9jSc6ICdwb3NpdGlvbicsDQogICAgICAgICAgJ+mDqOmXqCc6ICdkZXBhcnRtZW50JywNCiAgICAgICAgICAn5Zyw5Z2AJzogJ2FkZHJlc3MnLA0KICAgICAgICAgICfor6bnu4blnLDlnYAnOiAnZGV0YWlsZWRfYWRkcmVzcycsDQoNCiAgICAgICAgICAvLyDmioDmnK/nm7jlhbMNCiAgICAgICAgICAn5oqA5pyv5pa55ZCRJzogJ3RlY2hfZGlyZWN0aW9uJywNCiAgICAgICAgICAn5oqA5pyv5qCIJzogJ3RlY2hfc3RhY2snLA0KICAgICAgICAgICflvIDlj5Hor63oqIAnOiAncHJvZ3JhbW1pbmdfbGFuZ3VhZ2UnLA0KICAgICAgICAgICfpobnnm67lkajmnJ8nOiAncHJvamVjdF9kdXJhdGlvbicsDQogICAgICAgICAgJ+mihOeul+iMg+WbtCc6ICdidWRnZXRfcmFuZ2UnLA0KICAgICAgICAgICfpobnnm67or6bnu4bpnIDmsYInOiAnZGV0YWlsZWRfcmVxdWlyZW1lbnRzJywNCiAgICAgICAgICAn5oqA5pyv6KaB5rGCJzogJ3RlY2hfcmVxdWlyZW1lbnRzJywNCg0KICAgICAgICAgIC8vIOW4guWcuuaOqOW5v+ebuOWFsw0KICAgICAgICAgICfmjqjlub/nsbvlnosnOiAncHJvbW90aW9uX3R5cGUnLA0KICAgICAgICAgICfnm67moIflrqLmiLfnvqTkvZMnOiAndGFyZ2V0X2F1ZGllbmNlJywNCiAgICAgICAgICAn5o6o5bm/5rig6YGTJzogJ3Byb21vdGlvbl9jaGFubmVscycsDQogICAgICAgICAgJ+aOqOW5v+mihOeulyc6ICdwcm9tb3Rpb25fYnVkZ2V0JywNCiAgICAgICAgICAn5o6o5bm/5pe26Ze0JzogJ3Byb21vdGlvbl9kdXJhdGlvbicsDQogICAgICAgICAgJ+aOqOW5v+ebruaghyc6ICdwcm9tb3Rpb25fZ29hbHMnLA0KDQogICAgICAgICAgLy8g5oub6IGY55u45YWzDQogICAgICAgICAgJ+aLm+iBmOiBjOS9jSc6ICdqb2JfcG9zaXRpb24nLA0KICAgICAgICAgICflt6XkvZznu4/pqownOiAnd29ya19leHBlcmllbmNlJywNCiAgICAgICAgICAn5a2m5Y6G6KaB5rGCJzogJ2VkdWNhdGlvbl9yZXF1aXJlbWVudCcsDQogICAgICAgICAgJ+iWqui1hOiMg+WbtCc6ICdzYWxhcnlfcmFuZ2UnLA0KICAgICAgICAgICflt6XkvZzlnLDngrknOiAnd29ya19sb2NhdGlvbicsDQogICAgICAgICAgJ+iBjOS9jeaPj+i/sCc6ICdqb2JfZGVzY3JpcHRpb24nLA0KDQogICAgICAgICAgLy8g5oqV6LWE55u45YWzDQogICAgICAgICAgJ+aKlei1hOexu+Weiyc6ICdpbnZlc3RtZW50X3R5cGUnLA0KICAgICAgICAgICfmipXotYTph5Hpop0nOiAnaW52ZXN0bWVudF9hbW91bnQnLA0KICAgICAgICAgICfmipXotYTpmLbmrrUnOiAnaW52ZXN0bWVudF9zdGFnZScsDQogICAgICAgICAgJ+ihjOS4mumihuWfnyc6ICdpbmR1c3RyeV9maWVsZCcsDQogICAgICAgICAgJ+mhueebruS7i+e7jSc6ICdwcm9qZWN0X2ludHJvZHVjdGlvbicsDQoNCiAgICAgICAgICAvLyDph4fotK3nm7jlhbMNCiAgICAgICAgICAn5Lqn5ZOB5ZCN56ewJzogJ3Byb2R1Y3RfbmFtZScsDQogICAgICAgICAgJ+mHh+i0reaVsOmHjyc6ICdwdXJjaGFzZV9xdWFudGl0eScsDQogICAgICAgICAgJ+i0qOmHj+imgeaxgic6ICdxdWFsaXR5X3JlcXVpcmVtZW50cycsDQogICAgICAgICAgJ+S6pOS7mOaXtumXtCc6ICdkZWxpdmVyeV90aW1lJywNCiAgICAgICAgICAn6YeH6LSt6aKE566XJzogJ3B1cmNoYXNlX2J1ZGdldCcsDQoNCiAgICAgICAgICAvLyDpgJrnlKjlrZfmrrUNCiAgICAgICAgICAn6ZyA5rGC5o+P6L+wJzogJ2Rlc2NyaXB0aW9uJywNCiAgICAgICAgICAn6K+m57uG6K+05piOJzogJ2RldGFpbGVkX2Rlc2NyaXB0aW9uJywNCiAgICAgICAgICAn5aSH5rOoJzogJ3JlbWFyaycsDQogICAgICAgICAgJ+ivtOaYjic6ICdub3RlJywNCiAgICAgICAgICAn5qCH6aKYJzogJ3RpdGxlJywNCiAgICAgICAgICAn5YaF5a65JzogJ2NvbnRlbnQnLA0KICAgICAgICAgICfml7bpl7QnOiAndGltZScsDQogICAgICAgICAgJ+aXpeacnyc6ICdkYXRlJywNCiAgICAgICAgICAn5paH5Lu2JzogJ2ZpbGUnLA0KICAgICAgICAgICflm77niYcnOiAnaW1hZ2UnLA0KICAgICAgICAgICfpmYTku7YnOiAnYXR0YWNobWVudCcNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpoLmnpzmnInnm7TmjqXmmKDlsITvvIzkvb/nlKjmmKDlsITlgLwNCiAgICAgICAgaWYgKG5hbWVNYXBbZmllbGQubGFiZWxdKSB7DQogICAgICAgICAgZmllbGQubmFtZSA9IG5hbWVNYXBbZmllbGQubGFiZWxdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWQpuWImei/m+ihjOaZuuiDvei9rOaNog0KICAgICAgICAgIGxldCBuYW1lID0gZmllbGQubGFiZWwNCiAgICAgICAgICAgIC5yZXBsYWNlKC9bXHNcLVwvXFxdL2csICdfJykgLy8g5pu/5o2i56m65qC844CB5qiq57q/44CB5pac57q/5Li65LiL5YiS57q/DQogICAgICAgICAgICAucmVwbGFjZSgvW15cd1x1NGUwMC1cdTlmYTVdL2csICcnKSAvLyDnp7vpmaTnibnmrorlrZfnrKbvvIzkv53nlZnkuK3oi7HmloflkozmlbDlrZcNCiAgICAgICAgICAgIC50b0xvd2VyQ2FzZSgpOw0KDQogICAgICAgICAgLy8g5aaC5p6c5YyF5ZCr5Lit5paH77yM5bCd6K+V6L2s5o2i5Li65ou86Z+z5oiW6Iux5paHDQogICAgICAgICAgaWYgKC9bXHU0ZTAwLVx1OWZhNV0vLnRlc3QobmFtZSkpIHsNCiAgICAgICAgICAgIC8vIOeugOWNleeahOS4reaWh+WFs+mUruivjeabv+aNog0KICAgICAgICAgICAgbmFtZSA9IG5hbWUNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+exu+Weiy9nLCAndHlwZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/lkI3np7AvZywgJ25hbWUnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv5pe26Ze0L2csICd0aW1lJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+aXpeacny9nLCAnZGF0ZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/lnLDlnYAvZywgJ2FkZHJlc3MnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv55S16K+dL2csICdwaG9uZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/pgq7nrrEvZywgJ2VtYWlsJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+WFrOWPuC9nLCAnY29tcGFueScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/mj4/ov7AvZywgJ2Rlc2NyaXB0aW9uJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+ivtOaYji9nLCAnbm90ZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/lpIfms6gvZywgJ3JlbWFyaycpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/opoHmsYIvZywgJ3JlcXVpcmVtZW50JykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+iMg+WbtC9nLCAncmFuZ2UnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv6aKE566XL2csICdidWRnZXQnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv5pWw6YePL2csICdxdWFudGl0eScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/ku7fmoLwvZywgJ3ByaWNlJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+i0ueeUqC9nLCAnY29zdCcpOw0KDQogICAgICAgICAgICAvLyDlpoLmnpzov5jmnInkuK3mlofvvIzkvb/nlKjmi7zpn7PpppblrZfmr43miJbkv53mjIHljp/moLcNCiAgICAgICAgICAgIGlmICgvW1x1NGUwMC1cdTlmYTVdLy50ZXN0KG5hbWUpKSB7DQogICAgICAgICAgICAgIG5hbWUgPSAnZmllbGRfJyArIERhdGUubm93KCkudG9TdHJpbmcoKS5zbGljZSgtNik7IC8vIOS9v+eUqOaXtumXtOaIs+WQjjbkvY3kvZzkuLrllK/kuIDmoIfor4YNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICBmaWVsZC5uYW1lID0gbmFtZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5bqU55So6aKE6K6+5qih5p2/ICovDQogICAgYXBwbHlUZW1wbGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkVGVtcGxhdGUgPT09ICdiYXNpYycpIHsNCiAgICAgICAgdGhpcy5hcHBseUJhc2ljVGVtcGxhdGUoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWxlY3RlZFRlbXBsYXRlID09PSAndGVjaCcpIHsNCiAgICAgICAgdGhpcy5hcHBseVRlY2hUZW1wbGF0ZSgpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnNlbGVjdGVkVGVtcGxhdGUgPT09ICdidXNpbmVzcycpIHsNCiAgICAgICAgdGhpcy5hcHBseUJ1c2luZXNzVGVtcGxhdGUoKTsNCiAgICAgIH0NCiAgICAgIC8vIOW6lOeUqOaooeadv+WQjuWIneWni+WMlumihOiniOaVsOaNrg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRQcmV2aWV3RGF0YSgpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlupTnlKjln7rnoYDpnIDmsYLmqKHmnb8gKi8NCiAgICBhcHBseUJhc2ljVGVtcGxhdGUoKSB7DQogICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdCA9IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfloavlhpnor7TmmI4nLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiAn6K+35LuU57uG6ZiF6K+75Lul5LiL6K+05piO5ZCO5aGr5YaZ6KGo5Y2VJywNCiAgICAgICAgICBmaWVsZHM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfmuKnppqjmj5DnpLonLA0KICAgICAgICAgICAgICBuYW1lOiAndGlwcycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdGF0aWMnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogZmFsc2UsDQogICAgICAgICAgICAgIGhpZGRlbjogZmFsc2UsDQogICAgICAgICAgICAgIG9wdGlvbnM6ICcnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICfor7flpoLlrp7loavlhpnku6XkuIvkv6Hmga/vvIzmiJHku6zlsIblnKgyNOWwj+aXtuWGheS4juaCqOWPluW+l+iBlOezu+OAguW4pirlj7fnmoTkuLrlv4XloavpobnjgIInDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WfuuehgOS/oeaBrycsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfor7floavlhpnpnIDmsYLnmoTln7rmnKzkv6Hmga8nLA0KICAgICAgICAgIGZpZWxkczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+mcgOaxguagh+mimCcsDQogICAgICAgICAgICAgIG5hbWU6ICd0aXRsZScsDQogICAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXpnIDmsYLmoIfpopgnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfpnIDmsYLmj4/ov7AnLA0KICAgICAgICAgICAgICBuYW1lOiAnZGVzY3JpcHRpb24nLA0KICAgICAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36K+m57uG5o+P6L+w5oKo55qE6ZyA5rGCJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6IGU57O75pa55byPJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmeaCqOeahOiBlOezu+aWueW8j++8jOS7peS+v+aIkeS7rOS4juaCqOWPluW+l+iBlOezuycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6IGU57O75Lq6JywNCiAgICAgICAgICAgICAgbmFtZTogJ2NvbnRhY3RfbmFtZScsDQogICAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXogZTns7vkurrlp5PlkI0nLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfogZTns7vnlLXor50nLA0KICAgICAgICAgICAgICBuYW1lOiAncGhvbmUnLA0KICAgICAgICAgICAgICB0eXBlOiAndGVsJywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgIGhpZGRlbjogZmFsc2UsDQogICAgICAgICAgICAgIG9wdGlvbnM6ICcnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaJi+acuuWPt+eggScsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9DQogICAgICBdOw0KDQogICAgICAvLyDnoa7kv53lrZfmrrXlkI3np7DmraPnoa7nlJ/miJANCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0LmZvckVhY2gobW9kdWxlID0+IHsNCiAgICAgICAgbW9kdWxlLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICB0aGlzLmdlbmVyYXRlRmllbGROYW1lKGZpZWxkKTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOW6lOeUqOaKgOacr+mcgOaxguaooeadvyAqLw0KICAgIGFwcGx5VGVjaFRlbXBsYXRlKCkgew0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn5oqA5pyv6ZyA5rGCJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+ivpue7huaPj+i/sOaCqOeahOaKgOacr+mcgOaxgicsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn5oqA5pyv5pa55ZCRJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJ+WJjeerr+W8gOWPkSzlkI7nq6/lvIDlj5Es56e75Yqo5byA5Y+RLOS6uuW3peaZuuiDvSzlpKfmlbDmja4s5LqR6K6h566XJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6nmioDmnK/mlrnlkJEnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfmioDmnK/moIgnLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ2NoZWNrYm94JywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnSmF2YSxQeXRob24sSmF2YVNjcmlwdCxSZWFjdCxWdWUsU3ByaW5nIEJvb3QsTXlTUUwsUmVkaXMnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+mhueebruivpue7humcgOaxgicsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36K+m57uG5o+P6L+w6aG555uu6ZyA5rGC44CB5Yqf6IO96KaB5rGC44CB5oqA5pyv6KaB5rGC562JJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6aG555uu5L+h5oGvJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmemhueebrueahOWfuuacrOS/oeaBrycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6aG555uu5ZGo5pyfJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJzHlkajlhoUsMS0y5ZGoLDItNOWRqCwxLTLkuKrmnIgsMi0z5Liq5pyILDPkuKrmnIjku6XkuIonLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqemhueebruWRqOacnycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+mihOeul+iMg+WbtCcsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAncmFkaW8nLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJzHkuIfku6XkuIssMS015LiHLDUtMTDkuIcsMTAtMjDkuIcsMjDkuIfku6XkuIonLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+iBlOezu+aWueW8jycsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfor7floavlhpnmgqjnmoTogZTns7vmlrnlvI8nLA0KICAgICAgICAgIGZpZWxkczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+iBlOezu+S6uicsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAnaW5wdXQnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6IGU57O75Lq65aeT5ZCNJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6IGU57O755S16K+dJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICd0ZWwnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5omL5py65Y+356CBJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0NCiAgICAgIF07DQoNCiAgICAgIC8vIOiHquWKqOeUn+aIkOWtl+auteWQjeensA0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QuZm9yRWFjaChtb2R1bGUgPT4gew0KICAgICAgICBtb2R1bGUuZmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgIHRoaXMuZ2VuZXJhdGVGaWVsZE5hbWUoZmllbGQpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5bqU55So5ZWG5Yqh5ZCI5L2c5qih5p2/ICovDQogICAgYXBwbHlCdXNpbmVzc1RlbXBsYXRlKCkgew0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn5ZCI5L2c5L+h5oGvJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmeWQiOS9nOebuOWFs+S/oeaBrycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn5ZCI5L2c57G75Z6LJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdyYWRpbycsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAn5oiY55Wl5ZCI5L2cLOaKgOacr+WQiOS9nCzluILlnLrlkIjkvZws5oqV6LWE5ZCI5L2cJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICcnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICflkIjkvZzmj4/ov7AnLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ3RleHRhcmVhJywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgIGhpZGRlbjogZmFsc2UsDQogICAgICAgICAgICAgIG9wdGlvbnM6ICcnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+ivpue7huaPj+i/sOWQiOS9nOWGheWuueOAgeWQiOS9nOaWueW8j+OAgeacn+acm+i+vuaIkOeahOebruagh+etiScsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WFrOWPuOS/oeaBrycsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfor7floavlhpnmgqjnmoTlhazlj7jln7rmnKzkv6Hmga8nLA0KICAgICAgICAgIGZpZWxkczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+WFrOWPuOWQjeensCcsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAnaW5wdXQnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5YWs5Y+45YWo56ewJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn5YWs5Y+46KeE5qihJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJzEw5Lq65Lul5LiLLDEwLTUw5Lq6LDUwLTIwMOS6uiwyMDAtNTAw5Lq6LDUwMOS6uuS7peS4iicsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oup5YWs5Y+46KeE5qihJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6KGM5Lia6aKG5Z+fJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJ+S6kuiBlOe9kSzph5Hono0s5pWZ6IKyLOWMu+eWlyzliLbpgKDkuJos5pyN5Yqh5LiaLOWFtuS7licsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oup6KGM5Lia6aKG5Z+fJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6IGU57O75pa55byPJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmeiBlOezu+aWueW8j++8jOS7peS+v+aIkeS7rOS4juaCqOWPluW+l+iBlOezuycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6IGU57O75Lq6JywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXogZTns7vkurrlp5PlkI0nLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfogZTns7vnlLXor50nLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ3RlbCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXmiYvmnLrlj7fnoIEnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfpgq7nrrHlnLDlnYAnLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ2VtYWlsJywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXpgq7nrrHlnLDlnYDvvIjpgInloavvvIknLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgXTsNCg0KICAgICAgLy8g6Ieq5Yqo55Sf5oiQ5a2X5q615ZCN56ewDQogICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdC5mb3JFYWNoKG1vZHVsZSA9PiB7DQogICAgICAgIG1vZHVsZS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgdGhpcy5nZW5lcmF0ZUZpZWxkTmFtZShmaWVsZCk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5blrZfmrrXpgInpobkgKi8NCiAgICBnZXRGaWVsZE9wdGlvbnMoZmllbGQpIHsNCiAgICAgIGlmICghZmllbGQub3B0aW9ucyB8fCBmaWVsZC5vcHRpb25zLnRyaW0oKSA9PT0gJycpIHJldHVybiBbXTsNCiAgICAgIHJldHVybiBmaWVsZC5vcHRpb25zLnNwbGl0KCcsJykubWFwKG9wdGlvbiA9PiBvcHRpb24udHJpbSgpKS5maWx0ZXIob3B0aW9uID0+IG9wdGlvbiAhPT0gJycpOw0KICAgIH0sDQoNCiAgICAvKiog6aKE6KeI6KGo5Y2VICovDQogICAgcHJldmlld0Zvcm0oKSB7DQogICAgICB0aGlzLmluaXRQcmV2aWV3RGlhbG9nRGF0YSgpOw0KICAgICAgdGhpcy5wcmV2aWV3T3BlbiA9IHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDliJ3lp4vljJbpooTop4jlr7nor53moYbmlbDmja4gKi8NCiAgICBpbml0UHJldmlld0RpYWxvZ0RhdGEoKSB7DQogICAgICB0aGlzLnByZXZpZXdEaWFsb2dEYXRhID0ge307DQogICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdC5mb3JFYWNoKG1vZHVsZSA9PiB7DQogICAgICAgIGlmIChtb2R1bGUuZmllbGRzKSB7DQogICAgICAgICAgbW9kdWxlLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgIGlmIChmaWVsZC5uYW1lKSB7DQogICAgICAgICAgICAgIGlmIChmaWVsZC50eXBlID09PSAnY2hlY2tib3gnKSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSA9IFtdOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLnR5cGUgPT09ICdudW1iZXInKSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSA9IG51bGw7DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2RhdGUnIHx8IGZpZWxkLnR5cGUgPT09ICd0aW1lJykgew0KICAgICAgICAgICAgICAgIHRoaXMucHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0gPSBudWxsOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMucHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0gPSAnJzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOS/neWtmOihqOWNlemFjee9riAqLw0KICAgIHNhdmVGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRDYXRlZ29yeUlkKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmnKrpgInmi6npnIDmsYLnsbvlnosiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4HmqKHlnZfphY3nva4NCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5mb3JtTW9kdWxlc0xpc3QubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgbW9kdWxlID0gdGhpcy5mb3JtTW9kdWxlc0xpc3RbaV07DQogICAgICAgIGlmICghbW9kdWxlLm5hbWUgfHwgbW9kdWxlLm5hbWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quaooeWdl+WQjeensOS4jeiDveS4uuepumApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KDQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgbW9kdWxlLmZpZWxkcy5sZW5ndGg7IGorKykgew0KICAgICAgICAgIGNvbnN0IGZpZWxkID0gbW9kdWxlLmZpZWxkc1tqXTsNCiAgICAgICAgICBpZiAoIWZpZWxkLmxhYmVsIHx8IGZpZWxkLmxhYmVsLnRyaW0oKSA9PT0gJycpIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDmqKHlnZciJHttb2R1bGUubmFtZX0i5Lit56ysJHtqICsgMX3kuKrlrZfmrrXmoIfnrb7kuI3og73kuLrnqbpgKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZm9ybURhdGEgPSB7DQogICAgICAgIGNhdGVnb3J5SWQ6IHRoaXMuY3VycmVudENhdGVnb3J5SWQsDQogICAgICAgIGZvcm1GaWVsZHM6IEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybU1vZHVsZXNMaXN0KQ0KICAgICAgfTsNCg0KICAgICAgdXBkYXRlRGVtYW5kQ2F0ZWdvcnkoZm9ybURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLooajljZXphY3nva7kv53lrZjmiJDlip8iKTsNCiAgICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y6KGo5Y2V6YWN572u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuS/neWtmOihqOWNlemFjee9ruWksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlj5bmtojooajljZXphY3nva4gKi8NCiAgICBjYW5jZWxGb3JtQ29uZmlnKCkgew0KICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5mb3JtRmllbGRzTGlzdCA9IFtdOw0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbXTsNCiAgICAgIHRoaXMuY3VycmVudENhdGVnb3J5SWQgPSBudWxsOw0KICAgICAgdGhpcy5zZWxlY3RlZFRlbXBsYXRlID0gJyc7DQogICAgICB0aGlzLnByZXZpZXdEYXRhID0ge307DQogICAgICB0aGlzLnByZXZpZXdEaWFsb2dEYXRhID0ge307DQogICAgfQ0KICB9DQp9Ow0K"}, null]}