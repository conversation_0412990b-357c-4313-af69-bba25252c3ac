<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="需求标题" prop="demandTitle">
        <el-input
          v-model="queryParams.demandTitle"
          placeholder="请输入需求标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="需求类型" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择需求类型" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.categoryId"
            :label="category.categoryName"
            :value="category.categoryId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="需求状态" prop="demandStatus">
        <el-select v-model="queryParams.demandStatus" placeholder="请选择需求状态" clearable>
          <el-option label="已发布" value="0" />
          <el-option label="已对接" value="1" />
          <el-option label="已下架" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="对接状态" prop="hasDocking">
        <el-select v-model="queryParams.hasDocking" placeholder="请选择对接状态" clearable>
          <el-option label="未对接" value="0" />
          <el-option label="对接中" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间筛选" prop="timeFilter">
        <el-select v-model="queryParams.timeFilter" placeholder="请选择时间范围" clearable>
          <el-option label="一周内发布" value="week_within" />
          <el-option label="发布满一周" value="week_over" />
          <el-option label="发布满一月" value="month_over" />
          <el-option label="发布满一年" value="year_over" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:demand:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:demand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:demand:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:demand:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="demandList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="需求ID" align="center" prop="demandId" width="70" />
      <el-table-column label="需求标题" align="left" prop="demandTitle" min-width="180" show-overflow-tooltip />
      <el-table-column label="需求类型" align="center" prop="categoryName" width="90" />
      <el-table-column label="需求状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.demandStatus === '0'" type="success" size="small">已发布</el-tag>
          <el-tag v-else-if="scope.row.demandStatus === '1'" type="warning" size="small">已对接</el-tag>
          <el-tag v-else type="danger" size="small">已下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="对接状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.hasDocking === true || scope.row.hasDocking === 1" type="warning" size="small">对接中</el-tag>
          <el-tag v-else type="info" size="small">未对接</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否置顶" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isTop === '1'" type="warning" size="mini">置顶</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="320">
        <template slot-scope="scope">
          <div class="table-actions">
            <el-button
              size="mini"
              type="text"
              @click="handleDetail(scope.row)"
              v-hasPermi="['miniapp:demand:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['miniapp:demand:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleDelete(scope.row)"
              v-hasPermi="['miniapp:demand:remove']"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleToggleTop(scope.row)"
              v-hasPermi="['miniapp:demand:edit']"
            >{{ scope.row.isTop === '1' ? '取消置顶' : '置顶' }}</el-button>
            <el-button
              v-if="scope.row.demandStatus !== '2'"
              size="mini"
              type="text"
              @click="handleOffShelf(scope.row)"
              v-hasPermi="['miniapp:demand:edit']"
              style="color: #E6A23C;"
            >下架</el-button>
            <el-button
              v-if="scope.row.demandStatus === '2'"
              size="mini"
              type="text"
              @click="handleOnShelf(scope.row)"
              v-hasPermi="['miniapp:demand:edit']"
              style="color: #67C23A;"
            >上架</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 联系记录弹窗 -->
    <el-dialog title="联系记录" :visible.sync="contactDialogVisible" width="50%" append-to-body>
      <el-form ref="contactForm" :model="contactForm" :rules="contactRules" label-width="100px">
        <el-form-item label="对接用户">
          <span>{{ contactForm.userName }} ({{ contactForm.userPhone }})</span>
        </el-form-item>

        <el-form-item label="是否已联系" prop="isContacted">
          <el-radio-group v-model="contactForm.isContacted">
            <el-radio label="0">未联系</el-radio>
            <el-radio label="1">已联系</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="联系结果" prop="contactResult" v-if="contactForm.isContacted === '1'">
          <el-select v-model="contactForm.contactResult" placeholder="请选择联系结果" clearable style="width: 100%;">
            <el-option label="联系成功" value="联系成功" />
            <el-option label="无人接听" value="无人接听" />
            <el-option label="号码错误" value="号码错误" />
            <el-option label="拒绝沟通" value="拒绝沟通" />
            <el-option label="稍后联系" value="稍后联系" />
            <el-option label="已有合作" value="已有合作" />
            <el-option label="不感兴趣" value="不感兴趣" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="联系备注" prop="contactNotes">
          <el-input
            v-model="contactForm.contactNotes"
            type="textarea"
            :rows="4"
            placeholder="请输入联系备注，如沟通内容、后续计划等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="联系时间" prop="contactTime" v-if="contactForm.isContacted === '1'">
          <el-date-picker
            v-model="contactForm.contactTime"
            type="datetime"
            placeholder="选择联系时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="contactDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitContactForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 需求详情弹窗 -->
    <el-dialog title="需求详情" :visible.sync="detailDialogVisible" width="70%" append-to-body>
      <div class="detail-content">
        <!-- 内容信息 -->
        <div class="info-section">
          <h4 class="section-header">内容信息</h4>

          <div class="info-row">
            <span class="info-label">标题：</span>
            <span class="info-value">{{ detailForm.demandTitle || '' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">类型：</span>
            <span class="info-value">{{ detailForm.categoryName || '' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">状态：</span>
            <span class="info-value">
              <el-tag v-if="detailForm.demandStatus === '0'" type="success" size="small">已发布</el-tag>
              <el-tag v-else-if="detailForm.demandStatus === '1'" type="warning" size="small">已对接</el-tag>
              <el-tag v-else type="danger" size="small">已下架</el-tag>
            </span>
          </div>

          <div class="info-row">
            <span class="info-label">对接状态：</span>
            <span class="info-value">
              <el-tag v-if="detailForm.hasDocking === true || detailForm.hasDocking === 1" type="warning" size="small">对接中</el-tag>
              <el-tag v-else type="info" size="small">未对接</el-tag>
            </span>
          </div>

          <div class="info-row">
            <span class="info-label">是否置顶：</span>
            <span class="info-value">
              <el-tag v-if="detailForm.isTop === '1'" type="warning" size="small">置顶</el-tag>
              <span v-else>否</span>
            </span>
          </div>

          <div class="info-row">
            <span class="info-label">浏览次数：</span>
            <span class="info-value">{{ detailForm.viewCount || 0 }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">发布时间：</span>
            <span class="info-value">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </div>

          <div class="info-row" v-if="detailForm.demandDesc">
            <span class="info-label">需求描述：</span>
            <div class="info-value description-text">{{ detailForm.demandDesc }}</div>
          </div>

          <div class="info-row">
            <span class="info-label">联系人：</span>
            <span class="info-value">{{ detailForm.contactName || '' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">联系电话：</span>
            <span class="info-value">{{ detailForm.contactPhone || '' }}</span>
          </div>
        </div>

        <!-- 分隔线 -->
        <el-divider content-position="left">
          <i class="el-icon-document"></i>
          表单数据
        </el-divider>

        <!-- 表单数据部分 -->
        <div class="form-data-section" v-if="detailForm.formDataList && detailForm.formDataList.length > 0">
          <el-descriptions :column="1" border>
            <el-descriptions-item
              v-for="(item, index) in detailForm.formDataList"
              :key="index"
              :label="item.label"
              :label-style="{ width: '120px', fontWeight: 'bold' }"
            >
              <template v-if="item.type === 'textarea'">
                <div class="textarea-content">{{ item.value || '未填写' }}</div>
              </template>
              <template v-else-if="item.type === 'select' || item.type === 'radio'">
                <el-tag type="primary" size="small">{{ item.value || '未选择' }}</el-tag>
              </template>
              <template v-else-if="item.type === 'checkbox'">
                <div v-if="Array.isArray(item.value) && item.value.length > 0">
                  <el-tag v-for="val in item.value" :key="val" type="primary" size="small" style="margin-right: 5px;">{{ val }}</el-tag>
                </div>
                <span v-else>未选择</span>
              </template>
              <template v-else-if="item.type === 'file'">
                <div v-if="Array.isArray(item.value) && item.value.length > 0">
                  <div v-for="(file, fileIndex) in item.value" :key="fileIndex" class="file-item">
                    <i class="el-icon-document"></i>
                    <a :href="file.url || file" target="_blank" class="file-link">
                      {{ file.name || getFileNameFromUrl(file.url || file) }}
                    </a>
                  </div>
                </div>
                <span v-else-if="typeof item.value === 'string' && item.value">
                  <i class="el-icon-document"></i>
                  <a :href="item.value" target="_blank" class="file-link">
                    {{ getFileNameFromUrl(item.value) }}
                  </a>
                </span>
                <span v-else>未上传</span>
              </template>
              <template v-else-if="item.type === 'tel'">
                <span class="phone-number">{{ item.value || '未填写' }}</span>
              </template>
              <template v-else>
                <span>{{ item.value || '未填写' }}</span>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 分隔线 -->
        <el-divider content-position="left">
          <i class="el-icon-user-solid"></i>
          对接记录
          <el-tag size="small" type="info" style="margin-left: 8px;">
            共 {{ (detailForm.dockingList || []).length }} 条记录
          </el-tag>
        </el-divider>

        <!-- 对接记录部分 -->
        <div class="docking-section">
          <el-table
            :data="detailForm.dockingList || []"
            size="small"
            border
            :key="'docking-table-' + tableRefreshKey"
            class="docking-table"
            v-loading="detailForm.dockingLoading"
          >
            <!-- 自定义空数据提示 -->
            <template slot="empty">
              <div class="empty-data">
                <i class="el-icon-document"></i>
                <p>暂无对接记录</p>
              </div>
            </template>
              <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>

              <el-table-column label="对接用户" width="200" align="left">
                <template slot-scope="scope">
                  <div class="user-info">
                    <div class="user-name">
                      <i class="el-icon-user"></i>
                      <strong>{{ scope.row.userName || '未知用户' }}</strong>
                    </div>
                    <div class="user-phone" v-if="scope.row.userPhone">
                      <i class="el-icon-phone"></i>
                      {{ scope.row.userPhone }}
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="工作信息" min-width="200" align="left">
                <template slot-scope="scope">
                  <div class="work-info">
                    <div class="company" v-if="scope.row.userCompany">
                      <i class="el-icon-office-building"></i>
                      {{ scope.row.userCompany }}
                    </div>
                    <div class="position" v-if="scope.row.userPosition">
                      <i class="el-icon-suitcase"></i>
                      {{ scope.row.userPosition }}
                    </div>
                    <div v-if="!scope.row.userCompany && !scope.row.userPosition" class="no-info">
                      暂无工作信息
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="对接时间" width="140" align="center">
                <template slot-scope="scope">
                  <div class="docking-time">
                    <i class="el-icon-time"></i>
                    {{ parseTime(scope.row.dockingTime, '{y}-{m}-{d} {h}:{i}') }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="联系状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="(scope.row.isContacted === '1' || scope.row.isContacted === 1) ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ (scope.row.isContacted === '1' || scope.row.isContacted === 1) ? '已联系' : '未联系' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="primary"
                    plain
                    icon="el-icon-edit-outline"
                    @click="handleContactRecord(scope.row)"
                  >
                    联系记录
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
        </div>

      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改需求对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="需求类型" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择需求类型" style="width: 100%" @change="onCategoryChange">
                <el-option
                  v-for="category in categoryList"
                  :key="category.categoryId"
                  :label="category.categoryName"
                  :value="category.categoryId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求状态" prop="demandStatus">
              <el-select v-model="form.demandStatus" placeholder="请选择需求状态" style="width: 100%">
                <el-option label="已发布" value="0" />
                <el-option label="已对接" value="1" />
                <el-option label="已下架" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="需求标题" prop="demandTitle">
          <el-input v-model="form.demandTitle" placeholder="请输入需求标题" />
        </el-form-item>
        <el-form-item label="需求描述" prop="demandDesc">
          <el-input v-model="form.demandDesc" type="textarea" placeholder="请输入需求描述" :rows="4" />
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人姓名" prop="contactName">
              <el-input v-model="form.contactName" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否置顶" prop="isTop">
              <el-radio-group v-model="form.isTop">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 动态表单字段 -->
        <div v-if="categoryFieldsData && categoryFieldsData.length > 0" class="dynamic-fields-section">
          <el-divider content-position="left">
            <span style="color: #409EFF; font-weight: bold;">{{ getCategoryName() }}专属字段</span>
          </el-divider>

          <!-- 渲染分类字段 -->
          <div v-for="(categoryData, categoryIndex) in categoryFieldsData" :key="`category-${categoryIndex}`" class="category-group">
            <div v-if="categoryData.name" class="category-title">
              <i class="el-icon-folder-opened"></i>
              <span>{{ categoryData.name }}</span>
            </div>
            <div v-if="categoryData.description" class="category-description">
              {{ categoryData.description }}
            </div>

            <div v-for="(field, fieldIndex) in categoryData.fields" :key="`field-${field.name}-${fieldIndex}`" class="dynamic-field-item">
              <div class="field-label">
                <span v-if="field.required" class="required-mark">*</span>
                {{ field.label }}
              </div>
              <div class="field-content">
                <!-- 静态内容 -->
                <div v-if="field.type === 'static'" class="static-content">
                  {{ field.staticContent }}
                </div>
                <!-- 文本输入 -->
                <el-input
                  v-else-if="field.type === 'input'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 多行文本 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  type="textarea"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  :rows="3"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 数字输入 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  :value="form.dynamicData[field.name] || field.value || 0"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                />
                <!-- 电话号码 -->
                <el-input
                  v-else-if="field.type === 'tel'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 邮箱地址 -->
                <el-input
                  v-else-if="field.type === 'email'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请输入' + field.label"
                  @input="value => handleFieldInput(field, value)"
                />
                <!-- 单选框 -->
                <el-radio-group
                  v-else-if="field.type === 'radio'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  @input="value => handleFieldInput(field, value)"
                >
                  <el-radio
                    v-for="(option, optionIndex) in getFieldOptions(field)"
                    :key="`${field.name}-radio-${optionIndex}-${option}`"
                    :label="option"
                  >{{ option }}</el-radio>
                </el-radio-group>
                <!-- 多选框 -->
                <el-checkbox-group
                  v-else-if="field.type === 'checkbox'"
                  :value="form.dynamicData[field.name] || field.value || []"
                  @input="value => handleFieldInput(field, value)"
                >
                  <el-checkbox
                    v-for="(option, optionIndex) in getFieldOptions(field)"
                    :key="`${field.name}-checkbox-${optionIndex}-${option}`"
                    :label="option"
                  >{{ option }}</el-checkbox>
                </el-checkbox-group>
                <!-- 下拉选择 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  :value="form.dynamicData[field.name] || field.value || ''"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                >
                  <el-option
                    v-for="(option, optionIndex) in getFieldOptions(field)"
                    :key="`${field.name}-option-${optionIndex}-${option}`"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <!-- 日期选择 -->
                <el-date-picker
                  v-else-if="field.type === 'date'"
                  :value="form.dynamicData[field.name] || field.value || null"
                  type="date"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                />
                <!-- 时间选择 -->
                <el-time-picker
                  v-else-if="field.type === 'time'"
                  :value="form.dynamicData[field.name] || field.value || null"
                  :placeholder="field.placeholder || '请选择' + field.label"
                  style="width: 100%"
                  @change="value => handleFieldInput(field, value)"
                />
                <!-- 文件上传 -->
                <div v-else-if="field.type === 'file'">
                  <!-- 如果已有文件URL，显示文件信息 -->
                  <div v-if="field.value && typeof field.value === 'string' && field.value.startsWith('http')" class="existing-file">
                    <div class="file-display">
                      <i class="el-icon-document"></i>
                      <a :href="field.value" target="_blank" class="file-link">
                        {{ getFileNameFromUrl(field.value) }}
                      </a>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        @click="removeFileUrl(field)"
                        class="remove-file-btn"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>

                  <!-- 文件上传组件 -->
                  <el-upload
                    v-else
                    action="/dev-api/common/upload"
                    :headers="uploadHeaders"
                    :on-success="(response, file, fileList) => handleFileSuccess(response, file, fileList, field)"
                    :on-remove="(file, fileList) => handleFileRemove(file, fileList, field)"
                    :file-list="getFileList(field)"
                    :on-preview="handleFilePreview"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
                  </el-upload>

                  <!-- 已上传文件列表显示（数组格式） -->
                  <div v-if="Array.isArray(field.value) && field.value.length > 0" class="uploaded-files-list">
                    <div class="uploaded-files-title">已上传文件：</div>
                    <div
                      v-for="(file, index) in field.value"
                      :key="`uploaded-${field.name}-${index}`"
                      class="uploaded-file-item"
                    >
                      <i class="el-icon-document"></i>
                      <a
                        :href="file.url || file"
                        target="_blank"
                        class="file-link"
                        @click="downloadFile(file.url || file, file.name || getFileNameFromUrl(file))"
                      >
                        {{ file.name || getFileNameFromUrl(file.url || file) }}
                      </a>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        @click="removeUploadedFile(field, index)"
                        class="remove-file-btn"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 详情页面样式 */
.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.info-section, .form-data-section, .docking-section {
  margin-bottom: 20px;
}

.section-header {
  color: #409EFF;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-label {
  font-weight: bold;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  color: #303133;
}

.description-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.textarea-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.phone-number {
  color: #409eff;
  font-weight: 500;
}

.file-item {
  margin-bottom: 5px;
}

.file-link {
  color: #409eff;
  text-decoration: none;
  margin-left: 5px;
}

.file-link:hover {
  text-decoration: underline;
}

/* 对接记录表格样式 */
.docking-section .el-table {
  margin-top: 0; /* 移除上边距，让表格紧贴标题 */
}

/* 空数据提示样式 */
.empty-data {
  padding: 40px 0;
  text-align: center;
  color: #909399;
}

.empty-data i {
  font-size: 48px;
  color: #C0C4CC;
  margin-bottom: 16px;
  display: block;
}

.empty-data p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.docking-section .user-name {
  font-weight: bold;
  color: #303133;
}

.docking-section .user-phone {
  color: #409eff;
  font-weight: 500;
}

.no-data-simple {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-size: 14px;
}

.contacted {
  color: #67c23a;
  margin-right: 5px;
}

.uncontacted {
  color: #e6a23c;
}

/* 表格操作列样式优化 */
.table-actions {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  gap: 2px;
  white-space: nowrap;
}

.table-actions .el-button {
  padding: 4px 6px;
  font-size: 12px;
  margin: 0;
  min-width: auto;
}

.table-actions .el-button + .el-button {
  margin-left: 2px;
}

.el-table .small-padding .cell {
  padding-left: 4px;
  padding-right: 4px;
  white-space: nowrap;
  overflow: visible;
}

.el-table .fixed-width .cell {
  padding-left: 4px;
  padding-right: 4px;
}

/* 表格列间距优化 */
.el-table th,
.el-table td {
  padding: 6px 0;
}

.el-table .cell {
  padding-left: 6px;
  padding-right: 6px;
}

/* 对接记录表格样式 */
.docking-table {
  margin-top: 0; /* 移除上边距，让表格紧贴标题 */
}

.docking-table .user-info {
  text-align: left;
}

.docking-table .user-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.docking-table .user-name i {
  margin-right: 4px;
  color: #409EFF;
}

.docking-table .user-phone {
  font-size: 12px;
  color: #606266;
  display: flex;
  align-items: center;
}

.docking-table .user-phone i {
  margin-right: 4px;
  color: #67C23A;
}

.docking-table .work-info {
  text-align: left;
}

.docking-table .company,
.docking-table .position {
  margin-bottom: 4px;
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
}

.docking-table .company i,
.docking-table .position i {
  margin-right: 4px;
  color: #909399;
}

.docking-table .no-info {
  color: #C0C4CC;
  font-size: 12px;
  font-style: italic;
}

.docking-table .docking-time {
  color: #606266;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.docking-table .docking-time i {
  margin-right: 4px;
  color: #E6A23C;
}

/* 表格行样式 */
.docking-table .el-table__row {
  cursor: default;
}

.docking-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 分隔线样式 */
.el-divider {
  margin: 20px 0;
}

.el-divider__text {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.el-divider__text i {
  margin-right: 6px;
  color: #409EFF;
}

/* 详情弹窗内容区域 */
.docking-section {
  margin-bottom: 20px;
}

.form-data-section {
  margin-top: 10px;
}

/* 分隔线样式 */
.el-divider {
  margin: 20px 0;
}

.el-divider__text {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.el-divider__text i {
  margin-right: 6px;
  color: #409EFF;
}

/* 详情弹窗内容区域 */
.docking-section {
  margin-bottom: 20px;
}

.form-data-section {
  margin-top: 10px;
}
</style>

<script>
import { listDemand, getDemand, delDemand, addDemand, updateDemand, offShelfDemand, onShelfDemand, updateContactStatus } from "@/api/miniapp/demand";
import { getEnabledDemandCategoryList } from "@/api/miniapp/demandcategory";
import request from '@/utils/request';

export default {
  name: "MiniDemand",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 需求表格数据
      demandList: [],
      // 联系记录弹窗
      contactDialogVisible: false,
      // 联系记录表单
      contactForm: {
        dockingId: null,
        userName: '',
        userPhone: '',
        isContacted: '0',
        contactResult: '',
        contactNotes: '',
        contactTime: ''
      },
      // 联系记录表单验证
      contactRules: {
        isContacted: [
          { required: true, message: "请选择是否已联系", trigger: "change" }
        ]
      },
      // 详情弹窗
      detailDialogVisible: false,
      // 详情数据
      detailForm: {
        dockingList: [],
        formDataList: []
      },
      // 表格刷新key
      tableRefreshKey: 0,
      // 需求类型列表
      categoryList: [],
      // 动态表单字段
      dynamicFields: [],
      // 选中的类型名称
      selectedCategoryName: '',
      // 分类字段数据（新格式）
      categoryFieldsData: [],
      // 上传请求头
      uploadHeaders: {
        Authorization: "Bearer " + this.$store.getters.token
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        demandTitle: null,
        categoryId: null,
        demandStatus: null,
        hasDocking: null,
        timeFilter: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryId: [
          { required: true, message: "需求类型不能为空", trigger: "change" }
        ],
        demandTitle: [
          { required: true, message: "需求标题不能为空", trigger: "blur" }
        ],
        demandDesc: [
          { required: true, message: "需求描述不能为空", trigger: "blur" }
        ],
        contactName: [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" }
        ],
        contactPhone: [
          { required: true, message: "联系人电话不能为空", trigger: "blur" },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        demandStatus: [
          { required: true, message: "需求状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  computed: {
    /** 按模块分组的动态字段 */
    groupedDynamicFields() {
      const grouped = {};
      this.dynamicFields.forEach(field => {
        const moduleTitle = field.moduleTitle || '其他字段';
        if (!grouped[moduleTitle]) {
          grouped[moduleTitle] = [];
        }
        grouped[moduleTitle].push(field);
      });
      return grouped;
    },

    /** 安全的动态数据访问器 */
    safeDynamicData() {
      const safeData = { ...this.form.dynamicData };
      this.dynamicFields.forEach(field => {
        if (field.name) {
          if (field.type === 'checkbox' && !Array.isArray(safeData[field.name])) {
            safeData[field.name] = [];
          } else if (field.type === 'file' && !Array.isArray(safeData[field.name])) {
            safeData[field.name] = [];
          }
        }
      });
      return safeData;
    }
  },
  created() {
    this.getList();
    this.getCategoryList();
    // 测试新的数据格式
    this.testNewDataFormat();
  },
  methods: {
    /** 查询需求列表 */
    getList() {
      this.loading = true;
      listDemand(this.queryParams).then(response => {
        this.demandList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取需求列表失败:', error);
        this.loading = false;
        this.$modal.msgError("获取需求列表失败");
      });
    },
    /** 获取需求类型列表 */
    getCategoryList() {
      getEnabledDemandCategoryList().then(response => {
        this.categoryList = response.data;
      }).catch(error => {
        console.error('获取需求类型列表失败:', error);
        this.$modal.msgError("获取需求类型列表失败");
      });
    },


    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        demandId: null,
        categoryId: null,
        demandTitle: "",
        demandDesc: "",
        contactName: "",
        contactPhone: "",
        demandStatus: "0",
        isTop: "0",
        remark: "",
        dynamicData: {}
      };

      // 清除动态字段的验证规则
      Object.keys(this.rules).forEach(key => {
        if (key.startsWith('dynamicData.')) {
          this.$delete(this.rules, key);
        }
      });

      // 重置动态字段
      this.dynamicFields = [];
      this.selectedCategoryName = '';

      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.demandId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加需求";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // 先清理状态，但不重置表单
      this.dynamicFields = [];
      this.selectedCategoryName = '';

      const demandId = row.demandId || this.ids;
      getDemand(demandId).then(response => {
        // 使用$set来保持响应式
        const data = response.data;
        this.$set(this.form, 'demandId', data.demandId);
        this.$set(this.form, 'categoryId', data.categoryId);
        this.$set(this.form, 'demandTitle', data.demandTitle || "");
        this.$set(this.form, 'demandDesc', data.demandDesc || "");
        this.$set(this.form, 'contactName', data.contactName || "");
        this.$set(this.form, 'contactPhone', data.contactPhone || "");
        this.$set(this.form, 'demandStatus', data.demandStatus || "0");
        this.$set(this.form, 'isTop', data.isTop || "0");
        this.$set(this.form, 'remark', data.remark || "");

        // 解析动态表单数据
        if (data.formData) {
          try {
            const formData = JSON.parse(data.formData);

            // 检查是否是新格式的数据（包含fields数组的对象）
            if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {
              // 新格式：先设置表单数据，再处理分类字段数据
              this.$set(this.form, 'dynamicData', {});

              // 从fields中提取数据到dynamicData
              formData.forEach(categoryData => {
                if (categoryData.fields) {
                  categoryData.fields.forEach(field => {
                    if (field.value !== undefined && field.value !== null && field.value !== '') {
                      this.$set(this.form.dynamicData, field.name, field.value);
                    }
                  });
                }
              });

              // 处理分类字段数据
              this.processCategoryFieldsData(formData);
            } else {
              // 旧格式：直接使用formData作为dynamicData
              this.$set(this.form, 'dynamicData', formData);
              this.loadDynamicFields(this.form.categoryId);
            }
          } catch (e) {
            console.error('解析动态表单数据失败:', e);
            this.$set(this.form, 'dynamicData', {});
            this.loadDynamicFields(this.form.categoryId);
          }
        } else {
          this.$set(this.form, 'dynamicData', {});
          this.loadDynamicFields(this.form.categoryId);
        }

        // 在下一个tick中清除表单验证状态
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate();
          }
        });

        this.open = true;
        this.title = "修改需求";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 先验证动态字段
      let dynamicFieldsValid = true;
      let firstErrorField = null;

      // 验证新格式的分类字段数据
      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {
        this.categoryFieldsData.forEach(categoryData => {
          if (categoryData.fields) {
            categoryData.fields.forEach(field => {
              if (field.required && field.name && field.type !== 'static') {
                const value = this.form.dynamicData[field.name];
                let isEmpty = false;

                if (field.type === 'checkbox' || field.type === 'file') {
                  isEmpty = !Array.isArray(value) || value.length === 0;
                } else {
                  isEmpty = value === null || value === undefined || value === '';
                }

                if (isEmpty) {
                  dynamicFieldsValid = false;
                  if (!firstErrorField) {
                    firstErrorField = field.label;
                  }
                }
              }
            });
          }
        });
      } else {
        // 验证旧格式的动态字段
        this.dynamicFields.forEach(field => {
          if (field.required && field.name) {
            const value = this.form.dynamicData[field.name];
            let isEmpty = false;

            if (field.type === 'checkbox' || field.type === 'file') {
              isEmpty = !Array.isArray(value) || value.length === 0;
            } else {
              isEmpty = value === null || value === undefined || value === '';
            }

            if (isEmpty) {
              dynamicFieldsValid = false;
              if (!firstErrorField) {
                firstErrorField = field.label;
              }
            }
          }
        });
      }

      if (!dynamicFieldsValid) {
        this.$modal.msgError(`${firstErrorField}不能为空`);
        return;
      }

      this.$refs["form"].validate(valid => {
        if (valid) {
          const formData = { ...this.form };

          // 构建包含value的完整字段数据格式
          if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {
            // 新格式：使用分类字段数据，并更新每个字段的value
            const categoryDataWithValues = this.categoryFieldsData.map(categoryData => ({
              ...categoryData,
              fields: categoryData.fields.map(field => ({
                ...field,
                value: this.form.dynamicData[field.name] || field.value || (field.type === 'checkbox' || field.type === 'file' ? [] : '')
              }))
            }));
            formData.formData = JSON.stringify(categoryDataWithValues);
          } else if (formData.dynamicData && Object.keys(formData.dynamicData).length > 0) {
            // 旧格式：直接使用dynamicData
            formData.formData = JSON.stringify(formData.dynamicData);
          }

          delete formData.dynamicData; // 删除临时字段

          console.log('submitForm - formData.formData:', formData.formData);

          if (this.form.demandId != null) {
            updateDemand(formData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDemand(formData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 查看详情 */
    handleDetail(row) {
      console.log('查看详情 - 原始数据:', row);

      // 使用Vue.set确保响应式
      this.$set(this, 'detailForm', {
        ...row,
        dockingLoading: true,
        dockingList: [],
        formDataList: []
      });

      // 解析表单数据
      if (row.formData) {
        try {
          const formData = JSON.parse(row.formData);
          this.$set(this.detailForm, 'formDataList', this.parseFormDataForDisplay(formData));
        } catch (e) {
          console.error('解析表单数据失败:', e);
          this.$set(this.detailForm, 'formDataList', []);
        }
      }

      console.log('详情表单数据:', this.detailForm);

      // 打开弹窗
      this.detailDialogVisible = true;

      // 加载对接记录
      request({
        url: `/miniapp/demand/${row.demandId}/dockings`,
        method: 'get'
      }).then(response => {
        console.log('对接记录响应:', response);
        if (response.code === 200) {
          this.$set(this.detailForm, 'dockingList', response.data || []);
        } else {
          this.$set(this.detailForm, 'dockingList', []);
          console.error('获取对接记录失败:', response.msg);
        }
      }).catch(error => {
        console.error('获取对接记录异常:', error);
        this.$set(this.detailForm, 'dockingList', []);
      }).finally(() => {
        this.$set(this.detailForm, 'dockingLoading', false);
      });
    },

    /** 联系记录操作 */
    handleContactRecord(dockingRow) {
      console.log('打开联系记录弹窗，对接记录数据:', dockingRow);
      this.contactForm = {
        dockingId: dockingRow.dockingId,
        userName: dockingRow.userName,
        userPhone: dockingRow.userPhone,
        isContacted: dockingRow.isContacted || '0',
        contactResult: dockingRow.contactResult || '',
        contactNotes: dockingRow.contactNotes || '',
        contactTime: dockingRow.contactTime || ''
      };
      console.log('联系表单数据:', this.contactForm);
      this.contactDialogVisible = true;
    },

    /** 提交联系记录表单 */
    submitContactForm() {
      this.$refs["contactForm"].validate(valid => {
        if (valid) {
          // 如果选择已联系但没有设置联系时间，使用当前时间
          if (this.contactForm.isContacted === '1' && !this.contactForm.contactTime) {
            this.contactForm.contactTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
          }

          // 保存当前联系状态，用于直接更新本地数据
          const dockingId = this.contactForm.dockingId;
          const newIsContacted = this.contactForm.isContacted;
          const newContactResult = this.contactForm.contactResult;
          const newContactNotes = this.contactForm.contactNotes;
          const newContactTime = this.contactForm.contactTime;

          updateContactStatus(this.contactForm).then((response) => {
            console.log('联系状态更新成功:', response);
            this.$modal.msgSuccess("联系记录更新成功");
            this.contactDialogVisible = false;

            // 如果详情弹窗是打开的，先直接更新本地数据，再刷新详情中的对接记录
            if (this.detailDialogVisible && this.detailForm.demandId && this.detailForm.dockingList) {
              console.log('开始更新本地对接记录...');

              // 先直接更新本地数据，立即反映变化
              const dockingItem = this.detailForm.dockingList.find(item => item.dockingId === dockingId);
              console.log('找到的对接记录:', dockingItem);
              console.log('要更新的联系状态:', newIsContacted);
              console.log('当前dockingList:', this.detailForm.dockingList);

              if (dockingItem) {
                console.log('更新前的联系状态:', dockingItem.isContacted);
                this.$set(dockingItem, 'isContacted', newIsContacted);
                this.$set(dockingItem, 'contactResult', newContactResult);
                this.$set(dockingItem, 'contactNotes', newContactNotes);
                this.$set(dockingItem, 'contactTime', newContactTime);
                console.log('更新后的联系状态:', dockingItem.isContacted);

                // 强制刷新表格
                this.tableRefreshKey++;
                console.log('强制刷新表格，新key:', this.tableRefreshKey);
              } else {
                console.error('未找到对应的对接记录，dockingId:', dockingId);
                console.error('所有对接记录的ID:', this.detailForm.dockingList.map(item => item.dockingId));
              }

              // 然后再从服务器刷新完整数据
              console.log('开始刷新详情中的对接记录...');
              this.refreshDetailDockingList();
            }

            // 刷新主列表以更新统计数据
            this.getList();
          }).catch((error) => {
            console.error('联系状态更新失败:', error);
            this.$modal.msgError("更新失败，请稍后重试");
          });
        }
      });
    },

    /** 刷新详情中的对接记录 */
    refreshDetailDockingList() {
      this.$set(this.detailForm, 'dockingLoading', true);
      request({
        url: `/miniapp/demand/${this.detailForm.demandId}/dockings`,
        method: 'get'
      }).then(response => {
        console.log('刷新对接记录响应:', response);
        if (response.code === 200) {
          this.$set(this.detailForm, 'dockingList', response.data || []);
          console.log('更新后的对接记录:', this.detailForm.dockingList);
          // 强制刷新表格
          this.tableRefreshKey++;
          console.log('服务器数据刷新后，强制刷新表格，新key:', this.tableRefreshKey);
          // 调试数据
          this.debugDockingData();
        } else {
          this.$set(this.detailForm, 'dockingList', []);
          console.error('获取对接记录失败:', response.msg);
        }
      }).catch(error => {
        console.error('获取对接记录异常:', error);
        this.$set(this.detailForm, 'dockingList', []);
      }).finally(() => {
        this.$set(this.detailForm, 'dockingLoading', false);
      });
    },

    /** 解析表单数据为显示格式 */
    parseFormDataForDisplay(formData) {
      const displayList = [];

      try {
        // 检查是否是新格式的数据（包含fields数组的对象）
        if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {
          // 新格式：遍历所有分类和字段
          formData.forEach(categoryData => {
            if (categoryData.fields && Array.isArray(categoryData.fields)) {
              categoryData.fields.forEach(field => {
                // 跳过静态展示字段
                if (field.type !== 'static' && field.name && field.value !== undefined && field.value !== null && field.value !== '') {
                  displayList.push({
                    label: field.label || field.name,
                    value: field.value,
                    type: field.type || 'input'
                  });
                }
              });
            }
          });
        } else if (typeof formData === 'object' && formData !== null) {
          // 旧格式：直接遍历对象属性
          Object.keys(formData).forEach(key => {
            const value = formData[key];
            if (value !== undefined && value !== null && value !== '') {
              displayList.push({
                label: key,
                value: value,
                type: 'input' // 默认类型
              });
            }
          });
        }
      } catch (e) {
        console.error('解析表单数据失败:', e);
      }

      return displayList;
    },

    /** 从URL中提取文件名 */
    getFileNameFromUrl(url) {
      if (!url) return '未知文件';
      const parts = url.split('/');
      return parts[parts.length - 1] || '未知文件';
    },

    /** 获取联系结果标签类型 */
    getContactResultType(result) {
      const typeMap = {
        '联系成功': 'success',
        '已有合作': 'success',
        '无人接听': 'warning',
        '稍后联系': 'warning',
        '号码错误': 'danger',
        '拒绝沟通': 'danger',
        '不感兴趣': 'info',
        '其他': 'info'
      };
      return typeMap[result] || 'info';
    },

    /** 调试：检查当前对接记录数据 */
    debugDockingData() {
      console.log('=== 调试对接记录数据 ===');
      console.log('detailForm.dockingList:', this.detailForm.dockingList);
      if (this.detailForm.dockingList && this.detailForm.dockingList.length > 0) {
        this.detailForm.dockingList.forEach((item, index) => {
          console.log(`记录${index + 1}:`, {
            dockingId: item.dockingId,
            userName: item.userName,
            isContacted: item.isContacted,
            isContactedType: typeof item.isContacted
          });
        });
      }
      console.log('tableRefreshKey:', this.tableRefreshKey);
      console.log('========================');
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const demandIds = row ? [row.demandId] : this.ids;
      const confirmText = row
        ? `是否确认删除需求编号为"${row.demandId}"的数据项？`
        : `是否确认删除选中的${this.ids.length}条数据项？`;

      this.$modal.confirm(confirmText).then(function() {
        return delDemand(demandIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/demand/export', {
        ...this.queryParams
      }, `需求数据_${new Date().getTime()}.xlsx`)
    },
    /** 置顶/取消置顶 */
    handleToggleTop(row) {
      const text = row.isTop === "1" ? "取消置顶" : "置顶";
      const isTop = row.isTop === "1" ? "0" : "1";
      this.$modal.confirm('确认要"' + text + '"需求"' + row.demandTitle + '"吗？').then(function() {
        const updateData = {
          demandId: row.demandId,
          isTop: isTop
        };
        return updateDemand(updateData);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    },

    /** 下架需求 */
    handleOffShelf(row) {
      this.$modal.confirm('确认要下架需求"' + row.demandTitle + '"吗？').then(function() {
        return offShelfDemand(row.demandId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("下架成功");
      }).catch(() => {});
    },

    /** 上架需求 */
    handleOnShelf(row) {
      this.$modal.confirm('确认要上架需求"' + row.demandTitle + '"吗？').then(function() {
        return onShelfDemand(row.demandId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("上架成功");
      }).catch(() => {});
    },

    /** 需求类型变化事件 */
    onCategoryChange(categoryId) {
      console.log('onCategoryChange - categoryId:', categoryId);

      // 清空动态表单数据
      this.form.dynamicData = {};
      // 清空分类字段数据
      this.categoryFieldsData = [];

      if (!categoryId) {
        this.dynamicFields = [];
        this.selectedCategoryName = '';
        return;
      }

      const category = this.categoryList.find(cat => cat.categoryId === categoryId);
      console.log('onCategoryChange - found category:', category);

      if (category && category.formFields) {
        try {
          const formConfig = JSON.parse(category.formFields);
          console.log('onCategoryChange - formConfig:', formConfig);

          // 检查是否是新格式的数据（包含fields数组的对象）
          if (Array.isArray(formConfig) && formConfig.length > 0 && formConfig[0].fields) {
            // 新格式：使用分类字段数据
            this.processCategoryFieldsData(formConfig);
            console.log('onCategoryChange - using new format, categoryFieldsData:', this.categoryFieldsData);
          } else {
            // 旧格式：使用传统的动态字段加载
            this.loadDynamicFields(categoryId);
            console.log('onCategoryChange - using old format, dynamicFields:', this.dynamicFields);
          }
        } catch (e) {
          console.error('解析表单配置失败:', e);
          this.loadDynamicFields(categoryId);
        }
      } else {
        console.log('onCategoryChange - no category or formFields found');
        this.dynamicFields = [];
        this.selectedCategoryName = '';
      }
    },

    /** 加载动态表单字段 */
    loadDynamicFields(categoryId) {
      if (!categoryId) {
        this.dynamicFields = [];
        this.selectedCategoryName = '';
        return;
      }

      const category = this.categoryList.find(cat => cat.categoryId === categoryId);
      if (category) {
        this.selectedCategoryName = category.categoryName;

        if (category.formFields) {
          try {
            const formConfig = JSON.parse(category.formFields);
            this.dynamicFields = [];

            // 检查是否是新的模块化结构
            if (Array.isArray(formConfig) && formConfig.length > 0) {
              if (formConfig[0].fields) {
                // 新的模块化结构：提取所有模块中的字段
                formConfig.forEach(module => {
                  if (module.fields && Array.isArray(module.fields)) {
                    module.fields.forEach(field => {
                      // 跳过静态展示字段
                      if (field.type !== 'static' && field.name) {
                        this.dynamicFields.push({
                          ...field,
                          moduleTitle: module.name // 添加模块标题用于分组显示
                        });
                      }
                    });
                  }
                });
              } else {
                // 旧的扁平结构：直接使用
                this.dynamicFields = formConfig;
              }
            }

            // 初始化动态数据对象和验证规则
            this.dynamicFields.forEach(field => {
              if (field.name) {
                // 确保字段总是有正确的初始值
                if (field.type === 'checkbox') {
                  this.$set(this.form.dynamicData, field.name,
                    Array.isArray(this.form.dynamicData[field.name]) ? this.form.dynamicData[field.name] : []);
                } else if (field.type === 'file') {
                  // 处理文件字段的数据转换
                  const fileData = this.form.dynamicData[field.name];
                  if (typeof fileData === 'string' && fileData.trim() !== '') {
                    // 如果是字符串URL，转换为对象数组格式
                    const fileName = fileData.split('/').pop() || '下载文件';
                    this.$set(this.form.dynamicData, field.name, [{
                      name: fileName,
                      url: fileData
                    }]);
                  } else if (Array.isArray(fileData)) {
                    // 如果已经是数组，保持不变
                    this.$set(this.form.dynamicData, field.name, fileData);
                  } else {
                    // 其他情况设为空数组
                    this.$set(this.form.dynamicData, field.name, []);
                  }
                } else if (field.type === 'number') {
                  this.$set(this.form.dynamicData, field.name,
                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);
                } else if (field.type === 'date' || field.type === 'time') {
                  this.$set(this.form.dynamicData, field.name,
                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);
                } else {
                  this.$set(this.form.dynamicData, field.name,
                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : '');
                }

                // 添加动态字段的验证规则
                if (field.required) {
                  const ruleName = `dynamicData.${field.name}`;
                  this.$set(this.rules, ruleName, [
                    {
                      required: true,
                      message: `${field.label}不能为空`,
                      trigger: field.type === 'checkbox' ? 'change' : 'blur'
                    }
                  ]);
                }
              }
            });
          } catch (e) {
            console.error('解析表单字段配置失败:', e);
            this.dynamicFields = [];
          }
        } else {
          this.dynamicFields = [];
        }
      }
    },

    /** 获取字段选项 */
    getFieldOptions(field) {
      if (!field.options) return [];
      return field.options.split(',').map(option => option.trim()).filter(option => option);
    },

    /** 文件上传成功回调 */
    handleFileSuccess(response, file, fileList, field) {
      console.log('handleFileSuccess - response:', response, 'file:', file, 'field:', field.name);

      if (response.code === 200) {
        const fileUrl = response.url || response.fileName || response.data;

        // 对于文件类型字段，value直接存储URL链接，不存储文件名或对象结构
        this.handleFieldInput(field, fileUrl);

        console.log('handleFileSuccess - 文件上传成功，设置URL:', fileUrl);
        console.log('handleFileSuccess - field.value after update:', field.value);
      } else {
        this.$modal.msgError(response.msg || '文件上传失败');
      }
    },

    /** 文件删除回调 */
    handleFileRemove(file, fileList, field) {
      // 文件删除时，直接清空value字段
      this.handleFieldInput(field, '');
      console.log('handleFileRemove - 文件已删除，清空字段值');
    },

    /** 获取多选框的安全值 */
    getCheckboxValue(fieldName) {
      const value = this.form.dynamicData[fieldName];
      return Array.isArray(value) ? value : [];
    },

    /** 更新多选框的值 */
    updateCheckboxValue(fieldName, value) {
      this.$set(this.form.dynamicData, fieldName, Array.isArray(value) ? value : []);
    },

    /** 获取文件列表（用于el-upload组件） */
    getFileList(field) {
      const files = field.value;
      console.log('getFileList - field:', field.name, 'value:', files);

      // 如果是字符串URL且不为空，转换为文件列表格式显示在upload组件中
      if (typeof files === 'string' && files.trim() !== '') {
        return [{
          name: this.getFileNameFromUrl(files),
          url: files,
          uid: `${field.name}-0`,
          status: 'success'
        }];
      }

      // 如果是数组格式（兼容旧数据）
      if (Array.isArray(files)) {
        return files.map((file, index) => ({
          name: file.name || this.getFileNameFromUrl(file.url || file),
          url: file.url || file,
          uid: `${field.name}-${index}`,
          status: 'success'
        }));
      }

      // 其他情况返回空数组
      console.log('getFileList - 无有效文件数据，返回空数组');
      return [];
    },

    /** 获取已上传的文件列表（用于显示） */
    getUploadedFiles(field) {
      const files = field.value;
      return Array.isArray(files) ? files : [];
    },

    /** 文件预览 */
    handleFilePreview(file) {
      if (file.url) {
        window.open(file.url, '_blank');
      }
    },

    /** 下载文件 */
    downloadFile(url, fileName) {
      // 创建一个临时的a标签来触发下载
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '下载文件';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /** 删除已上传的文件 */
    removeUploadedFile(field, index) {
      if (field.value && Array.isArray(field.value)) {
        const newValue = [...field.value];
        newValue.splice(index, 1);
        this.handleFieldInput(field, newValue);
      }
    },

    /** 删除文件URL */
    removeFileUrl(field) {
      this.handleFieldInput(field, '');
    },

    /** 从URL中提取文件名 */
    getFileNameFromUrl(url) {
      if (!url) return '未知文件';
      const parts = url.split('/');
      const fileName = parts[parts.length - 1];
      // 如果文件名包含时间戳等，尝试提取原始文件名
      const match = fileName.match(/.*_\d+A\d+\.(.*)/);
      if (match) {
        return `文件.${match[1]}`;
      }
      return fileName || '未知文件';
    },

    /** 处理字段输入 */
    handleFieldInput(field, value) {
      // 更新字段的value
      field.value = value;
      // 同步到表单数据
      this.$set(this.form.dynamicData, field.name, value);
      console.log('handleFieldInput - field:', field.name, 'value:', value);
    },

    /** 更新字段值到表单数据 */
    updateFieldValue(field) {
      this.$set(this.form.dynamicData, field.name, field.value);
    },

    /** 获取分类名称 */
    getCategoryName() {
      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {
        return this.categoryFieldsData[0].name || '专属字段';
      }
      return this.selectedCategoryName || '专属字段';
    },

    /** 处理分类字段数据 */
    processCategoryFieldsData(data) {
      if (typeof data === 'string') {
        try {
          this.categoryFieldsData = JSON.parse(data);
        } catch (e) {
          console.error('解析分类字段数据失败:', e);
          this.categoryFieldsData = [];
        }
      } else if (Array.isArray(data)) {
        this.categoryFieldsData = data;
      } else {
        this.categoryFieldsData = [];
      }

      // 初始化字段值到表单数据
      this.categoryFieldsData.forEach(categoryData => {
        if (categoryData.fields) {
          categoryData.fields.forEach(field => {
            // 确保字段有初始值
            if (field.value === undefined || field.value === null) {
              if (field.type === 'file') {
                field.value = [];
              } else if (field.type === 'checkbox') {
                field.value = [];
              } else {
                field.value = '';
              }
            }

            // 从表单数据中恢复字段值（如果存在）
            if (this.form.dynamicData && this.form.dynamicData[field.name] !== undefined) {
              field.value = this.form.dynamicData[field.name];
            } else {
              // 设置到表单数据
              this.$set(this.form.dynamicData, field.name, field.value);
            }
          });
        }
      });
    },

    /** 测试新的数据格式 */
    testNewDataFormat() {
      // 使用您提供的实际JSON数据格式进行测试
      const testData = [
        {
          "name": "基础信息",
          "description": "",
          "fields": [
            {
              "label": "企业全称",
              "name": "field_652408",
              "type": "input",
              "required": true,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "",
              "value": "测试企业有限公司"
            },
            {
              "label": "行业标签",
              "name": "field_720944",
              "type": "select",
              "required": true,
              "options": "新能源,硬科技",
              "placeholder": "请选择",
              "staticContent": "",
              "value": "新能源"
            },
            {
              "label": "联系人",
              "name": "contact_name",
              "type": "input",
              "required": true,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "",
              "value": "张三"
            },
            {
              "label": "电话",
              "name": "phone",
              "type": "tel",
              "required": true,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "",
              "value": "13800138000"
            }
          ],
          "icon": "http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png"
        },
        {
          "name": "其他材料补充",
          "description": "",
          "fields": [
            {
              "label": "上传附件",
              "name": "field_989222",
              "type": "file",
              "required": false,
              "options": "",
              "placeholder": "未选择任何文件",
              "staticContent": "",
              "value": "http://************:8080/profile/upload/2025/07/23/xhuFwa0qulPS03911c35329f695848fb659a24f6f159_20250723183220A001.png"
            },
            {
              "label": "邮件提交至",
              "name": "field_227969",
              "type": "static",
              "required": false,
              "options": "",
              "placeholder": "请输入",
              "staticContent": "<EMAIL>(文件名：【企业曝光申请】-企业/项目名）",
              "value": ""
            }
          ],
          "icon": "http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png"
        }
      ];

      // 当点击修改按钮时，可以调用这个方法来设置测试数据
      // this.processCategoryFieldsData(testData);
    },




  }
};
</script>

<style scoped>

/* 详情弹窗样式 - 参考简洁布局 */
.detail-content {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 400px;
}

.info-section {
  background-color: white;
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
}

.section-header {
  background-color: #f8f9fa;
  padding: 12px 20px;
  margin: 0;
  font-size: 14px;
  font-weight: normal;
  color: #666;
  border-bottom: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  align-items: flex-start;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  min-width: 100px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  margin-right: 20px;
}

.info-value {
  color: #333;
  font-size: 14px;
  flex: 1;
  word-break: break-all;
}

.description-text {
  line-height: 1.6;
  white-space: pre-wrap;
}

.docking-section {
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.docking-list {
  padding: 0 20px 20px;
}

.docking-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 15px 0;
}

.docking-item:last-child {
  border-bottom: none;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-main {
  flex: 1;
}

.item-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
}

.user-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.user-phone {
  color: #666;
  font-size: 13px;
}

.item-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 13px;
}

.detail-item i {
  margin-right: 4px;
  color: #999;
}

.item-notes {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
}

.item-notes i {
  margin-right: 4px;
  margin-top: 2px;
  color: #999;
  flex-shrink: 0;
}

.item-actions {
  margin-left: 15px;
  flex-shrink: 0;
}

.no-data-simple {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
}
</style>

<style scoped>
.dynamic-fields-section {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}

.module-group {
  margin-bottom: 20px;
}

.module-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  font-weight: bold;
  color: #303133;
}

.module-title i {
  margin-right: 8px;
  color: #409eff;
}

/* 分类组样式 */
.category-group {
  margin-bottom: 20px;
}

.category-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  font-weight: bold;
  color: #303133;
}

.category-title i {
  margin-right: 8px;
  color: #409eff;
}

.category-description {
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 动态字段垂直布局样式 */
.dynamic-field-item {
  margin-bottom: 20px;
  width: 100%;
}

.dynamic-field-item:last-child {
  margin-bottom: 20px; /* 保持底部间距，避免与下方元素重合 */
}

/* 字段标签样式 */
.dynamic-field-item .field-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 必填字段标识 */
.dynamic-field-item .required-mark {
  color: #f56c6c;
  margin-right: 4px;
}

/* 字段内容区域 */
.dynamic-field-item .field-content {
  width: 100%;
}

/* 表单控件样式 */
.dynamic-field-item .field-content .el-input,
.dynamic-field-item .field-content .el-textarea,
.dynamic-field-item .field-content .el-select,
.dynamic-field-item .field-content .el-input-number,
.dynamic-field-item .field-content .el-date-editor,
.dynamic-field-item .field-content .el-time-picker {
  width: 100%;
}

/* 单选框和多选框布局 */
.dynamic-field-item .field-content .el-radio-group,
.dynamic-field-item .field-content .el-checkbox-group {
  width: 100%;
  line-height: 1.8;
}

.dynamic-field-item .field-content .el-radio,
.dynamic-field-item .field-content .el-checkbox {
  margin-right: 20px;
  margin-bottom: 10px;
  display: inline-block;
}

/* 文件上传组件 */
.dynamic-field-item .field-content .el-upload {
  width: 100%;
}

.el-divider {
  margin: 10px 0 20px 0;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .dynamic-field-item .el-form-item__label {
    width: 100px !important;
    text-align: left;
  }

  .dynamic-field-item .el-radio,
  .dynamic-field-item .el-checkbox {
    display: block;
    margin-bottom: 10px;
  }
}

/* 上传组件样式优化 */
.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.dynamic-field-item .el-upload {
  width: 100%;
}

.dynamic-field-item .el-upload-list {
  margin-top: 10px;
}

/* 文件上传相关样式 */
.uploaded-files-list {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.uploaded-files-title {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 8px;
}

.uploaded-file-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
}

.uploaded-file-item:last-child {
  border-bottom: none;
}

.uploaded-file-item i {
  margin-right: 8px;
  color: #409eff;
  font-size: 16px;
}

.file-link {
  flex: 1;
  color: #409eff;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
}

.file-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.remove-file-btn {
  margin-left: 10px;
  color: #f56c6c;
}

.remove-file-btn:hover {
  color: #f78989;
}

/* 静态内容样式 */
.static-content {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #e4e7ed;
}

/* 已存在文件显示样式 */
.existing-file {
  margin-bottom: 10px;
}

.file-display {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  gap: 8px;
}

.file-display .el-icon-document {
  color: #409eff;
  font-size: 16px;
}

.file-display .file-link {
  flex: 1;
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
}

.file-display .file-link:hover {
  text-decoration: underline;
}

.file-display .remove-file-btn {
  color: #f56c6c;
  padding: 0;
}

/* 动态字段整体布局优化 */
.dynamic-fields-section .el-row {
  margin-left: -10px;
  margin-right: -10px;
}

.dynamic-fields-section .el-col {
  padding-left: 10px;
  padding-right: 10px;
}

/* 优化表单验证错误提示的显示 */
.dynamic-field-item .el-form-item__error {
  position: static;
  margin-top: 2px;
  padding-top: 2px;
}

/* 对接情况显示样式 */
.docking-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.contact-stats {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.contacted {
  color: #67c23a;
}

.uncontacted {
  color: #e6a23c;
}

/* 展开内容样式 */
.expand-content {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 10px 0;
}

.expand-content h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.no-docking {
  text-align: center;
  padding: 20px;
}

/* 对接详情表格样式 */
.expand-content .el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.expand-content .el-table th {
  background-color: #fafafa;
}
</style>
