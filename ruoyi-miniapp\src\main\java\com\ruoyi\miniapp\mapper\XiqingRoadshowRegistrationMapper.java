package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.XiqingRoadshowRegistration;
import org.apache.ibatis.annotations.Mapper;

/**
 * 西青金种子路演报名管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Mapper
public interface XiqingRoadshowRegistrationMapper 
{
    /**
     * 查询西青金种子路演报名管理
     * 
     * @param registrationId 西青金种子路演报名管理主键
     * @return 西青金种子路演报名管理
     */
    public XiqingRoadshowRegistration selectXiqingRoadshowRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询西青金种子路演报名管理列表
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 西青金种子路演报名管理集合
     */
    public List<XiqingRoadshowRegistration> selectXiqingRoadshowRegistrationList(XiqingRoadshowRegistration xiqingRoadshowRegistration);

    /**
     * 新增西青金种子路演报名管理
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 结果
     */
    public int insertXiqingRoadshowRegistration(XiqingRoadshowRegistration xiqingRoadshowRegistration);

    /**
     * 修改西青金种子路演报名管理
     * 
     * @param xiqingRoadshowRegistration 西青金种子路演报名管理
     * @return 结果
     */
    public int updateXiqingRoadshowRegistration(XiqingRoadshowRegistration xiqingRoadshowRegistration);

    /**
     * 删除西青金种子路演报名管理
     * 
     * @param registrationId 西青金种子路演报名管理主键
     * @return 结果
     */
    public int deleteXiqingRoadshowRegistrationByRegistrationId(Long registrationId);

    /**
     * 批量删除西青金种子路演报名管理
     * 
     * @param registrationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXiqingRoadshowRegistrationByRegistrationIds(Long[] registrationIds);
}
