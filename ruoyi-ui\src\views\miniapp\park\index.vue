<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="园区名称" prop="parkName">
        <el-input
          v-model="queryParams.parkName"
          placeholder="请输入园区名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="园区编码" prop="parkCode">
        <el-input
          v-model="queryParams.parkCode"
          placeholder="请输入园区编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="园区简介" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入园区简介"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:park:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:park:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:park:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:park:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-picture"
          size="mini"
          @click="handleIntroImageUpload"
          v-hasPermi="['miniapp:park:edit']"
        >园区简介图片</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="parkList" 
      @selection-change="handleSelectionChange"
      row-key="parkId"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="园区ID" align="center" prop="parkId" width="80" />
      <el-table-column label="园区名称" align="center" prop="parkName" :show-overflow-tooltip="true" />
      <el-table-column label="园区编码" align="center" prop="parkCode" width="120" />
      <el-table-column label="园区简介" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="封面图片" align="center" prop="coverImage" width="120">
        <template slot-scope="scope">
          <image-preview :src="scope.row.coverImage" :width="80" :height="50" v-if="scope.row.coverImage"/>
        </template>
      </el-table-column>

      <el-table-column label="排序" align="center" prop="sortOrder" width="80">
        <template slot-scope="scope">
          <el-input-number 
            v-model="scope.row.sortOrder" 
            :min="0" 
            size="mini"
            :controls="false"
            @change="handleSortChange(scope.row)"
            style="width: 70px;"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:park:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:park:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改园区管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="园区名称" prop="parkName">
              <el-input v-model="form.parkName" placeholder="请输入园区名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="园区编码" prop="parkCode">
              <el-input v-model="form.parkCode" placeholder="请输入园区编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="园区简介" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入园区简介" />
        </el-form-item>
        <el-form-item label="封面图片" prop="coverImage">
          <image-upload v-model="form.coverImage"/>
        </el-form-item>
        <el-form-item label="详细内容" prop="content">
          <editor v-model="form.content" :min-height="300"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 园区简介图片上传对话框 -->
    <el-dialog title="园区简介图片管理" :visible.sync="introImageOpen" width="700px" append-to-body>
      <el-form ref="introImageForm" :model="introImageForm" label-width="120px">
        <el-form-item label="当前简介图片">
          <!-- 加载状态 -->
          <div v-if="introImageLoading" style="padding: 20px; text-align: center;">
            <i class="el-icon-loading" style="margin-right: 5px;"></i>
            <span>正在加载图片信息...</span>
          </div>

          <!-- 已设置图片 -->
          <div v-else-if="introImageForm.introImageUrl && introImageForm.introImageUrl.trim() !== ''" style="padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div>
                <i class="el-icon-success" style="color: #67c23a; margin-right: 5px;"></i>
                <span style="color: #409eff;">已设置园区简介图片</span>
              </div>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-view"
                @click="previewIntroImage"
              >
                预览图片
              </el-button>
            </div>
            <!-- 图片预览缩略图 -->
            <div style="margin-top: 10px;">
              <img
                :src="introImageForm.introImageUrl"
                style="width: 120px; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer; border: 1px solid #dcdfe6;"
                @click="previewIntroImage"
                @error="handleImageError"
                alt="园区简介图片缩略图"
              />
            </div>
          </div>

          <!-- 未设置图片 -->
          <div v-else style="padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;">
            <i class="el-icon-warning" style="color: #e6a23c; margin-right: 5px;"></i>
            <span style="color: #e6a23c;">暂未设置园区简介图片</span>
          </div>
        </el-form-item>
        <el-form-item label="上传新图片">
          <image-upload v-model="introImageForm.introImageUrl" :limit="1"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitIntroImageForm">确 定</el-button>
        <el-button @click="cancelIntroImage">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="园区简介图片预览"
      :visible.sync="previewVisible"
      width="800px"
      append-to-body
      :before-close="closePreview"
    >
      <div class="image-preview-container">
        <img
          :src="previewImageUrl"
          class="preview-image"
          alt="园区简介图片"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closePreview">关 闭</el-button>
        <el-button type="primary" @click="window.open(previewImageUrl, '_blank')">
          <i class="el-icon-zoom-in"></i> 原始大小查看
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listPark, getPark, delPark, addPark, updatePark, exportPark, getParkIntroImage, updateParkIntroImage } from "@/api/miniapp/park";

export default {
  name: "MiniPark",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 园区管理表格数据
      parkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 园区简介图片对话框
      introImageOpen: false,
      // 园区简介图片表单
      introImageForm: {
        introImageUrl: ''
      },
      // 图片加载状态
      introImageLoading: false,
      // 图片预览
      previewVisible: false,
      previewImageUrl: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parkName: null,
        parkCode: null,
        description: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parkName: [
          { required: true, message: "园区名称不能为空", trigger: "blur" }
        ],
        sortOrder: [
          { required: true, message: "排序不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询园区管理列表 */
    getList() {
      this.loading = true;
      listPark(this.queryParams).then(response => {
        this.parkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        parkId: null,
        parkName: null,
        parkCode: null,
        description: null,
        content: null,
        coverImage: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.parkId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加园区管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const parkId = row.parkId || this.ids;
      getPark(parkId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改园区管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.parkId !== null && this.form.parkId !== undefined) {
            updatePark(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPark(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const parkIds = row.parkId || this.ids;
      this.$modal.confirm('是否确认删除园区管理编号为"' + parkIds + '"的数据项？').then(function() {
        return delPark(parkIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有园区管理数据项？').then(() => {
        this.loading = true;
        return exportPark(this.queryParams);
      }).then(response => {
        this.$download.excel(response, '园区管理数据.xlsx');
        this.loading = false;
      }).catch(() => {});
    },
    /** 排序修改 */
    handleSortChange(row) {
      updatePark(row).then(response => {
        this.$modal.msgSuccess("排序修改成功");
        this.getList();
      });
    },
    /** 园区简介图片上传 */
    handleIntroImageUpload() {
      // 先重置表单数据
      this.introImageForm.introImageUrl = '';
      this.introImageOpen = true;
      // 加载当前图片数据
      this.loadIntroImage();
    },
    /** 加载园区简介图片 */
    loadIntroImage() {
      console.log('开始加载园区简介图片...');
      this.introImageLoading = true;

      getParkIntroImage().then(response => {
        console.log('获取园区简介图片响应:', response);
        if (response.code === 200) {
          // 优先读取data字段，如果没有则读取msg字段（向后兼容）
          this.introImageForm.introImageUrl = response.data || response.msg || '';
          console.log('设置图片URL:', this.introImageForm.introImageUrl);
        } else {
          console.warn('获取园区简介图片失败:', response.msg);
          this.introImageForm.introImageUrl = '';
        }
      }).catch(error => {
        console.error('加载园区简介图片出错:', error);
        this.introImageForm.introImageUrl = '';
        this.$modal.msgError("加载园区简介图片失败");
      }).finally(() => {
        this.introImageLoading = false;
      });
    },
    /** 取消园区简介图片 */
    cancelIntroImage() {
      this.introImageOpen = false;
      // 不要清空数据，保持原有数据
      // this.introImageForm.introImageUrl = '';
    },
    /** 提交园区简介图片 */
    submitIntroImageForm() {
      updateParkIntroImage(this.introImageForm.introImageUrl).then(response => {
        this.$modal.msgSuccess("园区简介图片更新成功");
        this.introImageOpen = false;
      }).catch(error => {
        this.$modal.msgError("更新园区简介图片失败");
      });
    },
    /** 预览园区简介图片 */
    previewIntroImage() {
      if (this.introImageForm.introImageUrl && this.introImageForm.introImageUrl.trim() !== '') {
        this.previewImageUrl = this.introImageForm.introImageUrl;
        this.previewVisible = true;
      } else {
        this.$modal.msgWarning("暂无图片可预览");
      }
    },
    /** 关闭图片预览 */
    closePreview() {
      this.previewVisible = false;
      this.previewImageUrl = '';
    },
    /** 图片加载错误处理 */
    handleImageError(event) {
      console.error('图片加载失败:', this.introImageForm.introImageUrl);
      this.$modal.msgError("图片加载失败，请检查图片链接是否有效");
      // 可以设置一个默认的错误图片
      // event.target.src = '/path/to/default-error-image.png';
    }
  }
};
</script>

<style scoped>
/* 图片预览样式 */
.image-preview-container {
  text-align: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.preview-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.preview-image:hover {
  transform: scale(1.02);
}

/* 缩略图悬停效果 */
.image-preview-container img[alt="园区简介图片缩略图"]:hover {
  opacity: 0.8;
  transform: scale(1.05);
  transition: all 0.3s ease;
}
</style>
