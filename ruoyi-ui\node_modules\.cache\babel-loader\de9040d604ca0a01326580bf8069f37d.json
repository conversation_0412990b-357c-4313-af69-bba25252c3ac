{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue", "mtime": 1753931003624}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}