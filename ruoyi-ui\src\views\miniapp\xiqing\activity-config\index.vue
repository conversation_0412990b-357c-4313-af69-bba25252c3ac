<template>
  <div class="app-container">
    <div class="form-config-container">
      <div class="config-header">
        <h3>路演活动报名表单配置</h3>
        <el-button
          type="primary"
          icon="el-icon-setting"
          @click="handleFormConfig"
          v-hasPermi="['miniapp:xiqing:activity:edit']"
        >配置表单</el-button>
      </div>

      <div class="config-content" v-loading="loading">
        <div v-if="formFields.length > 0" class="form-preview">
          <h4>当前表单字段预览：</h4>
          <div class="field-list">
            <div v-for="(field, index) in formFields" :key="index" class="field-item">
              <div class="field-info">
                <i :class="getFieldIcon(field.type)" class="field-icon"></i>
                <span class="field-label">{{ field.label }}</span>
                <el-tag size="mini" type="success">{{ field.name }}</el-tag>
                <el-tag v-if="field.required" size="mini" type="danger">必填</el-tag>
                <el-tag v-else size="mini" type="info">选填</el-tag>
                <span class="field-type">{{ getFieldTypeName(field.type) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-form">
          <i class="el-icon-document-add"></i>
          <p>暂未配置表单字段，点击"配置表单"开始设置</p>
        </div>
      </div>
    </div>



    <!-- 表单配置对话框 -->
    <el-dialog title="报名表单配置" :visible.sync="formConfigOpen" width="1000px" append-to-body>
      <div class="form-fields-config">
        <!-- 工具栏 -->
        <div class="form-fields-toolbar">
          <div class="toolbar-left">
            <el-button type="primary" size="small" @click="addFormField" icon="el-icon-plus">
              添加字段
            </el-button>
            <el-dropdown @command="handleTemplateCommand" size="small">
              <el-button size="small">
                预设模板<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="basic">基础信息模板</el-dropdown-item>
                <el-dropdown-item command="roadshow">路演报名模板</el-dropdown-item>
                <el-dropdown-item command="clear">清空所有字段</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="toolbar-right">
            <el-button size="small" @click="previewForm" icon="el-icon-view">
              预览表单
            </el-button>
            <el-button type="success" size="small" @click="saveFormConfig" icon="el-icon-check">
              保存配置
            </el-button>
          </div>
        </div>

        <!-- 字段配置区域 -->
        <div class="form-fields-list" v-if="formFields.length > 0">
          <div
            v-for="(field, index) in formFields"
            :key="index"
            class="form-field-item"
          >
            <div class="field-header">
              <div class="field-info">
                <i :class="getFieldIcon(field.type)" class="field-icon"></i>
                <span class="field-label">{{ field.label || '未命名字段' }}</span>
                <el-tag size="mini" type="success">{{ field.name || 'unnamed' }}</el-tag>
                <el-tag v-if="field.required" size="mini" type="danger">必填</el-tag>
                <el-tag v-else size="mini" type="info">选填</el-tag>
              </div>
              <div class="field-actions">
                <el-button
                  type="text"
                  size="mini"
                  @click="moveField(index, -1)"
                  :disabled="index === 0"
                  icon="el-icon-arrow-up"
                ></el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="moveField(index, 1)"
                  :disabled="index === formFields.length - 1"
                  icon="el-icon-arrow-down"
                ></el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="removeFormField(index)"
                  icon="el-icon-delete"
                  class="danger-btn"
                ></el-button>
              </div>
            </div>

            <div class="field-content">
              <el-row :gutter="10">
                <el-col :span="8">
                  <div class="field-item">
                    <label>字段标签</label>
                    <el-input
                      v-model="field.label"
                      placeholder="显示给用户的标签，如：姓名、电话等"
                      size="small"
                      @input="updateFieldName(field, $event)"
                    />
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="field-item">
                    <label>字段名称 (唯一标识)</label>
                    <el-input
                      v-model="field.name"
                      placeholder="字段的唯一标识符"
                      size="small"
                      readonly
                      style="background-color: #f5f7fa;"
                    />
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="field-item">
                    <label>字段类型</label>
                    <el-select v-model="field.type" placeholder="选择类型" size="small" style="width: 100%">
                      <el-option label="📝 文本输入" value="input" />
                      <el-option label="📄 多行文本" value="textarea" />
                      <el-option label="🔢 数字输入" value="number" />
                      <el-option label="📧 邮箱" value="email" />
                      <el-option label="📞 电话" value="tel" />
                      <el-option label="🔘 单选" value="radio" />
                      <el-option label="☑️ 多选" value="checkbox" />
                      <el-option label="📋 下拉选择" value="select" />
                      <el-option label="🔘➕ 单选+其他" value="radio_other" />
                      <el-option label="☑️➕ 多选+其他" value="checkbox_other" />
                      <el-option label="📋➕ 下拉+其他" value="select_other" />
                      <el-option label="📅 日期" value="date" />
                      <el-option label="📎 文件上传" value="file" />
                    </el-select>
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="10" style="margin-top: 10px;">
                <el-col :span="4">
                  <div class="field-item">
                    <label>是否必填</label>
                    <el-switch v-model="field.required" />
                  </div>
                </el-col>
                <el-col :span="20" v-if="['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)">
                  <div class="field-item">
                    <label>选项配置</label>
                    <el-input
                      v-model="field.options"
                      placeholder="用逗号分隔选项，如：选项1,选项2,选项3"
                      size="small"
                    />
                    <div class="options-preview" v-if="field.options">
                      <el-tag
                        v-for="(option, optIndex) in field.options.split(',')"
                        :key="optIndex"
                        size="mini"
                        style="margin-right: 5px; margin-top: 5px;"
                      >
                        {{ option.trim() }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-else>
          <i class="el-icon-document-add"></i>
          <p>暂无表单字段，点击"添加字段"开始配置</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="formConfigOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 表单预览对话框 -->
    <el-dialog title="表单预览" :visible.sync="previewDialogVisible" width="600px" append-to-body>
      <div class="form-preview">
        <div class="preview-header">
          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>
          <p>请填写以下信息完成报名</p>
        </div>
        <div class="preview-form">
          <div v-for="(field, index) in formFields" :key="index" class="preview-field">
            <label class="preview-label">
              {{ field.label }}
              <span v-if="field.required" class="required">*</span>
            </label>
            <div class="preview-input">
              <el-input
                v-if="field.type === 'input' || field.type === 'email' || field.type === 'tel'"
                :placeholder="'请输入' + field.label"
                size="small"
                disabled
              />
              <el-input
                v-else-if="field.type === 'textarea'"
                type="textarea"
                :placeholder="'请输入' + field.label"
                size="small"
                disabled
              />
              <el-input-number
                v-else-if="field.type === 'number'"
                :placeholder="'请输入' + field.label"
                size="small"
                disabled
                style="width: 100%"
              />
              <el-radio-group v-else-if="field.type === 'radio'" disabled>
                <el-radio
                  v-for="(option, optIndex) in (field.options || '').split(',')"
                  :key="optIndex"
                  :label="option.trim()"
                >
                  {{ option.trim() }}
                </el-radio>
              </el-radio-group>
              <el-checkbox-group v-else-if="field.type === 'checkbox'" disabled>
                <el-checkbox
                  v-for="(option, optIndex) in (field.options || '').split(',')"
                  :key="optIndex"
                  :label="option.trim()"
                >
                  {{ option.trim() }}
                </el-checkbox>
              </el-checkbox-group>
              <el-select v-else-if="field.type === 'select'" :placeholder="'请选择' + field.label" size="small" disabled style="width: 100%">
                <el-option
                  v-for="(option, optIndex) in (field.options || '').split(',')"
                  :key="optIndex"
                  :label="option.trim()"
                  :value="option.trim()"
                />
              </el-select>
              <el-date-picker
                v-else-if="field.type === 'date'"
                type="date"
                :placeholder="'请选择' + field.label"
                size="small"
                disabled
                style="width: 100%"
              />
              <el-upload
                v-else-if="field.type === 'file'"
                class="upload-demo"
                action="#"
                :disabled="true"
                :show-file-list="false"
              >
                <el-button size="small" type="primary" disabled>
                  <i class="el-icon-upload"></i> 选择文件
                </el-button>
                <div slot="tip" class="el-upload__tip">支持上传PDF、Word、Excel等格式文件</div>
              </el-upload>
              <!-- 单选+其他 -->
              <div v-else-if="field.type === 'radio_other'">
                <el-radio-group disabled>
                  <div v-for="(option, optIndex) in field.options.split(',')" :key="optIndex" style="display: block; margin-bottom: 8px;">
                    <el-radio :label="option.trim()">{{ option.trim() }}</el-radio>
                  </div>
                  <div style="display: block; margin-bottom: 8px;">
                    <el-radio label="其他">
                      其他
                      <el-input
                        placeholder="请输入其他内容"
                        size="small"
                        disabled
                        style="width: 200px; margin-left: 10px;"
                      />
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
              <!-- 多选+其他 -->
              <div v-else-if="field.type === 'checkbox_other'">
                <el-checkbox-group disabled>
                  <div v-for="(option, optIndex) in field.options.split(',')" :key="optIndex" style="display: block; margin-bottom: 8px;">
                    <el-checkbox :label="option.trim()">{{ option.trim() }}</el-checkbox>
                  </div>
                  <div style="display: block; margin-bottom: 8px;">
                    <el-checkbox label="其他">
                      其他
                      <el-input
                        placeholder="请输入其他内容"
                        size="small"
                        disabled
                        style="width: 200px; margin-left: 10px;"
                      />
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
              <!-- 下拉+其他 -->
              <div v-else-if="field.type === 'select_other'">
                <el-select disabled placeholder="请选择" size="small" style="width: 100%; margin-bottom: 8px;">
                  <el-option v-for="(option, optIndex) in field.options.split(',')" :key="optIndex" :label="option.trim()" :value="option.trim()" />
                  <el-option label="其他" value="其他" />
                </el-select>
                <el-input
                  placeholder="选择'其他'时请在此输入具体内容"
                  size="small"
                  disabled
                  style="width: 100%;"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getActivityConfig, updateActivityConfig } from "@/api/miniapp/xiqing/activity-config";

export default {
  name: "XiqingActivityConfig",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 是否显示表单配置弹出层
      formConfigOpen: false,
      // 是否显示表单预览弹出层
      previewDialogVisible: false,
      // 表单字段配置
      formFields: [],
      // 活动ID（固定为1，因为只有一个路演活动配置）
      activityId: 1
    };
  },
  created() {
    this.loadFormConfig();
  },
  methods: {
    /** 加载表单配置 */
    loadFormConfig() {
      this.loading = true;
      getActivityConfig(this.activityId).then(response => {
        if (response.data && response.data.formConfig) {
          try {
            this.formFields = JSON.parse(response.data.formConfig);
          } catch (e) {
            this.formFields = [];
          }
        } else {
          this.formFields = [];
        }
        this.loading = false;
      }).catch(() => {
        this.formFields = [];
        this.loading = false;
      });
    },
    /** 表单配置按钮操作 */
    handleFormConfig() {
      this.formConfigOpen = true;
    },
    /** 添加表单字段 */
    addFormField() {
      // 生成默认的唯一字段名
      const defaultName = this.generateUniqueFieldName('field');

      this.formFields.push({
        name: defaultName,
        label: '',
        type: 'input',
        required: false,
        options: ''
      });
    },
    /** 删除表单字段 */
    removeFormField(index) {
      this.formFields.splice(index, 1);
    },
    /** 移动字段位置 */
    moveField(index, direction) {
      const newIndex = index + direction;
      if (newIndex >= 0 && newIndex < this.formFields.length) {
        const temp = this.formFields[index];
        this.$set(this.formFields, index, this.formFields[newIndex]);
        this.$set(this.formFields, newIndex, temp);
      }
    },
    /** 更新字段名称 */
    updateFieldName(field, label) {
      if (!field.name || field.name === '') {
        field.name = this.generateUniqueFieldName(label);
      }
    },
    /** 生成唯一字段名称 */
    generateUniqueFieldName(label) {
      const pinyin = {
        '姓名': 'name',
        '联系电话': 'phone',
        '电话': 'phone',
        '邮箱': 'email',
        '邮箱地址': 'email',
        '公司': 'company',
        '项目名称': 'project_name',
        '项目描述': 'project_description',
        '团队规模': 'team_size'
      };

      // 生成基础名称
      let baseName = pinyin[label] || label.toLowerCase().replace(/[\s\u4e00-\u9fa5]+/g, '_').replace(/[^\w_]/g, '');

      // 确保名称不为空
      if (!baseName) {
        baseName = 'field';
      }

      // 检查名称是否已存在，如果存在则添加数字后缀
      let uniqueName = baseName;
      let counter = 1;

      while (this.isFieldNameExists(uniqueName)) {
        uniqueName = `${baseName}_${counter}`;
        counter++;
      }

      return uniqueName;
    },
    /** 检查字段名称是否已存在 */
    isFieldNameExists(name) {
      return this.formFields.some(field => field.name === name);
    },
    /** 获取字段图标 */
    getFieldIcon(type) {
      const icons = {
        input: 'el-icon-edit',
        textarea: 'el-icon-document',
        number: 'el-icon-s-data',
        email: 'el-icon-message',
        tel: 'el-icon-phone',
        radio: 'el-icon-success',
        checkbox: 'el-icon-check',
        select: 'el-icon-arrow-down',
        radio_other: 'el-icon-circle-plus',
        checkbox_other: 'el-icon-square-plus',
        select_other: 'el-icon-plus',
        date: 'el-icon-date',
        file: 'el-icon-upload'
      };
      return icons[type] || 'el-icon-edit';
    },
    /** 处理模板命令 */
    handleTemplateCommand(command) {
      if (command === 'clear') {
        this.$confirm('确定要清空所有字段吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.formFields = [];
          this.$message.success('已清空所有字段');
        });
        return;
      }

      const templates = {
        basic: [
          { label: '姓名', name: '', type: 'input', required: true, options: '' },
          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },
          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' }
        ],
        roadshow: [
          { label: '姓名', name: '', type: 'input', required: true, options: '' },
          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },
          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },
          { label: '公司/团队', name: '', type: 'input', required: true, options: '' },
          { label: '项目名称', name: '', type: 'input', required: true, options: '' },
          { label: '项目来源', name: '', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },
          { label: '项目阶段', name: '', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },
          { label: '团队规模', name: '', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },
          { label: '融资需求', name: '', type: 'input', required: false, options: '' },
          { label: '商业计划书', name: '', type: 'file', required: true, options: '' },
          { label: '项目描述', name: '', type: 'textarea', required: true, options: '' }
        ]
      };

      if (templates[command]) {
        // 清空现有字段
        this.formFields = [];

        // 为每个模板字段生成唯一的name
        const templateFields = templates[command].map(field => ({
          ...field,
          name: this.generateUniqueFieldName(field.label)
        }));

        this.formFields = templateFields;
        this.$message.success('模板应用成功');
      }
    },
    /** 预览表单 */
    previewForm() {
      if (this.formFields.length === 0) {
        this.$message.warning('请先添加表单字段');
        return;
      }
      this.previewDialogVisible = true;
    },
    /** 保存表单配置 */
    saveFormConfig() {
      if (this.formFields.length === 0) {
        this.$message.warning('请至少添加一个表单字段');
        return;
      }

      // 验证字段配置
      const fieldNames = [];
      for (let i = 0; i < this.formFields.length; i++) {
        const field = this.formFields[i];

        // 验证标签不能为空
        if (!field.label) {
          this.$message.error(`第${i + 1}个字段的标签不能为空`);
          return;
        }

        // 验证字段名称不能为空
        if (!field.name) {
          this.$message.error(`第${i + 1}个字段的名称不能为空`);
          return;
        }

        // 验证字段名称唯一性
        if (fieldNames.includes(field.name)) {
          this.$message.error(`字段名称"${field.name}"重复，请确保每个字段名称唯一`);
          return;
        }
        fieldNames.push(field.name);

        // 验证选项配置
        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {
          this.$message.error(`字段"${field.label}"需要配置选项`);
          return;
        }
      }

      const configData = JSON.stringify(this.formFields);
      const updateData = {
        activityId: this.activityId,
        formConfig: configData
      };

      updateActivityConfig(updateData).then(() => {
        this.$modal.msgSuccess("表单配置保存成功");
        this.formConfigOpen = false;
        this.loadFormConfig();
      });
    },
    /** 获取字段类型名称 */
    getFieldTypeName(type) {
      const typeNames = {
        input: '文本输入',
        textarea: '多行文本',
        number: '数字输入',
        email: '邮箱',
        tel: '电话',
        radio: '单选',
        checkbox: '多选',
        select: '下拉选择',
        radio_other: '单选+其他',
        checkbox_other: '多选+其他',
        select_other: '下拉+其他',
        date: '日期',
        file: '文件上传'
      };
      return typeNames[type] || '未知类型';
    }
  }
};
</script>

<style scoped>
.form-config-container {
  max-width: 800px;
  margin: 20px auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.config-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.config-content {
  padding: 24px;
  min-height: 300px;
}

.form-preview h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
}

.field-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.field-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafbfc;
}

.field-item:last-child {
  border-bottom: none;
}

.field-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.field-icon {
  color: #409eff;
  font-size: 16px;
}

.field-label {
  font-weight: 500;
  color: #303133;
  min-width: 100px;
}

.field-type {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
}

.empty-form {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-form i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.form-fields-config {
  min-height: 400px;
}

.form-fields-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.toolbar-left, .toolbar-right {
  display: flex;
  gap: 10px;
}

.form-fields-list {
  max-height: 500px;
  overflow-y: auto;
}

.form-field-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 15px;
  background: #fff;
  transition: all 0.3s;
}

.form-field-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.field-actions {
  display: flex;
  gap: 5px;
}

.danger-btn {
  color: #f56c6c;
}

.field-content {
  padding: 16px;
}

.field-item {
  margin-bottom: 10px;
}

.field-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.options-preview {
  margin-top: 5px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.form-preview {
  padding: 20px;
}

.preview-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.preview-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.preview-field {
  margin-bottom: 20px;
}

.preview-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.required {
  color: #f56c6c;
  margin-left: 2px;
}

.preview-input {
  width: 100%;
}
</style>
