{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\router\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\router\\index.js", "mtime": 1753758425260}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_layout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "redirect", "name", "meta", "title", "icon", "affix", "dynamicRoutes", "permissions", "activeMenu", "alwaysShow", "routerPush", "prototype", "push", "routerReplace", "replace", "location", "call", "catch", "err", "_default", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Router from 'vue-router'\r\n\r\nVue.use(Router)\r\n\r\n/* Layout */\r\nimport Layout from '@/layout'\r\n\r\n/**\r\n * Note: 路由配置项\r\n *\r\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\r\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\r\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\r\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\r\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\r\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\r\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\r\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\r\n * roles: ['admin', 'common']       // 访问路由的角色权限\r\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\r\n * meta : {\r\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\r\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\r\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\r\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\r\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\r\n  }\r\n */\r\n\r\n// 公共路由\r\nexport const constantRoutes = [\r\n  {\r\n    path: '/redirect',\r\n    component: Layout,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: '/redirect/:path(.*)',\r\n        component: () => import('@/views/redirect')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/login',\r\n    component: () => import('@/views/login'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/register',\r\n    component: () => import('@/views/register'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/404',\r\n    component: () => import('@/views/error/404'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/401',\r\n    component: () => import('@/views/error/401'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '',\r\n    component: Layout,\r\n    redirect: 'index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/index'),\r\n        name: 'Index',\r\n        meta: { title: '首页', icon: 'dashboard', affix: true }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/user',\r\n    component: Layout,\r\n    hidden: true,\r\n    redirect: 'noredirect',\r\n    children: [\r\n      {\r\n        path: 'profile',\r\n        component: () => import('@/views/system/user/profile/index'),\r\n        name: 'Profile',\r\n        meta: { title: '个人中心', icon: 'user' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 动态路由，基于用户权限动态去加载\r\nexport const dynamicRoutes = [\r\n  {\r\n    path: '/system/user-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:user:edit'],\r\n    children: [\r\n      {\r\n        path: 'role/:userId(\\\\d+)',\r\n        component: () => import('@/views/system/user/authRole'),\r\n        name: 'AuthRole',\r\n        meta: { title: '分配角色', activeMenu: '/system/user' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/role-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:role:edit'],\r\n    children: [\r\n      {\r\n        path: 'user/:roleId(\\\\d+)',\r\n        component: () => import('@/views/system/role/authUser'),\r\n        name: 'AuthUser',\r\n        meta: { title: '分配用户', activeMenu: '/system/role' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/dict-data',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:dict:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:dictId(\\\\d+)',\r\n        component: () => import('@/views/system/dict/data'),\r\n        name: 'Data',\r\n        meta: { title: '字典数据', activeMenu: '/system/dict' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/monitor/job-log',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['monitor:job:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:jobId(\\\\d+)',\r\n        component: () => import('@/views/monitor/job/log'),\r\n        name: 'JobLog',\r\n        meta: { title: '调度日志', activeMenu: '/monitor/job' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/tool/gen-edit',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['tool:gen:edit'],\r\n    children: [\r\n      {\r\n        path: 'index/:tableId(\\\\d+)',\r\n        component: () => import('@/views/tool/gen/editTable'),\r\n        name: 'GenEdit',\r\n        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }\r\n      }\r\n    ]\r\n  },\r\n  // 小程序管理模块路由\r\n  {\r\n    path: '/miniapp',\r\n    component: Layout,\r\n    redirect: 'noredirect',\r\n    alwaysShow: true,\r\n    name: 'Miniapp',\r\n    meta: {\r\n      title: '小程序管理',\r\n      icon: 'phone',\r\n      permissions: ['miniapp:view']\r\n    },\r\n    children: [\r\n      {\r\n        path: 'statistics',\r\n        component: () => import('@/views/miniapp/statistics/index'),\r\n        name: 'MiniappStatistics',\r\n        meta: {\r\n          title: '数据统计',\r\n          icon: 'chart',\r\n          permissions: ['miniapp:statistics:view']\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  // 小程序内容管理\r\n  {\r\n    path: '/miniapp/content',\r\n    component: Layout,\r\n    redirect: 'noredirect',\r\n    alwaysShow: true,\r\n    name: 'MiniappContent',\r\n    meta: {\r\n      title: '内容管理',\r\n      icon: 'edit',\r\n      permissions: ['miniapp:content:view']\r\n    },\r\n    children: [\r\n      {\r\n        path: 'banner',\r\n        component: () => import('@/views/miniapp/content/banner/index'),\r\n        name: 'MiniappBanner',\r\n        meta: {\r\n          title: '轮播图管理',\r\n          icon: 'star',\r\n          permissions: ['miniapp:banner:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'notice',\r\n        component: () => import('@/views/miniapp/content/notice/index'),\r\n        name: 'MiniappNotice',\r\n        meta: {\r\n          title: '通知管理',\r\n          icon: 'message',\r\n          permissions: ['miniapp:notice:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'barrage',\r\n        component: () => import('@/views/miniapp/content/barrage/index'),\r\n        name: 'MiniappBarrage',\r\n        meta: {\r\n          title: '弹幕管理',\r\n          icon: 'message',\r\n          permissions: ['miniapp:barrage:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'contact',\r\n        component: () => import('@/views/miniapp/content/contact/index'),\r\n        name: 'MiniappContact',\r\n        meta: {\r\n          title: '联系人管理',\r\n          icon: 'phone',\r\n          permissions: ['miniapp:contact:list']\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  // 小程序业务管理\r\n  {\r\n    path: '/miniapp/business',\r\n    component: Layout,\r\n    redirect: 'noredirect',\r\n    alwaysShow: true,\r\n    name: 'MiniappBusiness',\r\n    meta: {\r\n      title: '业务管理',\r\n      icon: 'skill',\r\n      permissions: ['miniapp:business:view']\r\n    },\r\n    children: [\r\n      {\r\n        path: 'activity',\r\n        component: () => import('@/views/miniapp/business/activity/index'),\r\n        name: 'MiniappActivity',\r\n        meta: {\r\n          title: '精彩活动',\r\n          icon: 'star',\r\n          permissions: ['miniapp:activity:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'event',\r\n        component: () => import('@/views/miniapp/business/event/index'),\r\n        name: 'MiniappEvent',\r\n        meta: {\r\n          title: '活动报名管理',\r\n          icon: 'form',\r\n          permissions: ['miniapp:event:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'registration',\r\n        component: () => import('@/views/miniapp/registration/index'),\r\n        name: 'MiniappRegistration',\r\n        meta: {\r\n          title: '用户报名记录',\r\n          icon: 'peoples',\r\n          permissions: ['miniapp:registration:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'demand',\r\n        component: () => import('@/views/miniapp/business/demand/index'),\r\n        name: 'MiniappDemand',\r\n        meta: {\r\n          title: '需求对接',\r\n          icon: 'link',\r\n          permissions: ['miniapp:demand:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'job',\r\n        component: () => import('@/views/miniapp/business/job/index'),\r\n        name: 'MiniappJob',\r\n        meta: {\r\n          title: '招聘信息',\r\n          icon: 'user',\r\n          permissions: ['miniapp:job:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'techstar',\r\n        component: () => import('@/views/miniapp/business/techstar/index'),\r\n        name: 'MiniappTechStar',\r\n        meta: {\r\n          title: '科技之星',\r\n          icon: 'star',\r\n          permissions: ['miniapp:techstar:list']\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  // 小程序配置管理\r\n  {\r\n    path: '/miniapp/config',\r\n    component: Layout,\r\n    redirect: 'noredirect',\r\n    alwaysShow: true,\r\n    name: 'MiniappConfig',\r\n    meta: {\r\n      title: '配置管理',\r\n      icon: 'system',\r\n      permissions: ['miniapp:config:view']\r\n    },\r\n    children: [\r\n      {\r\n        path: 'industry',\r\n        component: () => import('@/views/miniapp/industry/index'),\r\n        name: 'MiniappIndustry',\r\n        meta: {\r\n          title: '行业管理',\r\n          icon: 'tree',\r\n          permissions: ['miniapp:industry:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'jobType',\r\n        component: () => import('@/views/miniapp/jobType/index'),\r\n        name: 'MiniappJobType',\r\n        meta: {\r\n          title: '职位类型管理',\r\n          icon: 'post',\r\n          permissions: ['miniapp:jobType:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'enterprise',\r\n        component: () => import('@/views/miniapp/config/enterprise/index'),\r\n        name: 'MiniappEnterprise',\r\n        meta: {\r\n          title: '企业管理',\r\n          icon: 'company',\r\n          permissions: ['miniapp:enterprise:list']\r\n        }\r\n      },\r\n      {\r\n        path: 'page',\r\n        component: () => import('@/views/miniapp/config/page/index'),\r\n        name: 'MiniappPage',\r\n        meta: {\r\n          title: '页面内容',\r\n          icon: 'documentation',\r\n          permissions: ['miniapp:page:list']\r\n        }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 防止连续点击多次路由报错\r\nlet routerPush = Router.prototype.push\r\nlet routerReplace = Router.prototype.replace\r\n// push\r\nRouter.prototype.push = function push(location) {\r\n  return routerPush.call(this, location).catch(err => err)\r\n}\r\n// replace\r\nRouter.prototype.replace = function push(location) {\r\n  return routerReplace.call(this, location).catch(err => err)\r\n}\r\n\r\nexport default new Router({\r\n  mode: 'history', // 去掉url中的#\r\n  scrollBehavior: () => ({ y: 0 }),\r\n  routes: constantRoutes\r\n})\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,CAC5B;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;IAAA;EAAA,CAAC;EAC3CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,OAAO;EACjBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;MAAA;IAAA,CAAC;IACxCmB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK;EACtD,CAAC;AAEL,CAAC,EACD;EACEf,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZO,QAAQ,EAAE,YAAY;EACtBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC,CACF;;AAED;AACO,IAAME,aAAa,GAAAjB,OAAA,CAAAiB,aAAA,GAAG,CAC3B;EACEhB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,eAAe,CAAC;EAC9Bb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEK,UAAU,EAAE;IAAY;EACnD,CAAC;AAEL,CAAC;AACD;AACA;EACElB,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,YAAY;EACtBS,UAAU,EAAE,IAAI;EAChBR,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbG,WAAW,EAAE,CAAC,cAAc;EAC9B,CAAC;EACDb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kCAAkC;MAAA;IAAA,CAAC;IAC3DmB,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,OAAO;MACbG,WAAW,EAAE,CAAC,yBAAyB;IACzC;EACF,CAAC;AAEL,CAAC;AACD;AACA;EACEjB,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,YAAY;EACtBS,UAAU,EAAE,IAAI;EAChBR,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,MAAM;IACZG,WAAW,EAAE,CAAC,sBAAsB;EACtC,CAAC;EACDb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sCAAsC;MAAA;IAAA,CAAC;IAC/DmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MACJC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,qBAAqB;IACrC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sCAAsC;MAAA;IAAA,CAAC;IAC/DmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,SAAS;MACfG,WAAW,EAAE,CAAC,qBAAqB;IACrC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uCAAuC;MAAA;IAAA,CAAC;IAChEmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,SAAS;MACfG,WAAW,EAAE,CAAC,sBAAsB;IACtC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uCAAuC;MAAA;IAAA,CAAC;IAChEmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MACJC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,OAAO;MACbG,WAAW,EAAE,CAAC,sBAAsB;IACtC;EACF,CAAC;AAEL,CAAC;AACD;AACA;EACEjB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,YAAY;EACtBS,UAAU,EAAE,IAAI;EAChBR,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,OAAO;IACbG,WAAW,EAAE,CAAC,uBAAuB;EACvC,CAAC;EACDb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yCAAyC;MAAA;IAAA,CAAC;IAClEmB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,uBAAuB;IACvC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sCAAsC;MAAA;IAAA,CAAC;IAC/DmB,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MACJC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,oBAAoB;IACpC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,oCAAoC;MAAA;IAAA,CAAC;IAC7DmB,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE;MACJC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,SAAS;MACfG,WAAW,EAAE,CAAC,2BAA2B;IAC3C;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uCAAuC;MAAA;IAAA,CAAC;IAChEmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,qBAAqB;IACrC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,oCAAoC;MAAA;IAAA,CAAC;IAC7DmB,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,kBAAkB;IAClC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yCAAyC;MAAA;IAAA,CAAC;IAClEmB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,uBAAuB;IACvC;EACF,CAAC;AAEL,CAAC;AACD;AACA;EACEjB,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,YAAY;EACtBS,UAAU,EAAE,IAAI;EAChBR,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdG,WAAW,EAAE,CAAC,qBAAqB;EACrC,CAAC;EACDb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,gCAAgC;MAAA;IAAA,CAAC;IACzDmB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,uBAAuB;IACvC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MACJC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,MAAM;MACZG,WAAW,EAAE,CAAC,sBAAsB;IACtC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yCAAyC;MAAA;IAAA,CAAC;IAClEmB,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,SAAS;MACfG,WAAW,EAAE,CAAC,yBAAyB;IACzC;EACF,CAAC,EACD;IACEjB,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,eAAe;MACrBG,WAAW,EAAE,CAAC,mBAAmB;IACnC;EACF,CAAC;AAEL,CAAC,CACF;;AAED;AACA,IAAIG,UAAU,GAAGvB,kBAAM,CAACwB,SAAS,CAACC,IAAI;AACtC,IAAIC,aAAa,GAAG1B,kBAAM,CAACwB,SAAS,CAACG,OAAO;AAC5C;AACA3B,kBAAM,CAACwB,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACG,QAAQ,EAAE;EAC9C,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC1D,CAAC;AACD;AACA/B,kBAAM,CAACwB,SAAS,CAACG,OAAO,GAAG,SAASF,IAAIA,CAACG,QAAQ,EAAE;EACjD,OAAOF,aAAa,CAACG,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC7D,CAAC;AAAA,IAAAC,QAAA,GAAA9B,OAAA,CAAAU,OAAA,GAEc,IAAIZ,kBAAM,CAAC;EACxBiC,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAEnC;AACV,CAAC,CAAC", "ignoreList": []}]}