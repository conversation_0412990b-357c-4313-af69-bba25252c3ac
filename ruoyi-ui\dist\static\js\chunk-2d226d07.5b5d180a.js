(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d226d07"],{e9eb:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入通知标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),n("el-form-item",{attrs:{label:"内容",prop:"content"}},[n("el-input",{attrs:{placeholder:"请输入通知内容",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.content,callback:function(t){e.$set(e.queryParams,"content",t)},expression:"queryParams.content"}})],1),n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:notice:add"],expression:"['miniapp:notice:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:notice:edit"],expression:"['miniapp:notice:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:notice:remove"],expression:"['miniapp:notice:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:notice:export"],expression:"['miniapp:notice:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.noticeList,"row-key":"noticeId"},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"通知ID",align:"center",prop:"noticeId",width:"100"}}),n("el-table-column",{attrs:{label:"通知标题",align:"center",prop:"title","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{label:"通知内容",align:"center",prop:"content","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-input-number",{staticStyle:{width:"80px"},attrs:{min:0,size:"mini",controls:!1},on:{change:function(n){return e.handleSortChange(t.row)}},model:{value:t.row.sortOrder,callback:function(n){e.$set(t.row,"sortOrder",n)},expression:"scope.row.sortOrder"}})]}}])}),n("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),n("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:notice:edit"],expression:"['miniapp:notice:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:notice:remove"],expression:"['miniapp:notice:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"通知标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入通知标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),n("el-form-item",{attrs:{label:"通知内容",prop:"content"}},[n("editor",{attrs:{"min-height":200,placeholder:"请输入通知内容"},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1),n("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[n("el-input-number",{attrs:{min:0,placeholder:"数字越小越靠前"},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1),n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return n("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),n("el-form-item",{attrs:{label:"备注",prop:"remark"}},[n("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],r=n("5530"),o=(n("d81d"),n("d3b7"),n("0643"),n("a573"),n("b775"));function l(e){return Object(o["a"])({url:"/miniapp/notice/list",method:"post",data:e})}function s(e){return Object(o["a"])({url:"/miniapp/notice/getInfo",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/miniapp/notice/add",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/miniapp/notice/edit",method:"post",data:e})}function m(e){return Object(o["a"])({url:"/miniapp/notice/remove",method:"post",data:e})}var p={name:"MiniNotice",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,noticeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,content:null,status:null},form:{},rules:{title:[{required:!0,message:"通知标题不能为空",trigger:"blur"}],content:[{required:!0,message:"通知内容不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.noticeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={noticeId:null,title:null,content:null,sortOrder:0,status:"0",remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.noticeId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加通知"},handleUpdate:function(e){var t=this;this.reset();var n=e.noticeId||this.ids;s(n).then((function(e){t.form=e.data,t.open=!0,t.title="修改通知"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.noticeId?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.noticeId||this.ids;this.$modal.confirm('是否确认删除通知编号为"'+n+'"的数据项？').then((function(){return m(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/notice/export",Object(r["a"])({},this.queryParams),"通知数据_".concat((new Date).getTime(),".xlsx"))},handleSortChange:function(e){var t=this;u(e).then((function(e){t.$modal.msgSuccess("排序修改成功"),t.getList()}))}}},d=p,h=n("2877"),f=Object(h["a"])(d,a,i,!1,null,null,null);t["default"]=f.exports}}]);