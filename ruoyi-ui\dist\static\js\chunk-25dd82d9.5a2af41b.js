(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-25dd82d9"],{"5be5":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"活动名称",prop:"eventName"}},[a("el-input",{attrs:{placeholder:"请输入活动名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.eventName,callback:function(t){e.$set(e.queryParams,"eventName",t)},expression:"queryParams.eventName"}})],1),a("el-form-item",{attrs:{label:"用户名称",prop:"userName"}},[a("el-input",{attrs:{placeholder:"请输入用户名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),a("el-form-item",{attrs:{label:"用户手机号",prop:"userPhone"}},[a("el-input",{attrs:{placeholder:"请输入用户手机号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userPhone,callback:function(t){e.$set(e.queryParams,"userPhone",t)},expression:"queryParams.userPhone"}})],1),a("el-form-item",{attrs:{label:"报名时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeRegistrationTime,callback:function(t){e.daterangeRegistrationTime=t},expression:"daterangeRegistrationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:registration:remove"],expression:"['miniapp:registration:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:registration:export"],expression:"['miniapp:registration:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.registrationList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"报名ID",align:"center",prop:"registrationId"}}),a("el-table-column",{attrs:{label:"活动名称",align:"center",prop:"eventName"}}),a("el-table-column",{attrs:{label:"用户名称",align:"center",prop:"userName"}}),a("el-table-column",{attrs:{label:"用户手机号",align:"center",prop:"userPhone"}}),a("el-table-column",{attrs:{label:"报名时间",align:"center",prop:"registrationTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.registrationTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"报名表单数据",align:"center",prop:"formData","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleViewFormData(t.row)}}},[e._v("查看表单")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:registration:remove"],expression:"['miniapp:registration:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"报名表单数据",visible:e.formDataVisible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.formDataVisible=t}}},[e.formDataList&&e.formDataList.length>0?a("div",{staticClass:"form-data-container"},[a("el-descriptions",{attrs:{column:1,border:""}},e._l(e.formDataList,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.label,"label-style":{width:"120px",fontWeight:"bold"}}},["textarea"===t.type?[a("div",{staticClass:"textarea-content"},[e._v(e._s(t.value||"未填写"))])]:"select"===t.type||"radio"===t.type?[a("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(e._s(t.value||"未选择"))])]:"tel"===t.type?[a("span",{staticClass:"phone-number"},[e._v(e._s(t.value||"未填写"))])]:"email"===t.type?[a("span",{staticClass:"email-address"},[e._v(e._s(t.value||"未填写"))])]:[a("span",[e._v(e._s(t.value||"未填写"))])],t.required?a("el-tag",{staticStyle:{"margin-left":"8px"},attrs:{type:"danger",size:"mini"}},[e._v("必填")]):e._e()],2)})),1)],1):a("div",{staticClass:"no-data"},[a("el-empty",{attrs:{description:"暂无表单数据"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.formDataVisible=!1}}},[e._v("关 闭")])],1)])],1)},r=[],n=a("5530"),s=(a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("0643"),a("a573"),a("b775"));function l(e){var t=Object(n["a"])(Object(n["a"])({},e),{},{eventType:"activity"});return Object(s["a"])({url:"/miniapp/registration/list",method:"get",params:t})}function o(e){return Object(s["a"])({url:"/miniapp/registration/"+e,method:"get"})}function u(e){return Object(s["a"])({url:"/miniapp/registration",method:"post",data:e})}function m(e){return Object(s["a"])({url:"/miniapp/registration",method:"put",data:e})}function c(e){return Object(s["a"])({url:"/miniapp/registration/"+e,method:"delete"})}var d={name:"Registration",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,registrationList:[],title:"",open:!1,formDataVisible:!1,formDataText:"",formDataList:[],daterangeRegistrationTime:[],queryParams:{pageNum:1,pageSize:10,eventId:null,userId:null,eventName:null,userName:null,userPhone:null,registrationTime:null},form:{},rules:{eventId:[{required:!0,message:"活动ID不能为空",trigger:"blur"}],userId:[{required:!0,message:"用户ID不能为空",trigger:"blur"}],registrationTime:[{required:!0,message:"报名时间不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.params={},null!=this.daterangeRegistrationTime&&""!=this.daterangeRegistrationTime&&(this.queryParams.params["beginRegistrationTime"]=this.daterangeRegistrationTime[0],this.queryParams.params["endRegistrationTime"]=this.daterangeRegistrationTime[1]),l(this.queryParams).then((function(t){e.registrationList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={registrationId:null,eventId:null,userId:null,formData:null,registrationTime:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.daterangeRegistrationTime=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.registrationId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加用户报名记录"},handleUpdate:function(e){var t=this;this.reset();var a=e.registrationId||this.ids;o(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改用户报名记录"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.registrationId?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.registrationId||this.ids;this.$modal.confirm('是否确认删除用户报名记录编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/registration/export",Object(n["a"])({},this.queryParams),"registration_".concat((new Date).getTime(),".xlsx"))},handleViewFormData:function(e){if(this.formDataText=e.formData?JSON.stringify(JSON.parse(e.formData),null,2):"无表单数据",e.formData)try{this.formDataList=JSON.parse(e.formData)}catch(t){console.error("解析表单数据失败:",t),this.formDataList=[]}else this.formDataList=[];this.formDataVisible=!0}}},p=d,h=(a("6395"),a("2877")),g=Object(h["a"])(p,i,r,!1,null,"1a057aba",null);t["default"]=g.exports},6395:function(e,t,a){"use strict";a("f051")},f051:function(e,t,a){}}]);