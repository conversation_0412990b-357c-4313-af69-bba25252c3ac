{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-e6dea316\"],{\"75cd\":function(e,t,a){},\"8cd4\":function(e,t,a){\"use strict\";a.d(t,\"e\",(function(){return l})),a.d(t,\"c\",(function(){return o})),a.d(t,\"a\",(function(){return n})),a.d(t,\"f\",(function(){return r})),a.d(t,\"b\",(function(){return s})),a.d(t,\"d\",(function(){return c}));var i=a(\"b775\");function l(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/list\",method:\"post\",data:e})}function o(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/getInfo\",method:\"post\",data:e})}function n(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/add\",method:\"post\",data:e})}function r(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/edit\",method:\"post\",data:e})}function s(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/remove\",method:\"post\",data:e})}function c(){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/app/getEnabledList\",method:\"post\"})}},aca2:function(e,t,a){\"use strict\";a(\"75cd\")},bd9e:function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"类型名称\",prop:\"categoryName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入类型名称\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.categoryName,callback:function(t){e.$set(e.queryParams,\"categoryName\",t)},expression:\"queryParams.categoryName\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demandcategory:edit\"],expression:\"['miniapp:demandcategory:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demandcategory:export\"],expression:\"['miniapp:demandcategory:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.categoryList,\"row-key\":\"categoryId\"},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"类型ID\",align:\"center\",prop:\"categoryId\",width:\"100\"}}),a(\"el-table-column\",{attrs:{label:\"类型名称\",align:\"center\",prop:\"categoryName\"}}),a(\"el-table-column\",{attrs:{label:\"类型标识\",align:\"center\",prop:\"categoryCode\",width:\"120\"}}),a(\"el-table-column\",{attrs:{label:\"简称\",align:\"center\",prop:\"categoryShortName\",width:\"120\"}}),a(\"el-table-column\",{attrs:{label:\"图标\",align:\"center\",prop:\"categoryIcon\",width:\"80\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[t.row.categoryIcon?a(\"img\",{staticClass:\"category-icon\",attrs:{src:t.row.categoryIcon,alt:\"类型图标\"}}):a(\"span\",{staticClass:\"no-icon\"},[e._v(\"无图标\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"类型描述\",align:\"center\",prop:\"categoryDesc\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"排序\",align:\"center\",prop:\"sortOrder\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-input-number\",{staticStyle:{width:\"80px\"},attrs:{min:0,size:\"mini\",controls:!1},on:{change:function(a){return e.handleSortChange(t.row)}},model:{value:t.row.sortOrder,callback:function(a){e.$set(t.row,\"sortOrder\",a)},expression:\"scope.row.sortOrder\"}})]}}])}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"dict-tag\",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),a(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demandcategory:edit\"],expression:\"['miniapp:demandcategory:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(\"修改\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demandcategory:edit\"],expression:\"['miniapp:demandcategory:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-setting\"},on:{click:function(a){return e.handleFormConfig(t.row)}}},[e._v(\"表单配置\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total > 0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"900px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\"}},[a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"类型名称\",prop:\"categoryName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入类型名称\"},model:{value:e.form.categoryName,callback:function(t){e.$set(e.form,\"categoryName\",t)},expression:\"form.categoryName\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"类型标识\",prop:\"categoryCode\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入类型标识代码，如：tech、business\",disabled:null!=e.form.categoryId},model:{value:e.form.categoryCode,callback:function(t){e.$set(e.form,\"categoryCode\",t)},expression:\"form.categoryCode\"}}),a(\"div\",{staticClass:\"form-tip\"},[a(\"p\",[e._v(\"• 用于标识需求类型的唯一代码\")]),a(\"p\",[e._v(\"• 建议格式：tech、business、service等\")]),null==e.form.categoryId?a(\"p\",[e._v(\"• 一旦设置后不可修改\")]):a(\"p\",{staticStyle:{color:\"#f56c6c\"}},[e._v(\"• 类型标识不可修改\")])])],1)],1)],1),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"类型简称\",prop:\"categoryShortName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入类型名称简称\"},model:{value:e.form.categoryShortName,callback:function(t){e.$set(e.form,\"categoryShortName\",t)},expression:\"form.categoryShortName\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"排序\",prop:\"sortOrder\"}},[a(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{min:0,placeholder:\"数字越小越靠前\"},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,\"sortOrder\",t)},expression:\"form.sortOrder\"}})],1)],1)],1),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"类型图标\",prop:\"categoryIcon\"}},[a(\"ImageUpload\",{attrs:{limit:1,fileSize:2,isShowTip:!0},model:{value:e.form.categoryIcon,callback:function(t){e.$set(e.form,\"categoryIcon\",t)},expression:\"form.categoryIcon\"}}),a(\"div\",{staticClass:\"form-tip\"},[a(\"p\",[e._v(\"• 支持 jpg、png、gif 格式\")]),a(\"p\",[e._v(\"• 文件大小不超过 2MB\")]),a(\"p\",[e._v(\"• 建议尺寸 32x32 像素，保证清晰度\")])])],1)],1)],1),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"类型描述\",prop:\"categoryDesc\"}},[a(\"Editor\",{attrs:{\"min-height\":200,height:300,placeholder:\"请输入类型描述...\"},model:{value:e.form.categoryDesc,callback:function(t){e.$set(e.form,\"categoryDesc\",t)},expression:\"form.categoryDesc\"}})],1)],1)],1),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},e._l(e.dict.type.sys_normal_disable,(function(t){return a(\"el-radio\",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\",rows:3},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1)],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1),a(\"el-dialog\",{attrs:{title:\"表单字段配置\",visible:e.formConfigOpen,width:\"1400px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.formConfigOpen=t}}},[a(\"div\",{staticClass:\"form-config-container\"},[a(\"div\",{staticClass:\"config-area\"},[a(\"div\",{staticClass:\"config-header\"},[a(\"h4\",[e._v(\"字段配置\")]),a(\"div\",{staticClass:\"header-actions\"},[a(\"el-button\",{attrs:{type:\"success\",size:\"small\",icon:\"el-icon-folder-add\"},on:{click:e.addFormModule}},[e._v(\"添加模块\")]),a(\"el-button\",{attrs:{type:\"primary\",size:\"small\",icon:\"el-icon-plus\"},on:{click:e.addFormField}},[e._v(\"添加字段\")])],1)]),a(\"div\",{staticClass:\"template-section\"},[a(\"el-select\",{attrs:{placeholder:\"选择预设模板\",size:\"small\"},on:{change:e.applyTemplate},model:{value:e.selectedTemplate,callback:function(t){e.selectedTemplate=t},expression:\"selectedTemplate\"}},[a(\"el-option\",{attrs:{label:\"基础需求模板\",value:\"basic\"}}),a(\"el-option\",{attrs:{label:\"技术需求模板\",value:\"tech\"}}),a(\"el-option\",{attrs:{label:\"商务合作模板\",value:\"business\"}})],1)],1),a(\"div\",{staticClass:\"modules-list\"},[e._l(e.formModulesList,(function(t,i){return a(\"div\",{key:i,staticClass:\"module-item\"},[a(\"el-card\",{staticClass:\"module-card\",attrs:{shadow:\"hover\"}},[a(\"div\",{staticClass:\"module-header\"},[a(\"div\",{staticClass:\"module-title-section\"},[a(\"div\",{staticClass:\"module-icon-section\"},[a(\"div\",{staticClass:\"module-icon-display\",on:{click:function(t){return e.openIconUploader(i)}}},[t.icon?a(\"img\",{staticClass:\"custom-icon\",attrs:{src:t.icon,alt:\"模块图标\"}}):a(\"div\",{staticClass:\"default-icon-placeholder\"},[a(\"i\",{staticClass:\"el-icon-plus\"}),a(\"span\",[e._v(\"上传图标\")])])]),t.icon?a(\"el-dropdown\",{attrs:{trigger:\"click\"},on:{command:function(t){return e.handleIconCommand(t,i)}}},[a(\"el-button\",{staticClass:\"icon-edit-btn\",attrs:{type:\"text\",size:\"mini\"}},[a(\"i\",{staticClass:\"el-icon-edit\"})]),a(\"el-dropdown-menu\",{attrs:{slot:\"dropdown\"},slot:\"dropdown\"},[a(\"el-dropdown-item\",{attrs:{command:\"upload\"}},[e._v(\"重新上传\")]),a(\"el-dropdown-item\",{attrs:{command:\"remove\"}},[e._v(\"移除图标\")])],1)],1):e._e()],1),a(\"el-input\",{staticClass:\"module-name-input\",attrs:{placeholder:\"请输入模块名称\",size:\"small\"},on:{blur:function(a){return e.validateModuleName(t)}},model:{value:t.name,callback:function(a){e.$set(t,\"name\",a)},expression:\"module.name\"}})],1),a(\"div\",{staticClass:\"module-actions\"},[a(\"el-button\",{attrs:{type:\"text\",size:\"mini\",disabled:0===i},on:{click:function(t){return e.moveModuleUp(i)}}},[e._v(\"上移\")]),a(\"el-button\",{attrs:{type:\"text\",size:\"mini\",disabled:i===e.formModulesList.length-1},on:{click:function(t){return e.moveModuleDown(i)}}},[e._v(\"下移\")]),a(\"el-button\",{attrs:{type:\"text\",size:\"mini\",icon:\"el-icon-plus\"},on:{click:function(t){return e.addFieldToModule(i)}}},[e._v(\"添加字段\")]),a(\"el-button\",{staticStyle:{color:\"#f56c6c\"},attrs:{type:\"text\",size:\"mini\"},on:{click:function(t){return e.removeModule(i)}}},[e._v(\"删除模块\")])],1)]),t.fields&&t.fields.length>0?a(\"div\",{staticClass:\"fields-list\"},e._l(t.fields,(function(l,o){return a(\"div\",{key:o,staticClass:\"field-item\"},[a(\"el-card\",{staticClass:\"field-card\",attrs:{shadow:\"hover\"}},[a(\"div\",{staticClass:\"field-header\"},[a(\"span\",{staticClass:\"field-title\"},[e._v(\" \"+e._s(l.label||\"未命名字段\")+\" \"),l.hidden?a(\"el-tag\",{staticStyle:{\"margin-left\":\"8px\"},attrs:{size:\"mini\",type:\"info\"}},[e._v(\"隐藏\")]):e._e()],1),a(\"div\",{staticClass:\"field-actions\"},[a(\"el-button\",{attrs:{type:\"text\",size:\"mini\",disabled:0===o},on:{click:function(t){return e.moveFieldUpInModule(i,o)}}},[e._v(\"上移\")]),a(\"el-button\",{attrs:{type:\"text\",size:\"mini\",disabled:o===t.fields.length-1},on:{click:function(t){return e.moveFieldDownInModule(i,o)}}},[e._v(\"下移\")]),a(\"el-button\",{staticStyle:{color:\"#f56c6c\"},attrs:{type:\"text\",size:\"mini\"},on:{click:function(t){return e.removeFieldFromModule(i,o)}}},[e._v(\"删除\")])],1)]),a(\"el-form\",{attrs:{model:l,\"label-width\":\"80px\",size:\"small\"}},[a(\"el-form-item\",{attrs:{label:\"字段标签\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入字段标签\"},on:{input:function(t){return e.generateFieldName(l)}},model:{value:l.label,callback:function(t){e.$set(l,\"label\",t)},expression:\"field.label\"}})],1),a(\"el-form-item\",{attrs:{label:\"字段类型\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择字段类型\"},on:{change:function(t){return e.onFieldTypeChange(l)}},model:{value:l.type,callback:function(t){e.$set(l,\"type\",t)},expression:\"field.type\"}},[a(\"el-option\",{attrs:{label:\"文本输入\",value:\"input\"}}),a(\"el-option\",{attrs:{label:\"多行文本\",value:\"textarea\"}}),a(\"el-option\",{attrs:{label:\"数字输入\",value:\"number\"}}),a(\"el-option\",{attrs:{label:\"电话号码\",value:\"tel\"}}),a(\"el-option\",{attrs:{label:\"邮箱地址\",value:\"email\"}}),a(\"el-option\",{attrs:{label:\"单选框\",value:\"radio\"}}),a(\"el-option\",{attrs:{label:\"多选框\",value:\"checkbox\"}}),a(\"el-option\",{attrs:{label:\"下拉选择\",value:\"select\"}}),a(\"el-option\",{attrs:{label:\"日期选择\",value:\"date\"}}),a(\"el-option\",{attrs:{label:\"时间选择\",value:\"time\"}}),a(\"el-option\",{attrs:{label:\"文件上传\",value:\"file\"}}),a(\"el-option\",{attrs:{label:\"静态展示\",value:\"static\"}})],1)],1),\"static\"!==l.type?a(\"el-form-item\",{attrs:{label:\"提示文字\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入提示文字，如：请输入姓名\"},model:{value:l.placeholder,callback:function(t){e.$set(l,\"placeholder\",t)},expression:\"field.placeholder\"}})],1):e._e(),\"static\"===l.type?a(\"el-form-item\",{attrs:{label:\"显示内容\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入要显示的静态内容\",rows:3},model:{value:l.staticContent,callback:function(t){e.$set(l,\"staticContent\",t)},expression:\"field.staticContent\"}})],1):e._e(),\"static\"!==l.type?a(\"el-form-item\",{attrs:{label:\"是否必填\"}},[a(\"el-switch\",{model:{value:l.required,callback:function(t){e.$set(l,\"required\",t)},expression:\"field.required\"}})],1):e._e(),a(\"el-form-item\",{attrs:{label:\"是否隐藏\"}},[a(\"el-switch\",{on:{change:function(t){return e.onFieldHiddenChange(l)}},model:{value:l.hidden,callback:function(t){e.$set(l,\"hidden\",t)},expression:\"field.hidden\"}}),l.hidden?a(\"div\",{staticClass:\"field-tip\"},[a(\"i\",{staticClass:\"el-icon-info\"}),a(\"span\",[e._v(\"隐藏的字段在小程序端不会显示给用户\")])]):e._e()],1),[\"radio\",\"checkbox\",\"select\"].includes(l.type)?a(\"el-form-item\",{attrs:{label:\"选项配置\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入选项，用逗号分隔\",rows:2},model:{value:l.options,callback:function(t){e.$set(l,\"options\",t)},expression:\"field.options\"}})],1):e._e()],1)],1)],1)})),0):a(\"div\",{staticClass:\"empty-module\"},[a(\"el-empty\",{attrs:{description:\"暂无字段\",\"image-size\":60}},[a(\"el-button\",{attrs:{type:\"primary\",size:\"small\"},on:{click:function(t){return e.addFieldToModule(i)}}},[e._v(\"添加字段\")])],1)],1)])],1)})),0===e.formModulesList.length?a(\"div\",{staticClass:\"empty-modules\"},[a(\"el-empty\",{attrs:{description:\"暂无模块\",\"image-size\":80}},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.addFormModule}},[e._v(\"创建第一个模块\")])],1)],1):e._e()],2)]),a(\"div\",{staticClass:\"preview-area\"},[a(\"div\",{staticClass:\"preview-header\"},[a(\"h4\",[e._v(\"表单预览\")]),a(\"el-button\",{attrs:{type:\"success\",size:\"small\"},on:{click:e.previewForm}},[e._v(\"预览表单\")])],1),a(\"div\",{staticClass:\"preview-content\"},[e._l(e.formModulesList,(function(t,i){return a(\"div\",{key:i,staticClass:\"preview-module\"},[a(\"div\",{staticClass:\"preview-module-header\"},[a(\"div\",{staticClass:\"preview-module-title\"},[t.icon?a(\"img\",{staticClass:\"preview-module-icon-img\",attrs:{src:t.icon,alt:\"模块图标\"}}):a(\"i\",{staticClass:\"el-icon-folder-opened preview-module-icon\"}),a(\"h5\",[e._v(e._s(t.name||\"未命名模块\"))])])]),t.fields&&t.fields.length>0?a(\"el-form\",{attrs:{\"label-width\":\"100px\",size:\"small\"}},e._l(t.fields,(function(t){return a(\"el-form-item\",{key:t.name,class:{\"hidden-field\":t.hidden},attrs:{label:t.label,required:t.required&&\"static\"!==t.type}},[t.hidden?a(\"div\",{staticClass:\"hidden-field-indicator\"},[a(\"el-tag\",{attrs:{size:\"mini\",type:\"info\"}},[e._v(\"隐藏字段\")])],1):e._e(),\"static\"===t.type?a(\"div\",{staticClass:\"static-content\"},[e._v(\" \"+e._s(t.staticContent||\"暂无内容\")+\" \")]):\"input\"===t.type?a(\"el-input\",{attrs:{placeholder:t.placeholder||\"请输入\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"textarea\"===t.type?a(\"el-input\",{attrs:{type:\"textarea\",placeholder:t.placeholder||\"请输入\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"number\"===t.type?a(\"el-input-number\",{attrs:{placeholder:t.placeholder||\"请输入数字\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"tel\"===t.type?a(\"el-input\",{attrs:{placeholder:t.placeholder||\"请输入电话号码\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"email\"===t.type?a(\"el-input\",{attrs:{placeholder:t.placeholder||\"请输入邮箱地址\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"radio\"===t.type?a(\"el-radio-group\",{attrs:{disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}},e._l(e.getFieldOptions(t),(function(t){return a(\"el-radio\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),1):\"checkbox\"===t.type?a(\"el-checkbox-group\",{attrs:{disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}},e._l(e.getFieldOptions(t),(function(t){return a(\"el-checkbox\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),1):\"select\"===t.type?a(\"el-select\",{attrs:{placeholder:t.placeholder||\"请选择\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}},e._l(e.getFieldOptions(t),(function(e){return a(\"el-option\",{key:e,attrs:{label:e,value:e}})})),1):\"date\"===t.type?a(\"el-date-picker\",{attrs:{type:\"date\",placeholder:t.placeholder||\"请选择日期\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"time\"===t.type?a(\"el-time-picker\",{attrs:{placeholder:t.placeholder||\"请选择时间\",disabled:\"\"},model:{value:e.previewData[t.name],callback:function(a){e.$set(e.previewData,t.name,a)},expression:\"previewData[field.name]\"}}):\"file\"===t.type?a(\"el-upload\",{attrs:{action:\"#\",disabled:\"\"}},[a(\"el-button\",{attrs:{size:\"small\",type:\"primary\",disabled:\"\"}},[e._v(e._s(t.placeholder||\"点击上传\"))])],1):e._e()],1)})),1):a(\"div\",{staticClass:\"preview-empty-module\"},[a(\"span\",[e._v(\"该模块暂无字段\")])])],1)})),0===e.formModulesList.length?a(\"div\",{staticClass:\"preview-empty\"},[a(\"el-empty\",{attrs:{description:\"暂无表单配置\",\"image-size\":60}})],1):e._e()],2)])]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.saveFormConfig}},[e._v(\"保存配置\")]),a(\"el-button\",{on:{click:e.cancelFormConfig}},[e._v(\"取 消\")])],1)]),a(\"el-dialog\",{attrs:{title:\"表单预览\",visible:e.previewOpen,width:\"800px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.previewOpen=t}}},[a(\"div\",{staticClass:\"preview-dialog-content\"},[e._l(e.formModulesList,(function(t,i){return a(\"div\",{key:i,staticClass:\"preview-dialog-module\"},[a(\"div\",{staticClass:\"preview-dialog-module-header\"},[a(\"div\",{staticClass:\"preview-dialog-module-title\"},[t.icon?a(\"img\",{staticClass:\"preview-dialog-module-icon-img\",attrs:{src:t.icon,alt:\"模块图标\"}}):a(\"i\",{staticClass:\"el-icon-folder-opened preview-dialog-module-icon\"}),a(\"h4\",[e._v(e._s(t.name||\"未命名模块\"))])])]),t.fields&&t.fields.length>0?a(\"el-form\",{attrs:{\"label-width\":\"100px\"}},e._l(t.fields,(function(t){return a(\"el-form-item\",{key:t.name,class:{\"hidden-field\":t.hidden},attrs:{label:t.label,required:t.required&&\"static\"!==t.type}},[t.hidden?a(\"div\",{staticClass:\"hidden-field-indicator\"},[a(\"el-tag\",{attrs:{size:\"mini\",type:\"info\"}},[e._v(\"隐藏字段\")])],1):e._e(),\"static\"===t.type?a(\"div\",{staticClass:\"static-content\"},[e._v(\" \"+e._s(t.staticContent||\"暂无内容\")+\" \")]):\"input\"===t.type?a(\"el-input\",{attrs:{placeholder:t.placeholder||\"请输入\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"textarea\"===t.type?a(\"el-input\",{attrs:{type:\"textarea\",placeholder:t.placeholder||\"请输入\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"number\"===t.type?a(\"el-input-number\",{attrs:{placeholder:t.placeholder||\"请输入数字\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"tel\"===t.type?a(\"el-input\",{attrs:{placeholder:t.placeholder||\"请输入电话号码\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"email\"===t.type?a(\"el-input\",{attrs:{placeholder:t.placeholder||\"请输入邮箱地址\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"radio\"===t.type?a(\"el-radio-group\",{model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}},e._l(e.getFieldOptions(t),(function(t){return a(\"el-radio\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),1):\"checkbox\"===t.type?a(\"el-checkbox-group\",{model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}},e._l(e.getFieldOptions(t),(function(t){return a(\"el-checkbox\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),1):\"select\"===t.type?a(\"el-select\",{attrs:{placeholder:t.placeholder||\"请选择\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}},e._l(e.getFieldOptions(t),(function(e){return a(\"el-option\",{key:e,attrs:{label:e,value:e}})})),1):\"date\"===t.type?a(\"el-date-picker\",{attrs:{type:\"date\",placeholder:t.placeholder||\"请选择日期\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"time\"===t.type?a(\"el-time-picker\",{attrs:{placeholder:t.placeholder||\"请选择时间\"},model:{value:e.previewDialogData[t.name],callback:function(a){e.$set(e.previewDialogData,t.name,a)},expression:\"previewDialogData[field.name]\"}}):\"file\"===t.type?a(\"el-upload\",{attrs:{action:\"#\"}},[a(\"el-button\",{attrs:{size:\"small\",type:\"primary\"}},[e._v(e._s(t.placeholder||\"点击上传\"))])],1):e._e()],1)})),1):a(\"div\",{staticClass:\"preview-dialog-empty-module\"},[a(\"span\",[e._v(\"该模块暂无字段\")])])],1)})),0===e.formModulesList.length?a(\"div\",{staticClass:\"preview-dialog-empty\"},[a(\"el-empty\",{attrs:{description:\"暂无表单配置\",\"image-size\":60}})],1):e._e()],2),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(t){e.previewOpen=!1}}},[e._v(\"关 闭\")])],1)]),a(\"el-dialog\",{attrs:{title:\"上传模块图标\",visible:e.iconUploaderOpen,width:\"500px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.iconUploaderOpen=t}}},[a(\"div\",{staticClass:\"icon-uploader-content\"},[a(\"div\",{staticClass:\"upload-section\"},[a(\"h4\",[e._v(\"上传自定义图标\")]),a(\"ImageUpload\",{attrs:{limit:1,fileSize:2,isShowTip:!0},model:{value:e.uploadedIcon,callback:function(t){e.uploadedIcon=t},expression:\"uploadedIcon\"}}),a(\"div\",{staticClass:\"upload-tips\"},[a(\"p\",[e._v(\"• 支持 jpg、png、gif 格式\")]),a(\"p\",[e._v(\"• 文件大小不超过 2MB\")]),a(\"p\",[e._v(\"• 建议尺寸 32x32 像素，保证清晰度\")]),a(\"p\",[e._v(\"• 建议使用透明背景的PNG格式\")])])],1)]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.cancelIconUpload}},[e._v(\"取 消\")]),a(\"el-button\",{attrs:{type:\"primary\",disabled:!e.uploadedIcon},on:{click:e.confirmIconUpload}},[e._v(\"确 定\")])],1)])],1)},l=[],o=a(\"5530\"),n=(a(\"99af\"),a(\"4de4\"),a(\"d81d\"),a(\"14d9\"),a(\"fb6a\"),a(\"a434\"),a(\"b0c0\"),a(\"e9c4\"),a(\"b64b\"),a(\"d3b7\"),a(\"ac1f\"),a(\"00b4\"),a(\"25f0\"),a(\"5319\"),a(\"498a\"),a(\"0643\"),a(\"2382\"),a(\"4e3e\"),a(\"a573\"),a(\"159b\"),a(\"8cd4\")),r={name:\"MiniDemandCategory\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,categoryList:[],title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,categoryName:null,status:null},form:{},rules:{categoryName:[{required:!0,message:\"类型名称不能为空\",trigger:\"blur\"}],categoryCode:[{required:!0,message:\"类型标识不能为空\",trigger:\"blur\"},{pattern:/^[a-zA-Z][a-zA-Z0-9_]*$/,message:\"类型标识必须以字母开头，只能包含字母、数字和下划线\",trigger:\"blur\"}],status:[{required:!0,message:\"状态不能为空\",trigger:\"change\"}]},formConfigOpen:!1,previewOpen:!1,currentCategoryId:null,formFieldsList:[],formModulesList:[],selectedTemplate:\"\",previewData:{},previewDialogData:{},iconUploaderOpen:!1,currentModuleIndex:-1,uploadedIcon:\"\"}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(n[\"e\"])(this.queryParams).then((function(t){e.categoryList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={categoryId:null,categoryName:null,categoryCode:null,categoryShortName:null,categoryIcon:null,categoryDesc:null,sortOrder:0,status:\"0\",remark:null},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.categoryId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加需求类型\"},handleUpdate:function(e){var t=this;this.reset();var a=e.categoryId||this.ids;Object(n[\"c\"])(a).then((function(e){t.form=e.data,t.open=!0,t.title=\"修改需求类型\"}))},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.categoryId?Object(n[\"f\"])(e.form).then((function(t){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):Object(n[\"a\"])(e.form).then((function(t){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.categoryId||this.ids;this.$modal.confirm('是否确认删除需求类型编号为\"'+a+'\"的数据项？').then((function(){return Object(n[\"b\"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/demandcategory/export\",Object(o[\"a\"])({},this.queryParams),\"需求类型数据_\".concat((new Date).getTime(),\".xlsx\"))},handleSortChange:function(e){var t=this;Object(n[\"f\"])(e).then((function(e){t.$modal.msgSuccess(\"排序修改成功\"),t.getList()}))},handleFormConfig:function(e){var t=this;if(this.currentCategoryId=e.categoryId,this.formFieldsList=[],this.formModulesList=[],e.formFields)try{var a=JSON.parse(e.formFields);Array.isArray(a)&&a.length>0&&a[0].hasOwnProperty(\"name\")?(this.formModulesList=a,this.formModulesList.forEach((function(e){e.fields&&e.fields.forEach((function(e){void 0===e.hidden&&t.$set(e,\"hidden\",!1)}))}))):Array.isArray(a)&&(this.formFieldsList=a,a.length>0&&(a.forEach((function(e){void 0===e.hidden&&t.$set(e,\"hidden\",!1)})),this.formModulesList=[{name:\"基础信息\",description:\"\",fields:a}]))}catch(i){console.error(\"解析表单配置失败:\",i),this.formFieldsList=[],this.formModulesList=[]}this.formConfigOpen=!0,this.initPreviewData()},initPreviewData:function(){var e=this;this.previewData={},this.formModulesList.forEach((function(t){t.fields&&t.fields.forEach((function(t){t.name&&(\"checkbox\"===t.type?e.previewData[t.name]=[]:\"number\"===t.type||\"date\"===t.type||\"time\"===t.type?e.previewData[t.name]=null:e.previewData[t.name]=\"\")}))}))},addFormModule:function(){var e={name:\"新模块\",description:\"\",fields:[],icon:\"\"};this.formModulesList.push(e)},removeModule:function(e){var t=this;this.$confirm(\"确认删除该模块及其所有字段吗？\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){t.formModulesList.splice(e,1),t.$message.success(\"删除成功\")})).catch((function(){}))},moveModuleUp:function(e){if(e>0){var t=this.formModulesList[e];this.$set(this.formModulesList,e,this.formModulesList[e-1]),this.$set(this.formModulesList,e-1,t)}},moveModuleDown:function(e){if(e<this.formModulesList.length-1){var t=this.formModulesList[e];this.$set(this.formModulesList,e,this.formModulesList[e+1]),this.$set(this.formModulesList,e+1,t)}},validateModuleName:function(e){e.name&&\"\"!==e.name.trim()||(e.name=\"未命名模块\")},addFieldToModule:function(e){var t=this,a={label:\"\",name:\"\",type:\"input\",required:!1,hidden:!1,options:\"\",placeholder:\"请输入\",staticContent:\"\"};this.formModulesList[e].fields||this.$set(this.formModulesList[e],\"fields\",[]),this.formModulesList[e].fields.push(a),this.$nextTick((function(){t.initPreviewData()}))},handleIconCommand:function(e,t){this.currentModuleIndex=t,\"upload\"===e?(this.uploadedIcon=\"\",this.iconUploaderOpen=!0):\"remove\"===e&&this.$set(this.formModulesList[t],\"icon\",\"\")},openIconUploader:function(e){this.currentModuleIndex=e,this.uploadedIcon=\"\",this.iconUploaderOpen=!0},confirmIconUpload:function(){this.currentModuleIndex>=0&&this.uploadedIcon&&(this.$set(this.formModulesList[this.currentModuleIndex],\"icon\",this.uploadedIcon),this.$message.success(\"图标上传成功\")),this.iconUploaderOpen=!1,this.currentModuleIndex=-1,this.uploadedIcon=\"\"},cancelIconUpload:function(){this.iconUploaderOpen=!1,this.currentModuleIndex=-1,this.uploadedIcon=\"\"},onFieldTypeChange:function(e){var t={input:\"请输入\",textarea:\"请输入\",number:\"请输入数字\",tel:\"请输入电话号码\",email:\"请输入邮箱地址\",radio:\"\",checkbox:\"\",select:\"请选择\",date:\"请选择日期\",time:\"请选择时间\",file:\"点击上传\",static:\"\"};e.placeholder&&\"\"!==e.placeholder||(e.placeholder=t[e.type]||\"请输入\"),\"static\"===e.type&&(e.required=!1,e.staticContent||(e.staticContent=\"这里是静态展示内容\"))},onFieldHiddenChange:function(){},removeFieldFromModule:function(e,t){this.formModulesList[e].fields.splice(t,1)},moveFieldUpInModule:function(e,t){var a=this.formModulesList[e].fields;if(t>0){var i=a[t];this.$set(a,t,a[t-1]),this.$set(a,t-1,i)}},moveFieldDownInModule:function(e,t){var a=this.formModulesList[e].fields;if(t<a.length-1){var i=a[t];this.$set(a,t,a[t+1]),this.$set(a,t+1,i)}},addFormField:function(){0===this.formModulesList.length&&(this.addFormModule(),this.formModulesList[0].name=\"基础信息\"),this.addFieldToModule(0)},generateFieldName:function(e){if(e.label){var t={\"姓名\":\"name\",\"联系人\":\"contact_name\",\"联系电话\":\"phone\",\"手机号\":\"phone\",\"电话\":\"phone\",\"邮箱\":\"email\",\"邮箱地址\":\"email\",\"公司\":\"company\",\"公司名称\":\"company_name\",\"职位\":\"position\",\"部门\":\"department\",\"地址\":\"address\",\"详细地址\":\"detailed_address\",\"技术方向\":\"tech_direction\",\"技术栈\":\"tech_stack\",\"开发语言\":\"programming_language\",\"项目周期\":\"project_duration\",\"预算范围\":\"budget_range\",\"项目详细需求\":\"detailed_requirements\",\"技术要求\":\"tech_requirements\",\"推广类型\":\"promotion_type\",\"目标客户群体\":\"target_audience\",\"推广渠道\":\"promotion_channels\",\"推广预算\":\"promotion_budget\",\"推广时间\":\"promotion_duration\",\"推广目标\":\"promotion_goals\",\"招聘职位\":\"job_position\",\"工作经验\":\"work_experience\",\"学历要求\":\"education_requirement\",\"薪资范围\":\"salary_range\",\"工作地点\":\"work_location\",\"职位描述\":\"job_description\",\"投资类型\":\"investment_type\",\"投资金额\":\"investment_amount\",\"投资阶段\":\"investment_stage\",\"行业领域\":\"industry_field\",\"项目介绍\":\"project_introduction\",\"产品名称\":\"product_name\",\"采购数量\":\"purchase_quantity\",\"质量要求\":\"quality_requirements\",\"交付时间\":\"delivery_time\",\"采购预算\":\"purchase_budget\",\"需求描述\":\"description\",\"详细说明\":\"detailed_description\",\"备注\":\"remark\",\"说明\":\"note\",\"标题\":\"title\",\"内容\":\"content\",\"时间\":\"time\",\"日期\":\"date\",\"文件\":\"file\",\"图片\":\"image\",\"附件\":\"attachment\"};if(t[e.label])e.name=t[e.label];else{var a=e.label.replace(/[\\s\\-\\/\\\\]/g,\"_\").replace(/[^\\w\\u4e00-\\u9fa5]/g,\"\").toLowerCase();/[\\u4e00-\\u9fa5]/.test(a)&&(a=a.replace(/类型/g,\"type\").replace(/名称/g,\"name\").replace(/时间/g,\"time\").replace(/日期/g,\"date\").replace(/地址/g,\"address\").replace(/电话/g,\"phone\").replace(/邮箱/g,\"email\").replace(/公司/g,\"company\").replace(/描述/g,\"description\").replace(/说明/g,\"note\").replace(/备注/g,\"remark\").replace(/要求/g,\"requirement\").replace(/范围/g,\"range\").replace(/预算/g,\"budget\").replace(/数量/g,\"quantity\").replace(/价格/g,\"price\").replace(/费用/g,\"cost\"),/[\\u4e00-\\u9fa5]/.test(a)&&(a=\"field_\"+Date.now().toString().slice(-6))),e.name=a}}},applyTemplate:function(){var e=this;\"basic\"===this.selectedTemplate?this.applyBasicTemplate():\"tech\"===this.selectedTemplate?this.applyTechTemplate():\"business\"===this.selectedTemplate&&this.applyBusinessTemplate(),this.$nextTick((function(){e.initPreviewData()}))},applyBasicTemplate:function(){var e=this;this.formModulesList=[{name:\"填写说明\",description:\"请仔细阅读以下说明后填写表单\",fields:[{label:\"温馨提示\",name:\"tips\",type:\"static\",required:!1,hidden:!1,options:\"\",placeholder:\"\",staticContent:\"请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。\"}]},{name:\"基础信息\",description:\"请填写需求的基本信息\",fields:[{label:\"需求标题\",name:\"title\",type:\"input\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入需求标题\",staticContent:\"\"},{label:\"需求描述\",name:\"description\",type:\"textarea\",required:!0,hidden:!1,options:\"\",placeholder:\"请详细描述您的需求\",staticContent:\"\"}]},{name:\"联系方式\",description:\"请填写您的联系方式，以便我们与您取得联系\",fields:[{label:\"联系人\",name:\"contact_name\",type:\"input\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入联系人姓名\",staticContent:\"\"},{label:\"联系电话\",name:\"phone\",type:\"tel\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入手机号码\",staticContent:\"\"}]}],this.formModulesList.forEach((function(t){t.fields.forEach((function(t){e.generateFieldName(t)}))}))},applyTechTemplate:function(){var e=this;this.formModulesList=[{name:\"技术需求\",description:\"请详细描述您的技术需求\",fields:[{label:\"技术方向\",name:\"\",type:\"select\",required:!0,hidden:!1,options:\"前端开发,后端开发,移动开发,人工智能,大数据,云计算\",placeholder:\"请选择技术方向\",staticContent:\"\"},{label:\"技术栈\",name:\"\",type:\"checkbox\",required:!1,hidden:!1,options:\"Java,Python,JavaScript,React,Vue,Spring Boot,MySQL,Redis\",placeholder:\"\",staticContent:\"\"},{label:\"项目详细需求\",name:\"\",type:\"textarea\",required:!0,hidden:!1,options:\"\",placeholder:\"请详细描述项目需求、功能要求、技术要求等\",staticContent:\"\"}]},{name:\"项目信息\",description:\"请填写项目的基本信息\",fields:[{label:\"项目周期\",name:\"\",type:\"select\",required:!0,hidden:!1,options:\"1周内,1-2周,2-4周,1-2个月,2-3个月,3个月以上\",placeholder:\"请选择项目周期\",staticContent:\"\"},{label:\"预算范围\",name:\"\",type:\"radio\",required:!0,hidden:!1,options:\"1万以下,1-5万,5-10万,10-20万,20万以上\",placeholder:\"\",staticContent:\"\"}]},{name:\"联系方式\",description:\"请填写您的联系方式\",fields:[{label:\"联系人\",name:\"\",type:\"input\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入联系人姓名\",staticContent:\"\"},{label:\"联系电话\",name:\"\",type:\"tel\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入手机号码\",staticContent:\"\"}]}],this.formModulesList.forEach((function(t){t.fields.forEach((function(t){e.generateFieldName(t)}))}))},applyBusinessTemplate:function(){var e=this;this.formModulesList=[{name:\"合作信息\",description:\"请填写合作相关信息\",fields:[{label:\"合作类型\",name:\"\",type:\"radio\",required:!0,hidden:!1,options:\"战略合作,技术合作,市场合作,投资合作\",placeholder:\"\",staticContent:\"\"},{label:\"合作描述\",name:\"\",type:\"textarea\",required:!0,hidden:!1,options:\"\",placeholder:\"请详细描述合作内容、合作方式、期望达成的目标等\",staticContent:\"\"}]},{name:\"公司信息\",description:\"请填写您的公司基本信息\",fields:[{label:\"公司名称\",name:\"\",type:\"input\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入公司全称\",staticContent:\"\"},{label:\"公司规模\",name:\"\",type:\"select\",required:!0,hidden:!1,options:\"10人以下,10-50人,50-200人,200-500人,500人以上\",placeholder:\"请选择公司规模\",staticContent:\"\"},{label:\"行业领域\",name:\"\",type:\"select\",required:!0,hidden:!1,options:\"互联网,金融,教育,医疗,制造业,服务业,其他\",placeholder:\"请选择行业领域\",staticContent:\"\"}]},{name:\"联系方式\",description:\"请填写联系方式，以便我们与您取得联系\",fields:[{label:\"联系人\",name:\"\",type:\"input\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入联系人姓名\",staticContent:\"\"},{label:\"联系电话\",name:\"\",type:\"tel\",required:!0,hidden:!1,options:\"\",placeholder:\"请输入手机号码\",staticContent:\"\"},{label:\"邮箱地址\",name:\"\",type:\"email\",required:!1,hidden:!1,options:\"\",placeholder:\"请输入邮箱地址（选填）\",staticContent:\"\"}]}],this.formModulesList.forEach((function(t){t.fields.forEach((function(t){e.generateFieldName(t)}))}))},getFieldOptions:function(e){return e.options&&\"\"!==e.options.trim()?e.options.split(\",\").map((function(e){return e.trim()})).filter((function(e){return\"\"!==e})):[]},previewForm:function(){this.initPreviewDialogData(),this.previewOpen=!0},initPreviewDialogData:function(){var e=this;this.previewDialogData={},this.formModulesList.forEach((function(t){t.fields&&t.fields.forEach((function(t){t.name&&(\"checkbox\"===t.type?e.previewDialogData[t.name]=[]:\"number\"===t.type||\"date\"===t.type||\"time\"===t.type?e.previewDialogData[t.name]=null:e.previewDialogData[t.name]=\"\")}))}))},saveFormConfig:function(){var e=this;if(this.currentCategoryId){for(var t=0;t<this.formModulesList.length;t++){var a=this.formModulesList[t];if(!a.name||\"\"===a.name.trim())return void this.$modal.msgError(\"第\".concat(t+1,\"个模块名称不能为空\"));for(var i=0;i<a.fields.length;i++){var l=a.fields[i];if(!l.label||\"\"===l.label.trim())return void this.$modal.msgError('模块\"'.concat(a.name,'\"中第').concat(i+1,\"个字段标签不能为空\"))}}var o={categoryId:this.currentCategoryId,formFields:JSON.stringify(this.formModulesList)};Object(n[\"f\"])(o).then((function(){e.$modal.msgSuccess(\"表单配置保存成功\"),e.formConfigOpen=!1,e.getList()})).catch((function(t){console.error(\"保存表单配置失败:\",t),e.$modal.msgError(\"保存表单配置失败\")}))}else this.$modal.msgError(\"未选择需求类型\")},cancelFormConfig:function(){this.formConfigOpen=!1,this.formFieldsList=[],this.formModulesList=[],this.currentCategoryId=null,this.selectedTemplate=\"\",this.previewData={},this.previewDialogData={}}}},s=r,c=(a(\"aca2\"),a(\"2877\")),d=Object(c[\"a\"])(s,i,l,!1,null,\"07b2fb8c\",null);t[\"default\"]=d.exports}}]);", "extractedComments": []}