{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753847579546}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UGFyaywgZ2V0UGFyaywgZGVsUGFyaywgYWRkUGFyaywgdXBkYXRlUGFyaywgZXhwb3J0UGFyaywgZ2V0UGFya0ludHJvSW1hZ2UsIHVwZGF0ZVBhcmtJbnRyb0ltYWdlIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9wYXJrIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTWluaVBhcmsiLA0KICBkaWN0czogWydzeXNfbm9ybWFsX2Rpc2FibGUnXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOWbreWMuueuoeeQhuihqOagvOaVsOaNrg0KICAgICAgcGFya0xpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5Zut5Yy6566A5LuL5Zu+54mH5a+56K+d5qGGDQogICAgICBpbnRyb0ltYWdlT3BlbjogZmFsc2UsDQogICAgICAvLyDlm63ljLrnroDku4vlm77niYfooajljZUNCiAgICAgIGludHJvSW1hZ2VGb3JtOiB7DQogICAgICAgIGludHJvSW1hZ2VVcmw6ICcnDQogICAgICB9LA0KICAgICAgLy8g5Zu+54mH5Yqg6L2954q25oCBDQogICAgICBpbnRyb0ltYWdlTG9hZGluZzogZmFsc2UsDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcGFya05hbWU6IG51bGwsDQogICAgICAgIHBhcmtDb2RlOiBudWxsLA0KICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgcGFya05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Zut5Yy65ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc29ydE9yZGVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaOkuW6j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouWbreWMuueuoeeQhuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFBhcmsodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGFya0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5oyJ6ZKuICovDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvKiog6KGo5Y2V6YeN572uICovDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHBhcmtJZDogbnVsbCwNCiAgICAgICAgcGFya05hbWU6IG51bGwsDQogICAgICAgIHBhcmtDb2RlOiBudWxsLA0KICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwNCiAgICAgICAgY29udGVudDogbnVsbCwNCiAgICAgICAgY292ZXJJbWFnZTogbnVsbCwNCiAgICAgICAgc29ydE9yZGVyOiAwLA0KICAgICAgICBzdGF0dXM6ICIwIiwNCiAgICAgICAgcmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLyoqIOWkmumAieahhumAieS4reaVsOaNriAqLw0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucGFya0lkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlm63ljLrnrqHnkIYiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IHBhcmtJZCA9IHJvdy5wYXJrSWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXRQYXJrKHBhcmtJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Zut5Yy6566h55CGIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnBhcmtJZCAhPT0gbnVsbCAmJiB0aGlzLmZvcm0ucGFya0lkICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIHVwZGF0ZVBhcmsodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRQYXJrKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHBhcmtJZHMgPSByb3cucGFya0lkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Zut5Yy6566h55CG57yW5Y+35Li6IicgKyBwYXJrSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsUGFyayhwYXJrSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Zut5Yy6566h55CG5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgIHJldHVybiBleHBvcnRQYXJrKHRoaXMucXVlcnlQYXJhbXMpOw0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJGRvd25sb2FkLmV4Y2VsKHJlc3BvbnNlLCAn5Zut5Yy6566h55CG5pWw5o2uLnhsc3gnKTsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5o6S5bqP5L+u5pS5ICovDQogICAgaGFuZGxlU29ydENoYW5nZShyb3cpIHsNCiAgICAgIHVwZGF0ZVBhcmsocm93KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5L+u5pS55oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Zut5Yy6566A5LuL5Zu+54mH5LiK5LygICovDQogICAgaGFuZGxlSW50cm9JbWFnZVVwbG9hZCgpIHsNCiAgICAgIC8vIOWFiOmHjee9ruihqOWNleaVsOaNrg0KICAgICAgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7DQogICAgICB0aGlzLmludHJvSW1hZ2VPcGVuID0gdHJ1ZTsNCiAgICAgIC8vIOWKoOi9veW9k+WJjeWbvueJh+aVsOaNrg0KICAgICAgdGhpcy5sb2FkSW50cm9JbWFnZSgpOw0KICAgIH0sDQogICAgLyoqIOWKoOi9veWbreWMuueugOS7i+WbvueJhyAqLw0KICAgIGxvYWRJbnRyb0ltYWdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WKoOi9veWbreWMuueugOS7i+WbvueJhy4uLicpOw0KICAgICAgdGhpcy5pbnRyb0ltYWdlTG9hZGluZyA9IHRydWU7DQoNCiAgICAgIGdldFBhcmtJbnRyb0ltYWdlKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5blm63ljLrnroDku4vlm77niYflk43lupQ6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgLy8g5LyY5YWI6K+75Y+WZGF0YeWtl+aute+8jOWmguaenOayoeacieWImeivu+WPlm1zZ+Wtl+aute+8iOWQkeWQjuWFvOWuue+8iQ0KICAgICAgICAgIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybCA9IHJlc3BvbnNlLmRhdGEgfHwgcmVzcG9uc2UubXNnIHx8ICcnOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCforr7nva7lm77niYdVUkw6JywgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPluWbreWMuueugOS7i+WbvueJh+Wksei0pTonLCByZXNwb25zZS5tc2cpOw0KICAgICAgICAgIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybCA9ICcnOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWbreWMuueugOS7i+WbvueJh+WHuumUmTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuaW50cm9JbWFnZUZvcm0uaW50cm9JbWFnZVVybCA9ICcnOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5Yqg6L295Zut5Yy6566A5LuL5Zu+54mH5aSx6LSlIik7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5pbnRyb0ltYWdlTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5Zut5Yy6566A5LuL5Zu+54mHICovDQogICAgY2FuY2VsSW50cm9JbWFnZSgpIHsNCiAgICAgIHRoaXMuaW50cm9JbWFnZU9wZW4gPSBmYWxzZTsNCiAgICAgIC8vIOS4jeimgea4heepuuaVsOaNru+8jOS/neaMgeWOn+acieaVsOaNrg0KICAgICAgLy8gdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsID0gJyc7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5Zut5Yy6566A5LuL5Zu+54mHICovDQogICAgc3VibWl0SW50cm9JbWFnZUZvcm0oKSB7DQogICAgICB1cGRhdGVQYXJrSW50cm9JbWFnZSh0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmwpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlm63ljLrnroDku4vlm77niYfmm7TmlrDmiJDlip8iKTsNCiAgICAgICAgdGhpcy5pbnRyb0ltYWdlT3BlbiA9IGZhbHNlOw0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5pu05paw5Zut5Yy6566A5LuL5Zu+54mH5aSx6LSlIik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDpooTop4jlm63ljLrnroDku4vlm77niYcgKi8NCiAgICBwcmV2aWV3SW50cm9JbWFnZSgpIHsNCiAgICAgIGlmICh0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmwgJiYgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsLnRyaW0oKSAhPT0gJycpIHsNCiAgICAgICAgdGhpcy5wcmV2aWV3SW1hZ2VVcmwgPSB0aGlzLmludHJvSW1hZ2VGb3JtLmludHJvSW1hZ2VVcmw7DQogICAgICAgIHRoaXMucHJldmlld1Zpc2libGUgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi5pqC5peg5Zu+54mH5Y+v6aKE6KeIIik7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5YWz6Zet5Zu+54mH6aKE6KeIICovDQogICAgY2xvc2VQcmV2aWV3KCkgew0KICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5wcmV2aWV3SW1hZ2VVcmwgPSAnJzsNCiAgICB9LA0KICAgIC8qKiDlm77niYfliqDovb3plJnor6/lpITnkIYgKi8NCiAgICBoYW5kbGVJbWFnZUVycm9yKGV2ZW50KSB7DQogICAgICBjb25zb2xlLmVycm9yKCflm77niYfliqDovb3lpLHotKU6JywgdGhpcy5pbnRyb0ltYWdlRm9ybS5pbnRyb0ltYWdlVXJsKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlm77niYfliqDovb3lpLHotKXvvIzor7fmo4Dmn6Xlm77niYfpk77mjqXmmK/lkKbmnInmlYgiKTsNCiAgICAgIC8vIOWPr+S7peiuvue9ruS4gOS4qum7mOiupOeahOmUmeivr+WbvueJhw0KICAgICAgLy8gZXZlbnQudGFyZ2V0LnNyYyA9ICcvcGF0aC90by9kZWZhdWx0LWVycm9yLWltYWdlLnBuZyc7DQogICAgfQ0KICB9DQp9Ow0K"}, null]}