{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-8b273d68\"],{\"0d89\":function(e,t,a){\"use strict\";a(\"b439\")},b439:function(e,t,a){},de98:function(e,t,a){\"use strict\";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"联系人\",prop:\"contactName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系人姓名\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.contactName,callback:function(t){e.$set(e.queryParams,\"contactName\",t)},expression:\"queryParams.contactName\"}})],1),a(\"el-form-item\",{attrs:{label:\"标识\",prop:\"contactCode\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系人标识\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.contactCode,callback:function(t){e.$set(e.queryParams,\"contactCode\",t)},expression:\"queryParams.contactCode\"}})],1),a(\"el-form-item\",{attrs:{label:\"电话\",prop:\"contactPhone\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系电话\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.contactPhone,callback:function(t){e.$set(e.queryParams,\"contactPhone\",t)},expression:\"queryParams.contactPhone\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:contact:add\"],expression:\"['miniapp:contact:add']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-plus\",size:\"mini\"},on:{click:e.handleAdd}},[e._v(\"新增\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:contact:edit\"],expression:\"['miniapp:contact:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:contact:remove\"],expression:\"['miniapp:contact:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:contact:export\"],expression:\"['miniapp:contact:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.contactList,\"row-key\":\"contactId\"},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"联系人ID\",align:\"center\",prop:\"contactId\",width:\"100\"}}),a(\"el-table-column\",{attrs:{label:\"联系人姓名\",align:\"center\",prop:\"contactName\",width:\"120\"}}),a(\"el-table-column\",{attrs:{label:\"联系人标识\",align:\"center\",prop:\"contactCode\",width:\"120\"}}),a(\"el-table-column\",{attrs:{label:\"联系电话\",align:\"center\",prop:\"contactPhone\",width:\"150\"}}),a(\"el-table-column\",{attrs:{label:\"联系二维码\",align:\"center\",prop:\"qrCodeUrl\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[t.row.qrCodeUrl?a(\"image-preview\",{attrs:{src:t.row.qrCodeUrl,width:60,height:60}}):a(\"span\",{staticClass:\"no-qrcode\"},[e._v(\"暂无二维码\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"排序\",align:\"center\",prop:\"sortOrder\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-input-number\",{staticStyle:{width:\"80px\"},attrs:{min:0,size:\"mini\",controls:!1},on:{change:function(a){return e.handleSortChange(t.row)}},model:{value:t.row.sortOrder,callback:function(a){e.$set(t.row,\"sortOrder\",a)},expression:\"scope.row.sortOrder\"}})]}}])}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"dict-tag\",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),a(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"备注\",align:\"center\",prop:\"remark\",\"show-overflow-tooltip\":!0}}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"160\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:contact:edit\"],expression:\"['miniapp:contact:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(\"修改\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:contact:remove\"],expression:\"['miniapp:contact:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"联系人姓名\",prop:\"contactName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系人姓名\"},model:{value:e.form.contactName,callback:function(t){e.$set(e.form,\"contactName\",t)},expression:\"form.contactName\"}})],1),a(\"el-form-item\",{attrs:{label:\"联系人标识\",prop:\"contactCode\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系人唯一标识，如：SERVICE_01\",disabled:null!=e.form.contactId},model:{value:e.form.contactCode,callback:function(t){e.$set(e.form,\"contactCode\",t)},expression:\"form.contactCode\"}}),a(\"div\",{staticClass:\"form-tip\"},[a(\"p\",[e._v(\"• 用于标识联系人的唯一代码\")]),a(\"p\",[e._v(\"• 建议格式：SERVICE_01、TECH_01、BUSINESS_01\")]),null==e.form.contactId?a(\"p\",[e._v(\"• 一旦设置后不可修改\")]):a(\"p\",{staticStyle:{color:\"#f56c6c\"}},[e._v(\"• 联系人标识不可修改\")])])],1),a(\"el-form-item\",{attrs:{label:\"联系电话\",prop:\"contactPhone\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系电话\"},model:{value:e.form.contactPhone,callback:function(t){e.$set(e.form,\"contactPhone\",t)},expression:\"form.contactPhone\"}})],1),a(\"el-form-item\",{attrs:{label:\"联系二维码\",prop:\"qrCodeUrl\"}},[a(\"ImageUpload\",{attrs:{limit:1,fileSize:2,isShowTip:!0},model:{value:e.form.qrCodeUrl,callback:function(t){e.$set(e.form,\"qrCodeUrl\",t)},expression:\"form.qrCodeUrl\"}}),a(\"div\",{staticClass:\"form-tip\"},[a(\"p\",[e._v(\"• 支持 jpg、png、gif 格式\")]),a(\"p\",[e._v(\"• 文件大小不超过 2MB\")]),a(\"p\",[e._v(\"• 建议尺寸 200x200 像素，保证清晰度\")])])],1),a(\"el-form-item\",{attrs:{label:\"排序\",prop:\"sortOrder\"}},[a(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{min:0,placeholder:\"数字越小越靠前\"},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,\"sortOrder\",t)},expression:\"form.sortOrder\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},e._l(e.dict.type.sys_normal_disable,(function(t){return a(\"el-radio\",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1)],1)},r=[],o=a(\"5530\"),l=(a(\"d81d\"),a(\"d3b7\"),a(\"0643\"),a(\"a573\"),a(\"b775\"));function i(e){return Object(l[\"a\"])({url:\"/miniapp/contact/list\",method:\"post\",data:e})}function s(e){return Object(l[\"a\"])({url:\"/miniapp/contact/getInfo\",method:\"post\",data:e})}function c(e){return Object(l[\"a\"])({url:\"/miniapp/contact/add\",method:\"post\",data:e})}function u(e){return Object(l[\"a\"])({url:\"/miniapp/contact/edit\",method:\"post\",data:e})}function m(e){return Object(l[\"a\"])({url:\"/miniapp/contact/remove\",method:\"post\",data:e})}function d(e){return Object(l[\"a\"])({url:\"/miniapp/contact/updateSort\",method:\"post\",data:e})}var p={name:\"Contact\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,contactList:[],title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,contactName:null,contactCode:null,contactPhone:null,status:null},form:{},rules:{contactName:[{required:!0,message:\"联系人姓名不能为空\",trigger:\"blur\"}],contactCode:[{required:!0,message:\"联系人标识不能为空\",trigger:\"blur\"},{pattern:/^[A-Z0-9_]{3,20}$/,message:\"标识只能包含大写字母、数字和下划线，长度3-20位\",trigger:\"blur\"}],contactPhone:[{required:!0,message:\"联系电话不能为空\",trigger:\"blur\"},{pattern:/^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$|^400-?\\d{3}-?\\d{4}$/,message:\"请输入正确的电话号码\",trigger:\"blur\"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.contactList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={contactId:null,contactName:null,contactCode:null,contactPhone:null,qrCodeUrl:null,sortOrder:0,status:\"0\",remark:null},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.contactId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加联系人管理\"},handleUpdate:function(e){var t=this;this.reset();var a=e.contactId||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title=\"修改联系人管理\"}))},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.contactId?u(e.form).then((function(t){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.contactId||this.ids;this.$modal.confirm('是否确认删除联系人管理编号为\"'+a+'\"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/contact/export\",Object(o[\"a\"])({},this.queryParams),\"contact_\".concat((new Date).getTime(),\".xlsx\"))},handleSortChange:function(e){var t=this,a={contactId:e.contactId,sortOrder:e.sortOrder};d(a).then((function(e){t.$modal.msgSuccess(\"排序更新成功\"),t.getList()})).catch((function(){t.$modal.msgError(\"排序更新失败\"),t.getList()}))}}},h=p,f=(a(\"0d89\"),a(\"2877\")),b=Object(f[\"a\"])(h,n,r,!1,null,\"25b86cec\",null);t[\"default\"]=b.exports}}]);", "extractedComments": []}