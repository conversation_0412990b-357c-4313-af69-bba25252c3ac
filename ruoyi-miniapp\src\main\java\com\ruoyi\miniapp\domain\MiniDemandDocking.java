package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 需求对接对象 mini_demand_docking
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@ApiModel("需求对接")
public class MiniDemandDocking extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 对接ID */
    @ApiModelProperty("对接ID")
    private Long dockingId;

    /** 需求ID */
    @ApiModelProperty("需求ID")
    @Excel(name = "需求ID")
    private Long demandId;

    /** 对接用户ID（揭榜人） */
    @ApiModelProperty("对接用户ID")
    @Excel(name = "对接用户ID")
    private Long userId;

    /** 对接用户姓名 */
    @ApiModelProperty("对接用户姓名")
    @Excel(name = "对接用户姓名")
    private String userName;

    /** 对接用户手机号 */
    @ApiModelProperty("对接用户手机号")
    @Excel(name = "对接用户手机号")
    private String userPhone;

    /** 对接用户公司 */
    @ApiModelProperty("对接用户公司")
    @Excel(name = "对接用户公司")
    private String userCompany;

    /** 对接用户职位 */
    @ApiModelProperty("对接用户职位")
    @Excel(name = "对接用户职位")
    private String userPosition;

    /** 对接时间 */
    @ApiModelProperty("对接时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "对接时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dockingTime;

    /** 获取到的联系人姓名 */
    @ApiModelProperty("获取到的联系人姓名")
    @Excel(name = "联系人姓名")
    private String contactName;

    /** 获取到的联系人手机号 */
    @ApiModelProperty("获取到的联系人手机号")
    @Excel(name = "联系人手机号")
    private String contactPhone;

    /** 联系方式来源（0后台配置 1需求发布人） */
    @ApiModelProperty("联系方式来源")
    @Excel(name = "联系方式来源", readConverterExp = "0=后台配置,1=需求发布人")
    private String contactSource;

    /** 状态（0正常 1已取消） */
    @ApiModelProperty("状态")
    @Excel(name = "状态", readConverterExp = "0=正常,1=已取消")
    private String status;

    /** 需求标题（非数据库字段，用于显示） */
    @ApiModelProperty("需求标题")
    @Excel(name = "需求标题")
    private String demandTitle;

    /** 是否已联系（0未联系 1已联系） */
    @ApiModelProperty("是否已联系")
    @Excel(name = "是否已联系", readConverterExp = "0=未联系,1=已联系")
    private String isContacted;

    /** 联系结果 */
    @ApiModelProperty("联系结果")
    @Excel(name = "联系结果")
    private String contactResult;

    /** 联系备注 */
    @ApiModelProperty("联系备注")
    @Excel(name = "联系备注")
    private String contactNotes;

    /** 联系时间 */
    @ApiModelProperty("联系时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "联系时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date contactTime;

    /** 联系二维码URL */
    @ApiModelProperty("联系二维码URL")
    @Excel(name = "联系二维码URL")
    private String qrCodeUrl;

    // 关联查询字段
    /** 需求信息 */
    @ApiModelProperty("需求信息")
    private DemandInfo demandInfo;

    /** 对接用户信息 */
    @ApiModelProperty("对接用户信息")
    private UserInfo userInfo;

    public void setDockingId(Long dockingId) 
    {
        this.dockingId = dockingId;
    }

    public Long getDockingId() 
    {
        return dockingId;
    }
    public void setDemandId(Long demandId) 
    {
        this.demandId = demandId;
    }

    public Long getDemandId() 
    {
        return demandId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setUserPhone(String userPhone) 
    {
        this.userPhone = userPhone;
    }

    public String getUserPhone() 
    {
        return userPhone;
    }
    public void setUserCompany(String userCompany) 
    {
        this.userCompany = userCompany;
    }

    public String getUserCompany() 
    {
        return userCompany;
    }
    public void setUserPosition(String userPosition) 
    {
        this.userPosition = userPosition;
    }

    public String getUserPosition() 
    {
        return userPosition;
    }
    public void setDockingTime(Date dockingTime) 
    {
        this.dockingTime = dockingTime;
    }

    public Date getDockingTime() 
    {
        return dockingTime;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactSource(String contactSource) 
    {
        this.contactSource = contactSource;
    }

    public String getContactSource() 
    {
        return contactSource;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setDemandTitle(String demandTitle)
    {
        this.demandTitle = demandTitle;
    }

    public String getDemandTitle()
    {
        return demandTitle;
    }

    public void setIsContacted(String isContacted)
    {
        this.isContacted = isContacted;
    }

    public String getIsContacted()
    {
        return isContacted;
    }

    public void setContactResult(String contactResult)
    {
        this.contactResult = contactResult;
    }

    public String getContactResult()
    {
        return contactResult;
    }

    public void setContactNotes(String contactNotes)
    {
        this.contactNotes = contactNotes;
    }

    public String getContactNotes()
    {
        return contactNotes;
    }

    public void setContactTime(Date contactTime)
    {
        this.contactTime = contactTime;
    }

    public Date getContactTime()
    {
        return contactTime;
    }

    public void setQrCodeUrl(String qrCodeUrl)
    {
        this.qrCodeUrl = qrCodeUrl;
    }

    public String getQrCodeUrl()
    {
        return qrCodeUrl;
    }

    public DemandInfo getDemandInfo() {
        return demandInfo;
    }

    public void setDemandInfo(DemandInfo demandInfo) {
        this.demandInfo = demandInfo;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dockingId", getDockingId())
            .append("demandId", getDemandId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("userPhone", getUserPhone())
            .append("userCompany", getUserCompany())
            .append("userPosition", getUserPosition())
            .append("dockingTime", getDockingTime())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("contactSource", getContactSource())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    /**
     * 需求信息
     */
    @ApiModel("需求信息")
    public static class DemandInfo {
        @ApiModelProperty("需求ID")
        private Long demandId;
        
        @ApiModelProperty("需求标题")
        private String demandTitle;
        
        @ApiModelProperty("需求描述")
        private String demandDesc;
        
        @ApiModelProperty("分类名称")
        private String categoryName;

        /** 需求类型名称（关联查询字段，非数据库字段） */
        private String categoryShortName;

        /** 类型标识代码 */
        @Excel(name = "类型标识代码")
        private String categoryCode;

        /** 联系人姓名 */
        @Excel(name = "联系人姓名")
        private String contactName;

        /** 联系人手机 */
        @Excel(name = "联系人手机")
        private String contactPhone;

        /** 需求状态（0已发布 1已对接 2已下架） */
        @Excel(name = "需求状态", readConverterExp = "0=已发布,1=已对接,2=已下架")
        private String demandStatus;

        /** 动态表单数据(JSON格式) */
        @Excel(name = "动态表单数据")
        private String formData;

        /** 浏览次数 */
        @Excel(name = "浏览次数")
        private Integer viewCount;

        // getter and setter methods
        public Long getDemandId() { return demandId; }
        public void setDemandId(Long demandId) { this.demandId = demandId; }
        public String getDemandTitle() { return demandTitle; }
        public void setDemandTitle(String demandTitle) { this.demandTitle = demandTitle; }
        public String getDemandDesc() { return demandDesc; }
        public void setDemandDesc(String demandDesc) { this.demandDesc = demandDesc; }
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        public String getDemandStatus() { return demandStatus; }
        public void setDemandStatus(String demandStatus) { this.demandStatus = demandStatus; }

        public String getCategoryShortName() {
            return categoryShortName;
        }

        public void setCategoryShortName(String categoryShortName) {
            this.categoryShortName = categoryShortName;
        }

        public String getContactName() {
            return contactName;
        }

        public void setContactName(String contactName) {
            this.contactName = contactName;
        }

        public String getContactPhone() {
            return contactPhone;
        }

        public void setContactPhone(String contactPhone) {
            this.contactPhone = contactPhone;
        }

        public String getFormData() {
            return formData;
        }

        public void setFormData(String formData) {
            this.formData = formData;
        }

        public Integer getViewCount() {
            return viewCount;
        }

        public void setViewCount(Integer viewCount) {
            this.viewCount = viewCount;
        }

        public String getCategoryCode() {
            return categoryCode;
        }

        public void setCategoryCode(String categoryCode) {
            this.categoryCode = categoryCode;
        }
    }

    /**
     * 对接用户信息
     */
    @ApiModel("对接用户信息")
    public static class UserInfo {
        @ApiModelProperty("用户ID")
        private Long userId;
        
        @ApiModelProperty("用户名")
        private String userName;
        
        @ApiModelProperty("昵称")
        private String nickName;
        
        @ApiModelProperty("头像")
        private String avatar;
        
        @ApiModelProperty("真实姓名")
        private String realName;
        
        @ApiModelProperty("手机号")
        private String phonenumber;
        
        @ApiModelProperty("当前公司")
        private String currentCompany;
        
        @ApiModelProperty("职位")
        private String positionTitle;

        // getter and setter methods
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getNickName() { return nickName; }
        public void setNickName(String nickName) { this.nickName = nickName; }
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getPhonenumber() { return phonenumber; }
        public void setPhonenumber(String phonenumber) { this.phonenumber = phonenumber; }
        public String getCurrentCompany() { return currentCompany; }
        public void setCurrentCompany(String currentCompany) { this.currentCompany = currentCompany; }
        public String getPositionTitle() { return positionTitle; }
        public void setPositionTitle(String positionTitle) { this.positionTitle = positionTitle; }
    }
}
