{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=template&id=a83bd3b0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955748790}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}