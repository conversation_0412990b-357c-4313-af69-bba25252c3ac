{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\router\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\router\\index.js", "mtime": 1753758425260}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}