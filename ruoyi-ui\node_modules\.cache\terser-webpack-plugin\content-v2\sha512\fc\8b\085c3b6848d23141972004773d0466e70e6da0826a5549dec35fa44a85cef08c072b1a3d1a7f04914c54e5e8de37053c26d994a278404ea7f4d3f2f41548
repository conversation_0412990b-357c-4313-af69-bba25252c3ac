{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-160ca2c0\"],{2472:function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"活动ID\",prop:\"activityId\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入活动ID\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.activityId,callback:function(t){e.$set(e.queryParams,\"activityId\",t)},expression:\"queryParams.activityId\"}})],1),a(\"el-form-item\",{attrs:{label:\"用户ID\",prop:\"userId\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入用户ID\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,\"userId\",t)},expression:\"queryParams.userId\"}})],1),a(\"el-form-item\",{attrs:{label:\"审核状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择审核状态\",clearable:\"\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},[a(\"el-option\",{attrs:{label:\"待审核\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"通过\",value:\"1\"}}),a(\"el-option\",{attrs:{label:\"拒绝\",value:\"2\"}})],1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:xiqing:registration:remove\"],expression:\"['miniapp:xiqing:registration:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:xiqing:registration:export\"],expression:\"['miniapp:xiqing:registration:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.registrationManageList},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"报名ID\",align:\"center\",prop:\"registrationId\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"活动标题\",align:\"center\",prop:\"activityTitle\",\"show-overflow-tooltip\":!0}}),a(\"el-table-column\",{attrs:{label:\"用户ID\",align:\"center\",prop:\"userId\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"报名时间\",align:\"center\",prop:\"registrationTime\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.registrationTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"审核状态\",align:\"center\",prop:\"status\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"0\"===t.row.status?a(\"el-tag\",{attrs:{type:\"warning\"}},[e._v(\"待审核\")]):\"1\"===t.row.status?a(\"el-tag\",{attrs:{type:\"success\"}},[e._v(\"通过\")]):\"2\"===t.row.status?a(\"el-tag\",{attrs:{type:\"danger\"}},[e._v(\"拒绝\")]):e._e()]}}])}),a(\"el-table-column\",{attrs:{label:\"审核人\",align:\"center\",prop:\"auditBy\",width:\"100\"}}),a(\"el-table-column\",{attrs:{label:\"审核时间\",align:\"center\",prop:\"auditTime\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.auditTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:xiqing:registration:query\"],expression:\"['miniapp:xiqing:registration:query']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-view\"},on:{click:function(a){return e.handleView(t.row)}}},[e._v(\"查看\")]),\"0\"===t.row.status?a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:xiqing:registration:audit\"],expression:\"['miniapp:xiqing:registration:audit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-check\"},on:{click:function(a){return e.handleAudit(t.row)}}},[e._v(\"审核\")]):e._e(),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:xiqing:registration:remove\"],expression:\"['miniapp:xiqing:registration:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:\"报名详情\",visible:e.viewOpen,width:\"800px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.viewOpen=t}}},[a(\"el-descriptions\",{attrs:{column:2,border:\"\"}},[a(\"el-descriptions-item\",{attrs:{label:\"报名ID\"}},[e._v(e._s(e.form.registrationId))]),a(\"el-descriptions-item\",{attrs:{label:\"活动标题\"}},[e._v(e._s(e.form.activityTitle))]),a(\"el-descriptions-item\",{attrs:{label:\"用户ID\"}},[e._v(e._s(e.form.userId))]),a(\"el-descriptions-item\",{attrs:{label:\"报名时间\"}},[e._v(e._s(e.parseTime(e.form.registrationTime)))]),a(\"el-descriptions-item\",{attrs:{label:\"审核状态\"}},[\"0\"===e.form.status?a(\"el-tag\",{attrs:{type:\"warning\"}},[e._v(\"待审核\")]):\"1\"===e.form.status?a(\"el-tag\",{attrs:{type:\"success\"}},[e._v(\"通过\")]):\"2\"===e.form.status?a(\"el-tag\",{attrs:{type:\"danger\"}},[e._v(\"拒绝\")]):e._e()],1),a(\"el-descriptions-item\",{attrs:{label:\"审核人\"}},[e._v(e._s(e.form.auditBy))]),a(\"el-descriptions-item\",{attrs:{label:\"审核时间\"}},[e._v(e._s(e.parseTime(e.form.auditTime)))]),a(\"el-descriptions-item\",{attrs:{label:\"审核备注\",span:2}},[e._v(e._s(e.form.auditRemark))])],1),a(\"div\",{staticStyle:{\"margin-top\":\"20px\"}},[a(\"h4\",[e._v(\"报名表单数据：\")]),a(\"el-table\",{staticStyle:{\"margin-top\":\"10px\"},attrs:{data:e.formDataList,border:\"\"}},[a(\"el-table-column\",{attrs:{prop:\"key\",label:\"字段名\",width:\"200\"}}),a(\"el-table-column\",{attrs:{label:\"字段值\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"textarea\"===t.row.type?a(\"div\",{staticClass:\"textarea-content\"},[e._v(\" \"+e._s(t.row.value)+\" \")]):\"radio\"===t.row.type||\"picker\"===t.row.type||\"select\"===t.row.type?a(\"el-tag\",{attrs:{type:\"primary\",size:\"small\"}},[e._v(\" \"+e._s(t.row.value)+\" \")]):\"tel\"===t.row.type||\"phone\"===t.row.type?a(\"span\",{staticClass:\"phone-number\"},[e._v(\" \"+e._s(t.row.value)+\" \")]):\"date\"===t.row.type?a(\"el-tag\",{attrs:{type:\"info\",size:\"small\"}},[e._v(\" \"+e._s(t.row.value)+\" \")]):\"checkbox\"===t.row.type?a(\"div\",{staticClass:\"checkbox-content\"},[e._v(\" \"+e._s(t.row.value)+\" \")]):a(\"span\",[e._v(e._s(t.row.value))])]}}])})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(t){e.viewOpen=!1}}},[e._v(\"关 闭\")])],1)],1),a(\"el-dialog\",{attrs:{title:\"审核报名\",visible:e.auditOpen,width:\"500px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.auditOpen=t}}},[a(\"el-form\",{ref:\"auditForm\",attrs:{model:e.auditForm,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"审核结果\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:e.auditForm.status,callback:function(t){e.$set(e.auditForm,\"status\",t)},expression:\"auditForm.status\"}},[a(\"el-radio\",{attrs:{label:\"1\"}},[e._v(\"通过\")]),a(\"el-radio\",{attrs:{label:\"2\"}},[e._v(\"拒绝\")])],1)],1),a(\"el-form-item\",{attrs:{label:\"审核备注\",prop:\"auditRemark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入审核备注\"},model:{value:e.auditForm.auditRemark,callback:function(t){e.$set(e.auditForm,\"auditRemark\",t)},expression:\"auditForm.auditRemark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitAudit}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:function(t){e.auditOpen=!1}}},[e._v(\"取 消\")])],1)],1)],1)},r=[],n=a(\"5530\"),s=a(\"53ca\"),o=a(\"c14f\"),l=a(\"1da1\"),u=(a(\"a15b\"),a(\"d81d\"),a(\"14d9\"),a(\"b0c0\"),a(\"b64b\"),a(\"d3b7\"),a(\"0643\"),a(\"4e3e\"),a(\"a573\"),a(\"159b\"),a(\"b775\"));function c(e){return Object(u[\"a\"])({url:\"/miniapp/xiqing/registration-manage/list\",method:\"get\",params:e})}function m(e){return Object(u[\"a\"])({url:\"/miniapp/xiqing/registration-manage/\"+e,method:\"get\"})}function p(e){return Object(u[\"a\"])({url:\"/miniapp/xiqing/registration-manage/\"+e,method:\"delete\"})}function d(e){return Object(u[\"a\"])({url:\"/miniapp/xiqing/registration-manage/audit\",method:\"put\",data:e})}var f=a(\"26dc\"),g={name:\"XiqingRegistrationManage\",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,registrationManageList:[],viewOpen:!1,auditOpen:!1,formDataList:[],queryParams:{pageNum:1,pageSize:10,activityId:null,userId:null,status:null},form:{},auditForm:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,c(this.queryParams).then((function(t){e.registrationManageList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.registrationId})),this.multiple=!e.length},handleView:function(e){var t=this,a=e.registrationId;m(a).then(function(){var e=Object(l[\"a\"])(Object(o[\"a\"])().m((function e(a){return Object(o[\"a\"])().w((function(e){while(1)switch(e.n){case 0:return t.form=a.data,e.n=1,t.parseFormData();case 1:t.viewOpen=!0;case 2:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},parseFormData:function(){var e=this;return Object(l[\"a\"])(Object(o[\"a\"])().m((function t(){var a,i,r,n,l;return Object(o[\"a\"])().w((function(t){while(1)switch(t.n){case 0:if(e.formDataList=[],!e.form.formData){t.n=6;break}if(t.p=1,a=JSON.parse(e.form.formData),!Array.isArray(a)){t.n=2;break}a.forEach((function(t){t.name&&void 0!==t.value&&null!==t.value&&\"\"!==t.value&&e.formDataList.push({key:t.label||t.name,value:e.formatFieldValue(t.value,t.type),type:t.type})})),t.n=4;break;case 2:if(\"object\"!==Object(s[\"a\"])(a)){t.n=4;break}return t.n=3,e.getActivityFormConfig();case 3:for(n in i=t.v,r={},i&&i.forEach((function(e){r[e.name]=e.label})),a)void 0!==a[n]&&null!==a[n]&&\"\"!==a[n]&&e.formDataList.push({key:r[n]||n,value:a[n],type:\"text\"});case 4:t.n=6;break;case 5:t.p=5,l=t.v,console.error(\"解析表单数据失败:\",l);case 6:return t.a(2)}}),t,null,[[1,5]])})))()},formatFieldValue:function(e,t){if(void 0===e||null===e||\"\"===e)return\"未填写\";switch(t){case\"checkbox\":return Array.isArray(e)?e.length>0?e.join(\", \"):\"未选择\":e;case\"radio\":case\"picker\":case\"select\":return e||\"未选择\";case\"textarea\":return e;case\"date\":return e||\"未选择\";case\"tel\":case\"phone\":return e;default:return e}},getActivityFormConfig:function(){var e=this;return Object(l[\"a\"])(Object(o[\"a\"])().m((function t(){var a,i;return Object(o[\"a\"])().w((function(t){while(1)switch(t.n){case 0:if(e.form.activityId){t.n=1;break}return t.a(2,null);case 1:return t.p=1,t.n=2,Object(f[\"a\"])(e.form.activityId);case 2:if(a=t.v,!a.data||!a.data.formConfig){t.n=3;break}return t.a(2,JSON.parse(a.data.formConfig));case 3:t.n=5;break;case 4:t.p=4,i=t.v,console.error(\"获取活动表单配置失败:\",i);case 5:return t.a(2,null)}}),t,null,[[1,4]])})))()},handleAudit:function(e){this.auditForm={registrationId:e.registrationId,status:\"1\",auditRemark:\"\"},this.auditOpen=!0},submitAudit:function(){var e=this;d(this.auditForm).then((function(t){e.$modal.msgSuccess(\"审核成功\"),e.auditOpen=!1,e.getList()}))},handleDelete:function(e){var t=this,a=e.registrationId||this.ids;this.$modal.confirm('是否确认删除报名编号为\"'+a+'\"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/xiqing/registration-manage/export\",Object(n[\"a\"])({},this.queryParams),\"registration_manage_\".concat((new Date).getTime(),\".xlsx\"))}}},h=g,v=(a(\"2cf1\"),a(\"2877\")),b=Object(v[\"a\"])(h,i,r,!1,null,\"7d89a976\",null);t[\"default\"]=b.exports},\"26dc\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return r})),a.d(t,\"b\",(function(){return n}));var i=a(\"b775\");function r(e){return Object(i[\"a\"])({url:\"/miniapp/xiqing/activity-config/\"+e,method:\"get\"})}function n(e){return Object(i[\"a\"])({url:\"/miniapp/xiqing/activity-config\",method:\"put\",data:e})}},\"2cf1\":function(e,t,a){\"use strict\";a(\"8f53\")},\"8f53\":function(e,t,a){}}]);", "extractedComments": []}