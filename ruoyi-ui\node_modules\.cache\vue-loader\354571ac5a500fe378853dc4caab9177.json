{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\contact\\index.vue?vue&type=template&id=65b57d7e&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\contact\\index.vue", "mtime": 1753777704620}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}