package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniContactMapper;
import com.ruoyi.miniapp.domain.MiniContact;
import com.ruoyi.miniapp.service.IMiniContactService;

/**
 * 联系人管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
public class MiniContactServiceImpl implements IMiniContactService 
{
    @Autowired
    private MiniContactMapper miniContactMapper;

    /**
     * 查询联系人管理
     * 
     * @param contactId 联系人管理主键
     * @return 联系人管理
     */
    @Override
    public MiniContact selectMiniContactByContactId(Long contactId)
    {
        return miniContactMapper.selectMiniContactByContactId(contactId);
    }

    /**
     * 根据联系人编码查询联系人管理
     * 
     * @param contactCode 联系人编码
     * @return 联系人管理
     */
    @Override
    public MiniContact selectMiniContactByContactCode(String contactCode)
    {
        return miniContactMapper.selectMiniContactByContactCode(contactCode);
    }

    /**
     * 查询联系人管理列表
     * 
     * @param miniContact 联系人管理
     * @return 联系人管理
     */
    @Override
    public List<MiniContact> selectMiniContactList(MiniContact miniContact)
    {
        return miniContactMapper.selectMiniContactList(miniContact);
    }

    /**
     * 查询启用的联系人管理列表（小程序端用）
     * 
     * @return 联系人管理集合
     */
    @Override
    public List<MiniContact> selectEnabledMiniContactList()
    {
        return miniContactMapper.selectEnabledMiniContactList();
    }

    /**
     * 新增联系人管理
     * 
     * @param miniContact 联系人管理
     * @return 结果
     */
    @Override
    public int insertMiniContact(MiniContact miniContact)
    {
        miniContact.setCreateTime(DateUtils.getNowDate());
        return miniContactMapper.insertMiniContact(miniContact);
    }

    /**
     * 修改联系人管理
     * 
     * @param miniContact 联系人管理
     * @return 结果
     */
    @Override
    public int updateMiniContact(MiniContact miniContact)
    {
        miniContact.setUpdateTime(DateUtils.getNowDate());
        return miniContactMapper.updateMiniContact(miniContact);
    }

    /**
     * 批量删除联系人管理
     * 
     * @param contactIds 需要删除的联系人管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniContactByContactIds(Long[] contactIds)
    {
        return miniContactMapper.deleteMiniContactByContactIds(contactIds);
    }

    /**
     * 删除联系人管理信息
     * 
     * @param contactId 联系人管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniContactByContactId(Long contactId)
    {
        return miniContactMapper.deleteMiniContactByContactId(contactId);
    }

    /**
     * 更新联系人排序
     *
     * @param miniContact 联系人信息（包含contactId和sortOrder）
     * @return 结果
     */
    @Override
    public int updateMiniContactSort(MiniContact miniContact)
    {
        return miniContactMapper.updateMiniContactSort(miniContact.getContactId(), miniContact.getSortOrder());
    }
}
