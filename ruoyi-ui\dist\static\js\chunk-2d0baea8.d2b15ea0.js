(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0baea8"],{"38f3":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[l("el-form-item",{attrs:{label:"学院名称",prop:"collegeName"}},[l("el-input",{attrs:{placeholder:"请输入学院名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.collegeName,callback:function(t){e.$set(e.queryParams,"collegeName",t)},expression:"queryParams.collegeName"}})],1),l("el-form-item",{attrs:{label:"学院代码",prop:"collegeCode"}},[l("el-input",{attrs:{placeholder:"请输入学院代码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.collegeCode,callback:function(t){e.$set(e.queryParams,"collegeCode",t)},expression:"queryParams.collegeCode"}})],1),l("el-form-item",{attrs:{label:"院长姓名",prop:"deanName"}},[l("el-input",{attrs:{placeholder:"请输入院长姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deanName,callback:function(t){e.$set(e.queryParams,"deanName",t)},expression:"queryParams.deanName"}})],1),l("el-form-item",{attrs:{label:"状态",prop:"status"}},[l("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[l("el-option",{attrs:{label:"正常",value:"0"}}),l("el-option",{attrs:{label:"停用",value:"1"}})],1)],1),l("el-form-item",[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),l("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),l("el-row",{staticClass:"mb8",attrs:{gutter:10}},[l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:college:add"],expression:"['miniapp:college:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:college:edit"],expression:"['miniapp:college:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:college:remove"],expression:"['miniapp:college:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:college:export"],expression:"['miniapp:college:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),l("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.collegeList},on:{"selection-change":e.handleSelectionChange}},[l("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),l("el-table-column",{attrs:{label:"学院ID",align:"center",prop:"collegeId",width:"80"}}),l("el-table-column",{attrs:{label:"学院名称",align:"center",prop:"collegeName","show-overflow-tooltip":""}}),l("el-table-column",{attrs:{label:"学院代码",align:"center",prop:"collegeCode",width:"100"}}),l("el-table-column",{attrs:{label:"院长姓名",align:"center",prop:"deanName",width:"120"}}),l("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"contactPhone",width:"120"}}),l("el-table-column",{attrs:{label:"联系邮箱",align:"center",prop:"contactEmail","show-overflow-tooltip":""}}),l("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"80"}}),l("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"===t.row.status?l("el-tag",{attrs:{type:"success"}},[e._v("正常")]):l("el-tag",{attrs:{type:"danger"}},[e._v("停用")])]}}])}),l("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),l("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:college:edit"],expression:"['miniapp:college:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(l){return e.handleUpdate(t.row)}}},[e._v("修改")]),l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:college:remove"],expression:"['miniapp:college:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(l){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),l("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[l("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[l("el-row",[l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"学院名称",prop:"collegeName"}},[l("el-input",{attrs:{placeholder:"请输入学院名称"},model:{value:e.form.collegeName,callback:function(t){e.$set(e.form,"collegeName",t)},expression:"form.collegeName"}})],1)],1),l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"学院代码",prop:"collegeCode"}},[l("el-input",{attrs:{placeholder:"请输入学院代码"},model:{value:e.form.collegeCode,callback:function(t){e.$set(e.form,"collegeCode",t)},expression:"form.collegeCode"}})],1)],1)],1),l("el-form-item",{attrs:{label:"学院描述",prop:"collegeDesc"}},[l("el-input",{attrs:{type:"textarea",placeholder:"请输入学院描述",rows:3},model:{value:e.form.collegeDesc,callback:function(t){e.$set(e.form,"collegeDesc",t)},expression:"form.collegeDesc"}})],1),l("el-row",[l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"院长姓名",prop:"deanName"}},[l("el-input",{attrs:{placeholder:"请输入院长姓名"},model:{value:e.form.deanName,callback:function(t){e.$set(e.form,"deanName",t)},expression:"form.deanName"}})],1)],1),l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"联系电话",prop:"contactPhone"}},[l("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.contactPhone,callback:function(t){e.$set(e.form,"contactPhone",t)},expression:"form.contactPhone"}})],1)],1)],1),l("el-row",[l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"联系邮箱",prop:"contactEmail"}},[l("el-input",{attrs:{placeholder:"请输入联系邮箱"},model:{value:e.form.contactEmail,callback:function(t){e.$set(e.form,"contactEmail",t)},expression:"form.contactEmail"}})],1)],1),l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[l("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1)],1),l("el-form-item",{attrs:{label:"学院地址",prop:"address"}},[l("el-input",{attrs:{placeholder:"请输入学院地址"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1),l("el-form-item",{attrs:{label:"学院官网",prop:"websiteUrl"}},[l("el-input",{attrs:{placeholder:"请输入学院官网"},model:{value:e.form.websiteUrl,callback:function(t){e.$set(e.form,"websiteUrl",t)},expression:"form.websiteUrl"}})],1),l("el-row",[l("el-col",{attrs:{span:12}},[l("el-form-item",{attrs:{label:"状态",prop:"status"}},[l("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[l("el-radio",{attrs:{label:"0"}},[e._v("正常")]),l("el-radio",{attrs:{label:"1"}},[e._v("停用")])],1)],1)],1)],1),l("el-form-item",{attrs:{label:"备注",prop:"remark"}},[l("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),l("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],o=l("5530"),n=(l("d81d"),l("d3b7"),l("0643"),l("a573"),l("b775"));function i(e){return Object(n["a"])({url:"/miniapp/college/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/miniapp/college/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/miniapp/college",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/miniapp/college",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/miniapp/college/"+e,method:"delete"})}var p={name:"College",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,collegeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,collegeName:null,collegeCode:null,deanName:null,status:null},form:{},rules:{collegeName:[{required:!0,message:"学院名称不能为空",trigger:"blur"}],collegeCode:[{required:!0,message:"学院代码不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.collegeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={collegeId:null,collegeName:null,collegeCode:null,collegeDesc:null,deanName:null,contactPhone:null,contactEmail:null,address:null,websiteUrl:null,sortOrder:0,status:"0",remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.collegeId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加学院信息"},handleUpdate:function(e){var t=this;this.reset();var l=e.collegeId||this.ids;s(l).then((function(e){t.form=e.data,t.open=!0,t.title="修改学院信息"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.collegeId?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,l=e.collegeId||this.ids;this.$modal.confirm('是否确认删除学院编号为"'+l+'"的数据项？').then((function(){return u(l)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/college/export",Object(o["a"])({},this.queryParams),"学院信息_".concat((new Date).getTime(),".xlsx"))}}},d=p,f=l("2877"),h=Object(f["a"])(d,a,r,!1,null,null,null);t["default"]=h.exports}}]);