{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753944331124}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}