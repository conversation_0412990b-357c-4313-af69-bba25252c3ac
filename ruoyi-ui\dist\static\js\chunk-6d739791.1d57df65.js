(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6d739791"],{"8cb7":function(e,t,r){"use strict";r.d(t,"n",(function(){return n})),r.d(t,"i",(function(){return i})),r.d(t,"k",(function(){return o})),r.d(t,"b",(function(){return l})),r.d(t,"r",(function(){return s})),r.d(t,"f",(function(){return c})),r.d(t,"m",(function(){return u})),r.d(t,"j",(function(){return d})),r.d(t,"a",(function(){return m})),r.d(t,"q",(function(){return p})),r.d(t,"e",(function(){return h})),r.d(t,"c",(function(){return f})),r.d(t,"h",(function(){return v})),r.d(t,"p",(function(){return b})),r.d(t,"o",(function(){return y})),r.d(t,"l",(function(){return g})),r.d(t,"g",(function(){return k})),r.d(t,"d",(function(){return w}));var a=r("b775");function n(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/list",method:"get",params:e})}function i(){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/enabled",method:"get"})}function o(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/"+e,method:"get"})}function l(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory",method:"put",data:e})}function c(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/"+e,method:"delete"})}function u(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/list",method:"get",params:e})}function d(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/"+e,method:"get"})}function m(e){return Object(a["a"])({url:"/miniapp/sensitiveWord",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/miniapp/sensitiveWord",method:"put",data:e})}function h(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/"+e,method:"delete"})}function f(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/check",method:"post",params:{text:e}})}function v(e,t){return Object(a["a"])({url:"/miniapp/sensitiveWord/filter",method:"post",params:{text:e,replacement:t}})}function b(){return Object(a["a"])({url:"/miniapp/sensitiveWord/refresh",method:"post"})}function y(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/list",method:"get",params:e})}function g(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/"+e,method:"get"})}function k(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/"+e,method:"delete"})}function w(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/clean",method:"delete",params:{days:e}})}},b648:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"分类",prop:"categoryId"}},[r("el-select",{attrs:{placeholder:"请选择分类",clearable:""},model:{value:e.queryParams.categoryId,callback:function(t){e.$set(e.queryParams,"categoryId",t)},expression:"queryParams.categoryId"}},e._l(e.categoryList,(function(e){return r("el-option",{key:e.categoryId,attrs:{label:e.categoryName,value:e.categoryId}})})),1)],1),r("el-form-item",{attrs:{label:"敏感词",prop:"wordContent"}},[r("el-input",{attrs:{placeholder:"请输入敏感词内容",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.wordContent,callback:function(t){e.$set(e.queryParams,"wordContent",t)},expression:"queryParams.wordContent"}})],1),r("el-form-item",{attrs:{label:"词类型",prop:"wordType"}},[r("el-select",{attrs:{placeholder:"请选择词类型",clearable:""},model:{value:e.queryParams.wordType,callback:function(t){e.$set(e.queryParams,"wordType",t)},expression:"queryParams.wordType"}},[r("el-option",{attrs:{label:"敏感词",value:"1"}}),r("el-option",{attrs:{label:"白名单",value:"2"}})],1)],1),r("el-form-item",{attrs:{label:"严重程度",prop:"severityLevel"}},[r("el-select",{attrs:{placeholder:"请选择严重程度",clearable:""},model:{value:e.queryParams.severityLevel,callback:function(t){e.$set(e.queryParams,"severityLevel",t)},expression:"queryParams.severityLevel"}},[r("el-option",{attrs:{label:"轻微",value:"1"}}),r("el-option",{attrs:{label:"中等",value:"2"}}),r("el-option",{attrs:{label:"严重",value:"3"}})],1)],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:add"],expression:"['miniapp:sensitiveWord:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:edit"],expression:"['miniapp:sensitiveWord:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:remove"],expression:"['miniapp:sensitiveWord:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:export"],expression:"['miniapp:sensitiveWord:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:refresh"],expression:"['miniapp:sensitiveWord:refresh']"}],attrs:{type:"info",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.handleRefreshCache}},[e._v("刷新缓存")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",size:"mini"},on:{click:e.handleCheckDialog}},[e._v("敏感词检测")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.wordList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"敏感词ID",align:"center",prop:"wordId"}}),r("el-table-column",{attrs:{label:"分类",align:"center",prop:"categoryName"}}),r("el-table-column",{attrs:{label:"敏感词内容",align:"center",prop:"wordContent"}}),r("el-table-column",{attrs:{label:"词类型",align:"center",prop:"wordType"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:"1"===t.row.wordType?"danger":"success"}},[e._v(" "+e._s("1"===t.row.wordType?"敏感词":"白名单")+" ")])]}}])}),r("el-table-column",{attrs:{label:"严重程度",align:"center",prop:"severityLevel"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:e.getSeverityType(t.row.severityLevel)}},[e._v(" "+e._s(e.getSeverityText(t.row.severityLevel))+" ")])]}}])}),r("el-table-column",{attrs:{label:"替换字符",align:"center",prop:"replacementChar"}}),r("el-table-column",{attrs:{label:"命中次数",align:"center",prop:"hitCount"}}),r("el-table-column",{attrs:{label:"最后命中时间",align:"center",prop:"lastHitTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.lastHitTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:edit"],expression:"['miniapp:sensitiveWord:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWord:remove"],expression:"['miniapp:sensitiveWord:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"分类",prop:"categoryId"}},[r("el-select",{attrs:{placeholder:"请选择分类"},model:{value:e.form.categoryId,callback:function(t){e.$set(e.form,"categoryId",t)},expression:"form.categoryId"}},e._l(e.categoryList,(function(e){return r("el-option",{key:e.categoryId,attrs:{label:e.categoryName,value:e.categoryId}})})),1)],1),r("el-form-item",{attrs:{label:"敏感词内容",prop:"wordContent"}},[r("el-input",{attrs:{placeholder:"请输入敏感词内容"},model:{value:e.form.wordContent,callback:function(t){e.$set(e.form,"wordContent",t)},expression:"form.wordContent"}})],1),r("el-form-item",{attrs:{label:"词类型",prop:"wordType"}},[r("el-radio-group",{model:{value:e.form.wordType,callback:function(t){e.$set(e.form,"wordType",t)},expression:"form.wordType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("敏感词")]),r("el-radio",{attrs:{label:"2"}},[e._v("白名单")])],1)],1),r("el-form-item",{attrs:{label:"严重程度",prop:"severityLevel"}},[r("el-radio-group",{model:{value:e.form.severityLevel,callback:function(t){e.$set(e.form,"severityLevel",t)},expression:"form.severityLevel"}},[r("el-radio",{attrs:{label:"1"}},[e._v("轻微")]),r("el-radio",{attrs:{label:"2"}},[e._v("中等")]),r("el-radio",{attrs:{label:"3"}},[e._v("严重")])],1)],1),r("el-form-item",{attrs:{label:"替换字符",prop:"replacementChar"}},[r("el-input",{attrs:{placeholder:"请输入替换字符",maxlength:"10"},model:{value:e.form.replacementChar,callback:function(t){e.$set(e.form,"replacementChar",t)},expression:"form.replacementChar"}})],1),r("el-form-item",{attrs:{label:"正则表达式",prop:"isRegex"}},[r("el-radio-group",{model:{value:e.form.isRegex,callback:function(t){e.$set(e.form,"isRegex",t)},expression:"form.isRegex"}},[r("el-radio",{attrs:{label:"0"}},[e._v("否")]),r("el-radio",{attrs:{label:"1"}},[e._v("是")])],1)],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"敏感词检测",visible:e.checkOpen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.checkOpen=t}}},[r("el-form",{ref:"checkForm",attrs:{model:e.checkForm,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"检测文本"}},[r("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入要检测的文本"},model:{value:e.checkForm.text,callback:function(t){e.$set(e.checkForm,"text",t)},expression:"checkForm.text"}})],1),r("el-form-item",{attrs:{label:"替换字符"}},[r("el-input",{attrs:{placeholder:"请输入替换字符，默认为*",maxlength:"1"},model:{value:e.checkForm.replacement,callback:function(t){e.$set(e.checkForm,"replacement",t)},expression:"checkForm.replacement"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:e.handleCheck}},[e._v("检测敏感词")]),r("el-button",{attrs:{type:"success"},on:{click:e.handleFilter}},[e._v("过滤敏感词")])],1),null!==e.checkResult.contains?r("el-form-item",{attrs:{label:"检测结果"}},[r("el-tag",{attrs:{type:e.checkResult.contains?"danger":"success"}},[e._v(" "+e._s(e.checkResult.contains?"包含敏感词":"未发现敏感词")+" ")])],1):e._e(),e.checkResult.hitWords&&e.checkResult.hitWords.length>0?r("el-form-item",{attrs:{label:"命中敏感词"}},e._l(e.checkResult.hitWords,(function(t){return r("el-tag",{key:t,staticStyle:{"margin-right":"5px"},attrs:{type:"danger"}},[e._v(" "+e._s(t)+" ")])})),1):e._e(),e.checkResult.filteredText?r("el-form-item",{attrs:{label:"过滤结果"}},[r("el-input",{attrs:{type:"textarea",rows:4,readonly:""},model:{value:e.checkResult.filteredText,callback:function(t){e.$set(e.checkResult,"filteredText",t)},expression:"checkResult.filteredText"}})],1):e._e()],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.checkOpen=!1}}},[e._v("关 闭")])],1)],1)],1)},n=[],i=r("5530"),o=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("8cb7")),l={name:"SensitiveWord",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,wordList:[],categoryList:[],title:"",open:!1,checkOpen:!1,queryParams:{pageNum:1,pageSize:10,categoryId:null,wordContent:null,wordType:null,severityLevel:null,status:null},form:{},checkForm:{text:"",replacement:"*"},checkResult:{contains:null,hitWords:[],filteredText:""},rules:{wordContent:[{required:!0,message:"敏感词内容不能为空",trigger:"blur"}],wordType:[{required:!0,message:"词类型不能为空",trigger:"change"}]}}},created:function(){this.getList(),this.getCategoryList()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["m"])(this.queryParams).then((function(t){e.wordList=t.rows,e.total=t.total,e.loading=!1}))},getCategoryList:function(){var e=this;Object(o["i"])().then((function(t){e.categoryList=t.data}))},getSeverityType:function(e){var t={1:"info",2:"warning",3:"danger"};return t[e]||"info"},getSeverityText:function(e){var t={1:"轻微",2:"中等",3:"严重"};return t[e]||"未知"},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={wordId:null,categoryId:null,wordContent:null,wordType:"1",severityLevel:"1",replacementChar:"*",isRegex:"0",status:"0",remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.wordId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加敏感词"},handleUpdate:function(e){var t=this;this.reset();var r=e.wordId||this.ids;Object(o["j"])(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改敏感词"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.wordId?Object(o["q"])(e.form).then((function(){e.$message({type:"success",message:"修改成功!"}),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(){e.$message({type:"success",message:"新增成功!"}),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.wordId||this.ids;this.$confirm('是否确认删除敏感词编号为"'+r+'"的数据项？',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){return Object(o["e"])(r)})).then((function(){t.getList(),t.$message({type:"success",message:"删除成功!"})})).catch((function(){}))},handleExport:function(){this.download("miniapp/sensitiveWord/export",Object(i["a"])({},this.queryParams),"sensitiveWord_".concat((new Date).getTime(),".xlsx"))},handleRefreshCache:function(){var e=this;Object(o["p"])().then((function(){e.$message({type:"success",message:"缓存刷新成功!"})}))},handleCheckDialog:function(){this.checkOpen=!0,this.checkResult={contains:null,hitWords:[],filteredText:""}},handleCheck:function(){var e=this;this.checkForm.text?Object(o["c"])(this.checkForm.text).then((function(t){e.checkResult.contains=t.contains,e.checkResult.hitWords=t.hitWords||[],console.log("检测结果:",t)})).catch((function(t){console.error("检测敏感词失败:",t),e.$message.error("检测敏感词失败")})):this.$message.warning("请输入要检测的文本")},handleFilter:function(){var e=this;this.checkForm.text?Object(o["h"])(this.checkForm.text,this.checkForm.replacement).then((function(t){e.checkResult.filteredText=t.filteredText,e.checkResult.hitWords=t.hitWords||[],console.log("过滤结果:",t)})).catch((function(t){console.error("过滤敏感词失败:",t),e.$message.error("过滤敏感词失败")})):this.$message.warning("请输入要过滤的文本")}}},s=l,c=r("2877"),u=Object(c["a"])(s,a,n,!1,null,null,null);t["default"]=u.exports}}]);