package com.ruoyi.miniapp.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniDemandDocking;
import com.ruoyi.miniapp.service.IMiniDemandDockingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 需求对接Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Api(tags = "需求对接管理")
@RestController
@RequestMapping("/miniapp/docking")
public class MiniDemandDockingController extends BaseController
{
    @Autowired
    private IMiniDemandDockingService miniDemandDockingService;

    /**
     * 查询需求对接列表
     */
    @ApiOperation("查询需求对接列表")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniDemandDocking miniDemandDocking)
    {
        startPage();
        List<MiniDemandDocking> list = miniDemandDockingService.selectMiniDemandDockingList(miniDemandDocking);
        return getDataTable(list);
    }

    /**
     * 导出需求对接列表
     */
    @ApiOperation("导出需求对接列表")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:export')")
    @Log(title = "需求对接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniDemandDocking miniDemandDocking)
    {
        List<MiniDemandDocking> list = miniDemandDockingService.selectMiniDemandDockingList(miniDemandDocking);
        ExcelUtil<MiniDemandDocking> util = new ExcelUtil<MiniDemandDocking>(MiniDemandDocking.class);
        util.exportExcel(response, list, "需求对接数据");
    }

    /**
     * 获取需求对接详细信息
     */
    @ApiOperation("获取需求对接详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:query')")
    @GetMapping(value = "/{dockingId}")
    public AjaxResult getInfo(@ApiParam("对接ID") @PathVariable("dockingId") Long dockingId)
    {
        return AjaxResult.success(miniDemandDockingService.selectMiniDemandDockingByDockingId(dockingId));
    }

    /**
     * 新增需求对接
     */
    @ApiOperation("新增需求对接")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:add')")
    @Log(title = "需求对接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("需求对接信息") @RequestBody MiniDemandDocking miniDemandDocking)
    {
        return toAjax(miniDemandDockingService.insertMiniDemandDocking(miniDemandDocking));
    }

    /**
     * 修改需求对接
     */
    @ApiOperation("修改需求对接")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:edit')")
    @Log(title = "需求对接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("需求对接信息") @RequestBody MiniDemandDocking miniDemandDocking)
    {
        return toAjax(miniDemandDockingService.updateMiniDemandDocking(miniDemandDocking));
    }

    /**
     * 更新联系状态
     */
    @ApiOperation("更新联系状态")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:edit')")
    @Log(title = "需求对接", businessType = BusinessType.UPDATE)
    @PutMapping("/updateContactStatus")
    public AjaxResult updateContactStatus(@RequestBody MiniDemandDocking docking)
    {
        try
        {
            // 设置联系时间为当前时间
            if ("1".equals(docking.getIsContacted()) && docking.getContactTime() == null)
            {
                docking.setContactTime(new Date());
            }


            int result = miniDemandDockingService.updateMiniDemandDocking(docking);

            if (result > 0)
            {
                return AjaxResult.success("联系状态更新成功");
            }
            else
            {
                return error("联系状态更新失败");
            }
        }
        catch (Exception e)
        {
            logger.error("更新联系状态失败", e);
            return error("更新联系状态失败：" + e.getMessage());
        }
    }

    /**
     * 删除需求对接
     */
    @ApiOperation("删除需求对接")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:remove')")
    @Log(title = "需求对接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dockingIds}")
    public AjaxResult remove(@ApiParam("对接ID数组") @PathVariable Long[] dockingIds)
    {
        return toAjax(miniDemandDockingService.deleteMiniDemandDockingByDockingIds(dockingIds));
    }

    /**
     * 获取需求的对接详情列表
     */
    @ApiOperation("获取需求的对接详情列表")
    @PreAuthorize("@ss.hasPermi('miniapp:docking:list')")
    @GetMapping("/demand/{demandId}")
    public AjaxResult getDockingDetailListByDemandId(@ApiParam("需求ID") @PathVariable("demandId") Long demandId)
    {
        List<MiniDemandDocking> list = miniDemandDockingService.getDockingDetailListByDemandId(demandId);
        return AjaxResult.success(list);
    }
}
