{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-3cbc8805\"],{\"1c6b\":function(t,e,a){\"use strict\";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:t.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"需求标题\",prop:\"demandTitle\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入需求标题\",clearable:\"\"},nativeOn:{keyup:function(e){return!e.type.indexOf(\"key\")&&t._k(e.keyCode,\"enter\",13,e.key,\"Enter\")?null:t.handleQuery(e)}},model:{value:t.queryParams.demandTitle,callback:function(e){t.$set(t.queryParams,\"demandTitle\",e)},expression:\"queryParams.demandTitle\"}})],1),a(\"el-form-item\",{attrs:{label:\"对接用户\",prop:\"userName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入用户姓名\",clearable:\"\"},nativeOn:{keyup:function(e){return!e.type.indexOf(\"key\")&&t._k(e.keyCode,\"enter\",13,e.key,\"Enter\")?null:t.handleQuery(e)}},model:{value:t.queryParams.userName,callback:function(e){t.$set(t.queryParams,\"userName\",e)},expression:\"queryParams.userName\"}})],1),a(\"el-form-item\",{attrs:{label:\"对接状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择对接状态\",clearable:\"\"},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,\"status\",e)},expression:\"queryParams.status\"}},[a(\"el-option\",{attrs:{label:\"正常\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"已取消\",value:\"1\"}})],1)],1),a(\"el-form-item\",{attrs:{label:\"对接时间\",prop:\"dockingTime\"}},[a(\"el-date-picker\",{staticStyle:{width:\"240px\"},attrs:{\"value-format\":\"yyyy-MM-dd\",type:\"daterange\",\"range-separator\":\"-\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:\"dateRange\"}})],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:t.handleQuery}},[t._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:t.resetQuery}},[t._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:docking:export\"],expression:\"['miniapp:docking:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:t.handleExport}},[t._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:t.showSearch},on:{\"update:showSearch\":function(e){t.showSearch=e},\"update:show-search\":function(e){t.showSearch=e},queryTable:t.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],attrs:{data:t.dockingList},on:{\"selection-change\":t.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"ID\",align:\"center\",prop:\"dockingId\",width:\"70\"}}),a(\"el-table-column\",{attrs:{label:\"需求标题\",align:\"left\",\"min-width\":\"160\",\"show-overflow-tooltip\":\"\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"div\",[a(\"div\",{staticStyle:{\"font-weight\":\"bold\",color:\"#303133\"}},[t._v(\" \"+t._s(e.row.demandTitle||\"未知需求\")+\" \")]),a(\"div\",{staticStyle:{color:\"#909399\",\"font-size\":\"12px\"}},[t._v(\" ID: \"+t._s(e.row.demandId)+\" \")])])]}}])}),a(\"el-table-column\",{attrs:{label:\"对接用户\",align:\"left\",width:\"130\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"div\",[a(\"div\",{staticStyle:{\"font-weight\":\"bold\"}},[t._v(t._s(e.row.userName))]),a(\"div\",{staticStyle:{color:\"#409EFF\",\"font-size\":\"12px\"}},[t._v(t._s(e.row.userPhone))])])]}}])}),a(\"el-table-column\",{attrs:{label:\"获得联系方式\",align:\"left\",width:\"130\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"div\",[a(\"div\",[t._v(t._s(e.row.contactName))]),a(\"div\",{staticStyle:{color:\"#409EFF\",\"font-size\":\"12px\"}},[t._v(t._s(e.row.contactPhone))]),a(\"el-tag\",{attrs:{size:\"mini\",type:\"0\"===e.row.contactSource?\"primary\":\"success\"}},[t._v(\" \"+t._s(\"0\"===e.row.contactSource?\"后台\":\"发布人\")+\" \")])],1)]}}])}),a(\"el-table-column\",{attrs:{label:\"对接时间\",align:\"center\",width:\"110\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"span\",[t._v(t._s(t.parseTime(e.row.dockingTime,\"{m}-{d} {h}:{i}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"联系状态\",align:\"center\",width:\"80\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-tag\",{attrs:{type:\"1\"===e.row.isContacted?\"success\":\"warning\",size:\"mini\"}},[t._v(\" \"+t._s(\"1\"===e.row.isContacted?\"已联系\":\"未联系\")+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"联系结果\",align:\"center\",width:\"80\",\"show-overflow-tooltip\":\"\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[e.row.contactResult?a(\"span\",[t._v(t._s(e.row.contactResult))]):a(\"span\",{staticStyle:{color:\"#C0C4CC\"}},[t._v(\"-\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",width:\"70\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-tag\",{attrs:{type:\"0\"===e.row.status?\"success\":\"info\",size:\"mini\"}},[t._v(\" \"+t._s(\"0\"===e.row.status?\"正常\":\"已取消\")+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",width:\"180\",fixed:\"right\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:docking:query\"],expression:\"['miniapp:docking:query']\"}],attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return t.handleDetail(e.row)}}},[t._v(\"详情\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:docking:edit\"],expression:\"['miniapp:docking:edit']\"}],attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return t.handleContact(e.row)}}},[t._v(\"联系\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:docking:remove\"],expression:\"['miniapp:docking:remove']\"}],staticStyle:{color:\"#F56C6C\"},attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.total>0,expression:\"total>0\"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{\"update:page\":function(e){return t.$set(t.queryParams,\"pageNum\",e)},\"update:limit\":function(e){return t.$set(t.queryParams,\"pageSize\",e)},pagination:t.getList}}),a(\"el-dialog\",{attrs:{title:\"对接详情\",visible:t.detailDialogVisible,width:\"70%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.detailDialogVisible=e}}},[a(\"div\",{staticClass:\"detail-content\"},[a(\"el-descriptions\",{attrs:{column:2,border:\"\",size:\"medium\"}},[a(\"el-descriptions-item\",{attrs:{label:\"对接ID\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.dockingId)+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"需求ID\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.demandId)+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"需求标题\",span:2,\"label-style\":\"width: 120px;\"}},[a(\"span\",{staticStyle:{\"font-weight\":\"bold\",color:\"#303133\"}},[t._v(\" \"+t._s(t.currentDocking.demandTitle||\"未知需求\")+\" \")])]),a(\"el-descriptions-item\",{attrs:{label:\"用户姓名\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.userName)+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"用户电话\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.userPhone)+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"用户公司\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.userCompany||\"未填写\")+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"用户职位\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.userPosition||\"未填写\")+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"获得联系人\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.contactName)+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"获得电话\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.contactPhone)+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"联系方式来源\",span:2,\"label-style\":\"width: 120px;\"}},[a(\"el-tag\",{attrs:{type:\"0\"===t.currentDocking.contactSource?\"primary\":\"success\",size:\"small\"}},[t._v(\" \"+t._s(\"0\"===t.currentDocking.contactSource?\"后台配置\":\"需求发布人\")+\" \")])],1),a(\"el-descriptions-item\",{attrs:{label:\"对接时间\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.parseTime(t.currentDocking.dockingTime,\"{y}-{m}-{d} {h}:{i}:{s}\"))+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"对接状态\",\"label-style\":\"width: 120px;\"}},[a(\"el-tag\",{attrs:{type:\"0\"===t.currentDocking.status?\"success\":\"info\",size:\"small\"}},[t._v(\" \"+t._s(\"0\"===t.currentDocking.status?\"正常\":\"已取消\")+\" \")])],1),a(\"el-descriptions-item\",{attrs:{label:\"创建时间\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.parseTime(t.currentDocking.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\"))+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"更新时间\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.parseTime(t.currentDocking.updateTime,\"{y}-{m}-{d} {h}:{i}:{s}\"))+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"是否已联系\",\"label-style\":\"width: 120px;\"}},[a(\"el-tag\",{attrs:{type:\"1\"===t.currentDocking.isContacted?\"success\":\"warning\",size:\"small\"}},[t._v(\" \"+t._s(\"1\"===t.currentDocking.isContacted?\"已联系\":\"未联系\")+\" \")])],1),a(\"el-descriptions-item\",{attrs:{label:\"联系结果\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.contactResult||\"无\")+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"联系时间\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.contactTime?t.parseTime(t.currentDocking.contactTime,\"{y}-{m}-{d} {h}:{i}:{s}\"):\"无\")+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"联系备注\",\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.contactNotes||\"无\")+\" \")]),a(\"el-descriptions-item\",{attrs:{label:\"备注\",span:2,\"label-style\":\"width: 120px;\"}},[t._v(\" \"+t._s(t.currentDocking.remark||\"无\")+\" \")])],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(e){t.detailDialogVisible=!1}}},[t._v(\"关 闭\")])],1)]),a(\"el-dialog\",{attrs:{title:\"联系记录\",visible:t.contactDialogVisible,width:\"50%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.contactDialogVisible=e}}},[a(\"el-form\",{ref:\"contactForm\",attrs:{model:t.contactForm,rules:t.contactRules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"是否已联系\",prop:\"isContacted\"}},[a(\"el-radio-group\",{model:{value:t.contactForm.isContacted,callback:function(e){t.$set(t.contactForm,\"isContacted\",e)},expression:\"contactForm.isContacted\"}},[a(\"el-radio\",{attrs:{label:\"0\"}},[t._v(\"未联系\")]),a(\"el-radio\",{attrs:{label:\"1\"}},[t._v(\"已联系\")])],1)],1),\"1\"===t.contactForm.isContacted?a(\"el-form-item\",{attrs:{label:\"联系结果\",prop:\"contactResult\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择联系结果\",clearable:\"\"},model:{value:t.contactForm.contactResult,callback:function(e){t.$set(t.contactForm,\"contactResult\",e)},expression:\"contactForm.contactResult\"}},[a(\"el-option\",{attrs:{label:\"联系成功\",value:\"联系成功\"}}),a(\"el-option\",{attrs:{label:\"无人接听\",value:\"无人接听\"}}),a(\"el-option\",{attrs:{label:\"号码错误\",value:\"号码错误\"}}),a(\"el-option\",{attrs:{label:\"拒绝沟通\",value:\"拒绝沟通\"}}),a(\"el-option\",{attrs:{label:\"稍后联系\",value:\"稍后联系\"}}),a(\"el-option\",{attrs:{label:\"已有合作\",value:\"已有合作\"}}),a(\"el-option\",{attrs:{label:\"不感兴趣\",value:\"不感兴趣\"}}),a(\"el-option\",{attrs:{label:\"其他\",value:\"其他\"}})],1)],1):t._e(),a(\"el-form-item\",{attrs:{label:\"联系备注\",prop:\"contactNotes\"}},[a(\"el-input\",{attrs:{type:\"textarea\",rows:4,placeholder:\"请输入联系备注，如沟通内容、后续计划等\",maxlength:\"500\",\"show-word-limit\":\"\"},model:{value:t.contactForm.contactNotes,callback:function(e){t.$set(t.contactForm,\"contactNotes\",e)},expression:\"contactForm.contactNotes\"}})],1),\"1\"===t.contactForm.isContacted?a(\"el-form-item\",{attrs:{label:\"联系时间\",prop:\"contactTime\"}},[a(\"el-date-picker\",{staticStyle:{width:\"100%\"},attrs:{type:\"datetime\",placeholder:\"选择联系时间\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\"},model:{value:t.contactForm.contactTime,callback:function(e){t.$set(t.contactForm,\"contactTime\",e)},expression:\"contactForm.contactTime\"}})],1):t._e()],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(e){t.contactDialogVisible=!1}}},[t._v(\"取 消\")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.submitContactForm}},[t._v(\"确 定\")])],1)],1)],1)},n=[],l=a(\"5530\"),o=(a(\"d81d\"),a(\"d3b7\"),a(\"0643\"),a(\"a573\"),a(\"b775\"));function s(t){return Object(o[\"a\"])({url:\"/miniapp/docking/list\",method:\"get\",params:t})}function r(t){return Object(o[\"a\"])({url:\"/miniapp/docking/\"+t,method:\"delete\"})}var c={name:\"Docking\",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,dockingList:[],dateRange:[],queryParams:{pageNum:1,pageSize:10,demandTitle:null,userName:null,status:null,dockingTime:null},detailDialogVisible:!1,currentDocking:{},contactDialogVisible:!1,contactForm:{dockingId:null,isContacted:\"0\",contactResult:\"\",contactNotes:\"\",contactTime:\"\"},contactRules:{isContacted:[{required:!0,message:\"请选择是否已联系\",trigger:\"change\"}]}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.params={},null!=this.dateRange&&\"\"!=this.dateRange&&(this.queryParams.params[\"beginDockingTime\"]=this.dateRange[0],this.queryParams.params[\"endDockingTime\"]=this.dateRange[1]),s(this.queryParams).then((function(e){t.dockingList=e.rows,t.total=e.total,t.loading=!1,e.rows&&e.rows.length>0&&(console.log(\"第一条对接记录数据:\",e.rows[0]),console.log(\"需求标题字段:\",e.rows[0].demandTitle))}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.dockingId})),this.single=1!==t.length,this.multiple=!t.length},handleDetail:function(t){this.currentDocking=t,this.detailDialogVisible=!0},handleContact:function(t){this.contactForm={dockingId:t.dockingId,isContacted:t.isContacted||\"0\",contactResult:t.contactResult||\"\",contactNotes:t.contactNotes||\"\",contactTime:t.contactTime||\"\"},this.contactDialogVisible=!0},submitContactForm:function(){var t=this;this.$refs[\"contactForm\"].validate((function(e){e&&(\"1\"!==t.contactForm.isContacted||t.contactForm.contactTime||(t.contactForm.contactTime=t.parseTime(new Date,\"{y}-{m}-{d} {h}:{i}:{s}\")),t.$http.put(\"/miniapp/docking/updateContactStatus\",t.contactForm).then((function(e){200===e.data.code?(t.$modal.msgSuccess(\"联系记录更新成功\"),t.contactDialogVisible=!1,t.getList()):t.$modal.msgError(e.data.msg||\"更新失败\")})).catch((function(){t.$modal.msgError(\"网络错误\")})))}))},handleDelete:function(t){var e=this,a=t.dockingId||this.ids;this.$modal.confirm('是否确认删除对接记录编号为\"'+a+'\"的数据项？').then((function(){return r(a)})).then((function(){e.getList(),e.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/docking/export\",Object(l[\"a\"])({},this.queryParams),\"docking_\".concat((new Date).getTime(),\".xlsx\"))}}},d=c,u=(a(\"84c8\"),a(\"2877\")),m=Object(u[\"a\"])(d,i,n,!1,null,\"0f13cfaf\",null);e[\"default\"]=m.exports},\"84c8\":function(t,e,a){\"use strict\";a(\"9a25\")},\"9a25\":function(t,e,a){}}]);", "extractedComments": []}