{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2d2213da\"],{ca06:function(t,e,a){\"use strict\";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:t.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"活动标题\",prop:\"title\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入活动标题\",clearable:\"\"},nativeOn:{keyup:function(e){return!e.type.indexOf(\"key\")&&t._k(e.keyCode,\"enter\",13,e.key,\"Enter\")?null:t.handleQ<PERSON>y(e)}},model:{value:t.queryParams.title,callback:function(e){t.$set(t.queryParams,\"title\",e)},expression:\"queryParams.title\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,\"status\",e)},expression:\"queryParams.status\"}},t._l(t.dict.type.sys_normal_disable,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:t.handleQuery}},[t._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:t.resetQuery}},[t._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"right-toolbar\",{attrs:{showSearch:t.showSearch},on:{\"update:showSearch\":function(e){t.showSearch=e},\"update:show-search\":function(e){t.showSearch=e},queryTable:t.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],attrs:{data:t.activityList}},[a(\"el-table-column\",{attrs:{label:\"内容ID\",align:\"center\",prop:\"contentId\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"活动标题\",align:\"center\",prop:\"title\",\"show-overflow-tooltip\":!0}}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"80\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"dict-tag\",{attrs:{options:t.dict.type.sys_normal_disable,value:e.row.status}})]}}])}),a(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"180\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"span\",[t._v(t._s(t.parseTime(e.row.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"160\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:xiqing:activity:edit\"],expression:\"['miniapp:xiqing:activity:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v(\"编辑\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.total>0,expression:\"total>0\"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{\"update:page\":function(e){return t.$set(t.queryParams,\"pageNum\",e)},\"update:limit\":function(e){return t.$set(t.queryParams,\"pageSize\",e)},pagination:t.getList}}),a(\"el-dialog\",{attrs:{title:t.title,visible:t.open,width:\"900px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(e){t.open=e}}},[a(\"el-form\",{ref:\"form\",attrs:{model:t.form,rules:t.rules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"活动标题\",prop:\"title\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入活动标题\"},model:{value:t.form.title,callback:function(e){t.$set(t.form,\"title\",e)},expression:\"form.title\"}})],1),a(\"el-form-item\",{attrs:{label:\"活动内容\",prop:\"content\"}},[a(\"editor\",{attrs:{\"min-height\":400},model:{value:t.form.content,callback:function(e){t.$set(t.form,\"content\",e)},expression:\"form.content\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:t.form.status,callback:function(e){t.$set(t.form,\"status\",e)},expression:\"form.status\"}},t._l(t.dict.type.sys_normal_disable,(function(e){return a(\"el-radio\",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label))])})),1)],1),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,\"remark\",e)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.submitForm}},[t._v(\"确 定\")]),a(\"el-button\",{on:{click:t.cancel}},[t._v(\"取 消\")])],1)],1)],1)},i=[],l=a(\"b775\");function n(t){return Object(l[\"a\"])({url:\"/miniapp/xiqing/activity/list\",method:\"get\",params:t})}function o(t){return Object(l[\"a\"])({url:\"/miniapp/xiqing/activity/\"+t,method:\"get\"})}function s(t){return Object(l[\"a\"])({url:\"/miniapp/xiqing/activity\",method:\"put\",data:t})}var u={name:\"XiqingActivity\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,showSearch:!0,total:0,activityList:[],title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,status:null},form:{},rules:{title:[{required:!0,message:\"活动标题不能为空\",trigger:\"blur\"}]}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,n(this.queryParams).then((function(e){t.activityList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={contentId:null,title:null,content:null,status:\"0\",remark:null},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleUpdate:function(t){var e=this;this.reset();var a=t.contentId;o(a).then((function(t){e.form=t.data,e.open=!0,e.title=\"编辑专区活动内容\"}))},submitForm:function(){var t=this;this.$refs.form.validate((function(e){e&&s(t.form).then((function(e){t.$modal.msgSuccess(\"修改成功\"),t.open=!1,t.getList()}))}))}}},c=u,m=a(\"2877\"),p=Object(m[\"a\"])(c,r,i,!1,null,null,null);e[\"default\"]=p.exports}}]);", "extractedComments": []}