{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue", "mtime": 1753931003624}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_barrage", "require", "_config", "name", "data", "loading", "ids", "multiple", "showSearch", "total", "barrageList", "detailOpen", "auditOpen", "batchRejectOpen", "configOpen", "configLoading", "auditTitle", "currentBarrage", "date<PERSON><PERSON><PERSON>", "barrageDetail", "queryParams", "pageNum", "pageSize", "content", "userNickname", "auditStatus", "auditForm", "barrageId", "auditRemark", "batchRejectForm", "rejectReason", "configForm", "rows", "speed", "interval", "auditRules", "batchRejectRules", "required", "message", "trigger", "configRules", "type", "min", "max", "created", "getList", "methods", "_this", "params", "addDateRange", "listBarrage", "then", "response", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "handleDetail", "row", "_this2", "getBarrage", "handleAudit", "submitAudit", "_this3", "auditBarrage", "$modal", "msgSuccess", "handleBatchApprove", "_this4", "msgError", "confirm", "batchApproveBarrage", "catch", "handleBatchReject", "submitBatchReject", "_this5", "$refs", "validate", "valid", "barrageIds", "batchRejectBarrage", "handleDelete", "_this6", "delBarrage", "handleExport", "_this7", "exportBarrage", "$download", "excel", "getAuditStatusType", "status", "statusMap", "getAuditStatusText", "handleConfigManage", "loadBarrageConfig", "_this8", "getBarrageConfig", "config", "submitConfig", "_this9", "config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "toString", "getExistingConfigs", "_this0", "listConfig", "config<PERSON><PERSON>", "allConfigs", "targetConfigs", "filter", "includes", "updateExistingConfigs", "error", "console", "existingConfigs", "_this1", "updateCount", "totalCount", "for<PERSON>ach", "key", "index", "existingConfig", "find", "updateData", "_objectSpread2", "default", "config<PERSON><PERSON><PERSON>", "updateConfig", "concat", "warn", "msgWarning"], "sources": ["src/views/miniapp/content/barrage/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"弹幕内容\" prop=\"content\">\r\n        <el-input\r\n          v-model=\"queryParams.content\"\r\n          placeholder=\"请输入弹幕内容\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"发布者\" prop=\"userNickname\">\r\n        <el-input\r\n          v-model=\"queryParams.userNickname\"\r\n          placeholder=\"请输入发布者昵称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"审核状态\" prop=\"auditStatus\">\r\n        <el-select v-model=\"queryParams.auditStatus\" placeholder=\"请选择审核状态\" clearable>\r\n          <el-option label=\"待审核\" value=\"0\" />\r\n          <el-option label=\"审核通过\" value=\"1\" />\r\n          <el-option label=\"审核拒绝\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-check\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleBatchApprove\"\r\n          v-hasPermi=\"['miniapp:barrage:audit']\"\r\n        >批量通过</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleBatchReject\"\r\n          v-hasPermi=\"['miniapp:barrage:audit']\"\r\n        >批量拒绝</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:barrage:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:barrage:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-setting\"\r\n          size=\"mini\"\r\n          @click=\"handleConfigManage\"\r\n          v-hasPermi=\"['system:config:edit']\"\r\n        >弹幕配置</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"barrageList\" @selection-change=\"handleSelectionChange\" row-key=\"barrageId\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"发布者\" align=\"center\" width=\"140\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"user-info\">\r\n            <el-avatar\r\n              :size=\"40\"\r\n              :src=\"scope.row.userAvatarUrl\"\r\n              class=\"user-avatar\"\r\n              fit=\"cover\"\r\n            >\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </el-avatar>\r\n            <div class=\"user-details\">\r\n              <div class=\"user-nickname\">{{ scope.row.userNickName || '未设置昵称' }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"弹幕内容\" align=\"left\" prop=\"content\" min-width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"barrage-content-cell\">\r\n            {{ scope.row.content }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核状态\" align=\"center\" prop=\"auditStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getAuditStatusType(scope.row.auditStatus)\"\r\n            size=\"small\"\r\n          >\r\n            {{ getAuditStatusText(scope.row.auditStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"发布时间\" align=\"center\" prop=\"createTime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"time-info\">\r\n            <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>\r\n            <div class=\"time-detail\">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核时间\" align=\"center\" prop=\"auditTime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.auditTime\" class=\"time-info\">\r\n            <div>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</div>\r\n            <div class=\"time-detail\">{{ parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}</div>\r\n          </div>\r\n          <span v-else class=\"no-data\">-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"220\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.auditStatus === '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleAudit(scope.row, '1')\"\r\n            v-hasPermi=\"['miniapp:barrage:audit']\"\r\n            style=\"color: #67C23A;\"\r\n          >通过</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.auditStatus === '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"handleAudit(scope.row, '2')\"\r\n            v-hasPermi=\"['miniapp:barrage:audit']\"\r\n            style=\"color: #F56C6C;\"\r\n          >拒绝</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            v-hasPermi=\"['miniapp:barrage:query']\"\r\n          >详情</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:barrage:remove']\"\r\n            style=\"color: #F56C6C;\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 弹幕详情对话框 -->\r\n    <el-dialog title=\"弹幕详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"发布者信息\" :span=\"2\">\r\n          <div class=\"user-info-detail\">\r\n            <el-avatar\r\n              :size=\"50\"\r\n              :src=\"barrageDetail.userAvatarUrl\"\r\n              class=\"user-avatar-detail\"\r\n              fit=\"cover\"\r\n            >\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </el-avatar>\r\n            <div class=\"user-details-detail\">\r\n              <div class=\"user-nickname-detail\">{{ barrageDetail.userNickName || '未设置昵称' }}</div>\r\n            </div>\r\n          </div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"弹幕内容\" :span=\"2\">\r\n          <div class=\"barrage-content\">{{ barrageDetail.content }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核状态\">\r\n          <el-tag :type=\"getAuditStatusType(barrageDetail.auditStatus)\">\r\n            {{ getAuditStatusText(barrageDetail.auditStatus) }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核时间\">\r\n          {{ barrageDetail.auditTime ? parseTime(barrageDetail.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核备注\" :span=\"2\" v-if=\"barrageDetail.auditRemark\">\r\n          {{ barrageDetail.auditRemark }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"发布时间\" :span=\"2\">\r\n          {{ parseTime(barrageDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 审核操作 -->\r\n      <div v-if=\"barrageDetail.auditStatus === '0'\" style=\"margin-top: 20px; text-align: center;\">\r\n        <el-button type=\"success\" @click=\"handleAudit(barrageDetail, '1')\">审核通过</el-button>\r\n        <el-button type=\"danger\" @click=\"handleAudit(barrageDetail, '2')\">审核拒绝</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog :title=\"auditTitle\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"100px\">\r\n        <el-form-item label=\"审核结果\">\r\n          <el-tag :type=\"auditForm.auditStatus === '1' ? 'success' : 'danger'\">\r\n            {{ auditForm.auditStatus === '1' ? '审核通过' : '审核拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"审核备注\">\r\n          <el-input\r\n            v-model=\"auditForm.auditRemark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入审核备注（可选）\"\r\n            :rows=\"4\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\r\n        <el-button @click=\"auditOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量拒绝对话框 -->\r\n    <el-dialog title=\"批量拒绝\" :visible.sync=\"batchRejectOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"batchRejectForm\" :model=\"batchRejectForm\" :rules=\"batchRejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"拒绝原因\" prop=\"rejectReason\">\r\n          <el-input\r\n            v-model=\"batchRejectForm.rejectReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            :rows=\"4\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitBatchReject\">确 定</el-button>\r\n        <el-button @click=\"batchRejectOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 弹幕配置管理对话框 -->\r\n    <el-dialog title=\"弹幕配置管理\" :visible.sync=\"configOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"configForm\" :model=\"configForm\" :rules=\"configRules\" label-width=\"120px\">\r\n        <el-form-item label=\"弹幕行数\" prop=\"rows\">\r\n          <el-input-number\r\n            v-model=\"configForm.rows\"\r\n            :min=\"1\"\r\n            :max=\"2\"\r\n            controls-position=\"right\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span style=\"margin-left: 10px; color: #909399; font-size: 12px;\">最大值为2行</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"弹幕滚动速度\" prop=\"speed\">\r\n          <el-input-number\r\n            v-model=\"configForm.speed\"\r\n            :min=\"1\"\r\n            :max=\"50\"\r\n            controls-position=\"right\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span style=\"margin-left: 10px; color: #909399; font-size: 12px;\">数值越大滚动越快</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"弹幕发送间隔\" prop=\"interval\">\r\n          <el-input-number\r\n            v-model=\"configForm.interval\"\r\n            :min=\"1\"\r\n            :max=\"60\"\r\n            controls-position=\"right\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span style=\"margin-left: 10px; color: #909399; font-size: 12px;\">单位：秒</span>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitConfig\" :loading=\"configLoading\">确 定</el-button>\r\n        <el-button @click=\"configOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listBarrage, getBarrage, delBarrage, auditBarrage, batchApproveBarrage, batchRejectBarrage, exportBarrage, getBarrageConfig } from \"@/api/miniapp/barrage\";\r\nimport { listConfig, updateConfig } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  name: \"MiniBarrage\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 弹幕表格数据\r\n      barrageList: [],\r\n      // 是否显示详情弹出层\r\n      detailOpen: false,\r\n      // 是否显示审核弹出层\r\n      auditOpen: false,\r\n      // 是否显示批量拒绝弹出层\r\n      batchRejectOpen: false,\r\n      // 是否显示配置管理弹出层\r\n      configOpen: false,\r\n      // 配置加载状态\r\n      configLoading: false,\r\n      // 审核标题\r\n      auditTitle: \"\",\r\n      // 当前弹幕\r\n      currentBarrage: {},\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 弹幕详情\r\n      barrageDetail: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        content: null,\r\n        userNickname: null,\r\n        auditStatus: null\r\n      },\r\n      // 审核表单参数\r\n      auditForm: {\r\n        barrageId: null,\r\n        auditStatus: null,\r\n        auditRemark: ''\r\n      },\r\n      // 批量拒绝表单参数\r\n      batchRejectForm: {\r\n        rejectReason: ''\r\n      },\r\n      // 配置表单参数\r\n      configForm: {\r\n        rows: 2,\r\n        speed: 10,\r\n        interval: 6\r\n      },\r\n      // 审核表单校验\r\n      auditRules: {\r\n        // 审核备注改为非必填\r\n      },\r\n      // 批量拒绝表单校验\r\n      batchRejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"拒绝原因不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 配置表单校验\r\n      configRules: {\r\n        rows: [\r\n          { required: true, message: \"弹幕行数不能为空\", trigger: \"blur\" },\r\n          { type: 'number', min: 1, max: 3, message: \"弹幕行数必须在1-3之间\", trigger: \"blur\" }\r\n        ],\r\n        speed: [\r\n          { required: true, message: \"弹幕滚动速度不能为空\", trigger: \"blur\" },\r\n          { type: 'number', min: 1, max: 50, message: \"弹幕滚动速度必须在1-50之间\", trigger: \"blur\" }\r\n        ],\r\n        interval: [\r\n          { required: true, message: \"弹幕发送间隔不能为空\", trigger: \"blur\" },\r\n          { type: 'number', min: 1, max: 60, message: \"弹幕发送间隔必须在1-60秒之间\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询弹幕列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      let params = this.addDateRange(this.queryParams, this.dateRange);\r\n      listBarrage(params).then(response => {\r\n        this.barrageList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.barrageId);\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      getBarrage(row.barrageId).then(response => {\r\n        this.barrageDetail = response.data;\r\n        this.detailOpen = true;\r\n      });\r\n    },\r\n    /** 审核按钮操作 */\r\n    handleAudit(row, auditStatus) {\r\n      this.currentBarrage = row;\r\n      this.auditForm = {\r\n        barrageId: row.barrageId,\r\n        auditStatus: auditStatus,\r\n        auditRemark: ''\r\n      };\r\n      this.auditTitle = auditStatus === '1' ? '审核通过' : '审核拒绝';\r\n      this.auditOpen = true;\r\n    },\r\n    /** 提交审核 */\r\n    submitAudit() {\r\n      // 直接提交，不需要表单验证\r\n      auditBarrage(this.auditForm).then(() => {\r\n        this.$modal.msgSuccess(\"审核成功\");\r\n        this.auditOpen = false;\r\n        this.detailOpen = false;\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 批量审核通过 */\r\n    handleBatchApprove() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要审核的弹幕\");\r\n        return;\r\n      }\r\n      this.$modal.confirm('是否确认批量审核通过选中的弹幕？').then(() => {\r\n        return batchApproveBarrage(this.ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"批量审核通过成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量审核拒绝 */\r\n    handleBatchReject() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要拒绝的弹幕\");\r\n        return;\r\n      }\r\n      this.batchRejectForm.rejectReason = '';\r\n      this.batchRejectOpen = true;\r\n    },\r\n    /** 提交批量拒绝 */\r\n    submitBatchReject() {\r\n      this.$refs.batchRejectForm.validate(valid => {\r\n        if (valid) {\r\n          const params = {\r\n            barrageIds: this.ids,\r\n            rejectReason: this.batchRejectForm.rejectReason\r\n          };\r\n          batchRejectBarrage(params).then(() => {\r\n            this.$modal.msgSuccess(\"批量拒绝成功\");\r\n            this.batchRejectOpen = false;\r\n            this.getList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const barrageIds = row.barrageId || this.ids;\r\n      this.$modal.confirm('是否确认删除弹幕编号为\"' + barrageIds + '\"的数据项？').then(function() {\r\n        return delBarrage(barrageIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      let params = this.addDateRange(this.queryParams, this.dateRange);\r\n      this.$modal.confirm('是否确认导出所有弹幕数据项？').then(() => {\r\n        this.loading = true;\r\n        return exportBarrage(params);\r\n      }).then(response => {\r\n        this.$download.excel(response, '弹幕数据.xlsx');\r\n        this.loading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 获取审核状态类型 */\r\n    getAuditStatusType(status) {\r\n      const statusMap = {\r\n        '0': 'warning',\r\n        '1': 'success',\r\n        '2': 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n    /** 获取审核状态文本 */\r\n    getAuditStatusText(status) {\r\n      const statusMap = {\r\n        '0': '待审核',\r\n        '1': '审核通过',\r\n        '2': '审核拒绝'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n    /** 配置管理按钮操作 */\r\n    handleConfigManage() {\r\n      this.loadBarrageConfig();\r\n      this.configOpen = true;\r\n    },\r\n    /** 加载弹幕配置 */\r\n    loadBarrageConfig() {\r\n      getBarrageConfig().then(response => {\r\n        const config = response.data;\r\n        this.configForm = {\r\n          rows: config.rows || 2,\r\n          speed: config.speed || 10,\r\n          interval: config.interval || 6\r\n        };\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"获取配置失败\");\r\n      });\r\n    },\r\n    /** 提交配置 */\r\n    submitConfig() {\r\n      this.$refs.configForm.validate(valid => {\r\n        if (valid) {\r\n          this.configLoading = true;\r\n\r\n          // 准备要更新的配置键名和值\r\n          const configKeys = [\r\n            'danmaku.display.rows',\r\n            'danmaku.scroll.speed',\r\n            'danmaku.send.interval'\r\n          ];\r\n\r\n          const configValues = [\r\n            this.configForm.rows.toString(),\r\n            this.configForm.speed.toString(),\r\n            this.configForm.interval.toString()\r\n          ];\r\n\r\n          // 先获取所有相关配置\r\n          this.getExistingConfigs(configKeys, configValues);\r\n        }\r\n      });\r\n    },\r\n    /** 获取现有配置 */\r\n    getExistingConfigs(configKeys, configValues) {\r\n      // 查询系统配置，获取这三个配置的详细信息\r\n      listConfig({\r\n        pageNum: 1,\r\n        pageSize: 100,\r\n        configKey: ''  // 获取所有配置，然后过滤\r\n      }).then(response => {\r\n        const allConfigs = response.rows || [];\r\n        const targetConfigs = allConfigs.filter(config =>\r\n          configKeys.includes(config.configKey)\r\n        );\r\n\r\n        // 更新现有配置\r\n        this.updateExistingConfigs(targetConfigs, configKeys, configValues);\r\n      }).catch(error => {\r\n        this.configLoading = false;\r\n        console.error('获取配置列表失败:', error);\r\n        this.$modal.msgError(\"获取配置信息失败\");\r\n      });\r\n    },\r\n    /** 更新现有配置 */\r\n    updateExistingConfigs(existingConfigs, configKeys, configValues) {\r\n      let updateCount = 0;\r\n      const totalCount = configKeys.length;\r\n\r\n      configKeys.forEach((key, index) => {\r\n        const existingConfig = existingConfigs.find(config => config.configKey === key);\r\n\r\n        if (existingConfig) {\r\n          // 更新现有配置\r\n          const updateData = {\r\n            ...existingConfig,\r\n            configValue: configValues[index]\r\n          };\r\n\r\n          updateConfig(updateData).then(() => {\r\n            updateCount++;\r\n            if (updateCount === totalCount) {\r\n              this.configLoading = false;\r\n              this.$modal.msgSuccess(\"配置更新成功\");\r\n              this.configOpen = false;\r\n            }\r\n          }).catch(error => {\r\n            this.configLoading = false;\r\n            console.error(`更新配置 ${key} 失败:`, error);\r\n            this.$modal.msgError(`更新配置失败: ${error.message || '未知错误'}`);\r\n          });\r\n        } else {\r\n          // 配置不存在，记录错误\r\n          console.warn(`配置 ${key} 不存在`);\r\n          updateCount++;\r\n          if (updateCount === totalCount) {\r\n            this.configLoading = false;\r\n            this.$modal.msgWarning(\"部分配置不存在，请联系管理员\");\r\n            this.configOpen = false;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.user-avatar {\r\n  flex-shrink: 0;\r\n  border: 2px solid #f0f0f0;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.user-nickname {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n  text-align: center;\r\n}\r\n\r\n.barrage-content {\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.barrage-content-cell {\r\n  max-width: 300px;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n  line-height: 1.5;\r\n  padding: 8px 0;\r\n}\r\n\r\n.time-info {\r\n  text-align: center;\r\n}\r\n\r\n.time-detail {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 2px;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.el-table .el-table__cell .el-button + .el-button {\r\n  margin-left: 5px;\r\n}\r\n\r\n/* 防止操作按钮换行 */\r\n::v-deep .el-table .small-padding .cell {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n}\r\n\r\n::v-deep .el-table .fixed-width .cell {\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 表格行高度调整 */\r\n::v-deep .el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n::v-deep .el-table .el-table__cell {\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 详情对话框样式 */\r\n.user-info-detail {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.user-avatar-detail {\r\n  flex-shrink: 0;\r\n  border: 2px solid #f0f0f0;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-details-detail {\r\n  flex: 1;\r\n}\r\n\r\n.user-nickname-detail {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1200px) {\r\n  .el-table .el-table__cell .el-button {\r\n    margin: 2px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAwUA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,UAAA;MACA;MACAC,SAAA;MACA;MACAC,eAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACAC,YAAA;QACAC,WAAA;MACA;MACA;MACAC,SAAA;QACAC,SAAA;QACAF,WAAA;QACAG,WAAA;MACA;MACA;MACAC,eAAA;QACAC,YAAA;MACA;MACA;MACAC,UAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACA;MACAC,UAAA;QACA;MAAA,CACA;MACA;MACAC,gBAAA;QACAN,YAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,WAAA;QACAR,IAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,IAAA;UAAAC,GAAA;UAAAC,GAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1C,OAAA;MACA,IAAA2C,MAAA,QAAAC,YAAA,MAAA7B,WAAA,OAAAF,SAAA;MACA,IAAAgC,oBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAArC,WAAA,GAAA0C,QAAA,CAAApB,IAAA;QACAe,KAAA,CAAAtC,KAAA,GAAA2C,QAAA,CAAA3C,KAAA;QACAsC,KAAA,CAAA1C,OAAA;MACA;IACA;IACA,aACAgD,WAAA,WAAAA,YAAA;MACA,KAAAjC,WAAA,CAAAC,OAAA;MACA,KAAAwB,OAAA;IACA;IACA,aACAS,UAAA,WAAAA,WAAA;MACA,KAAApC,SAAA;MACA,KAAAqC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnD,GAAA,GAAAmD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhC,SAAA;MAAA;MACA,KAAApB,QAAA,IAAAkD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,mBAAA,EAAAF,GAAA,CAAAnC,SAAA,EAAAwB,IAAA,WAAAC,QAAA;QACAW,MAAA,CAAA5C,aAAA,GAAAiC,QAAA,CAAAhD,IAAA;QACA2D,MAAA,CAAApD,UAAA;MACA;IACA;IACA,aACAsD,WAAA,WAAAA,YAAAH,GAAA,EAAArC,WAAA;MACA,KAAAR,cAAA,GAAA6C,GAAA;MACA,KAAApC,SAAA;QACAC,SAAA,EAAAmC,GAAA,CAAAnC,SAAA;QACAF,WAAA,EAAAA,WAAA;QACAG,WAAA;MACA;MACA,KAAAZ,UAAA,GAAAS,WAAA;MACA,KAAAb,SAAA;IACA;IACA,WACAsD,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,qBAAA,OAAA1C,SAAA,EAAAyB,IAAA;QACAgB,MAAA,CAAAE,MAAA,CAAAC,UAAA;QACAH,MAAA,CAAAvD,SAAA;QACAuD,MAAA,CAAAxD,UAAA;QACAwD,MAAA,CAAAtB,OAAA;MACA;IACA;IACA,aACA0B,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,SAAAlE,GAAA,CAAAsD,MAAA;QACA,KAAAS,MAAA,CAAAI,QAAA;QACA;MACA;MACA,KAAAJ,MAAA,CAAAK,OAAA,qBAAAvB,IAAA;QACA,WAAAwB,4BAAA,EAAAH,MAAA,CAAAlE,GAAA;MACA,GAAA6C,IAAA;QACAqB,MAAA,CAAA3B,OAAA;QACA2B,MAAA,CAAAH,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,iBAAA,WAAAA,kBAAA;MACA,SAAAvE,GAAA,CAAAsD,MAAA;QACA,KAAAS,MAAA,CAAAI,QAAA;QACA;MACA;MACA,KAAA5C,eAAA,CAAAC,YAAA;MACA,KAAAjB,eAAA;IACA;IACA,aACAiE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAnD,eAAA,CAAAoD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAlC,MAAA;YACAmC,UAAA,EAAAJ,MAAA,CAAAzE,GAAA;YACAwB,YAAA,EAAAiD,MAAA,CAAAlD,eAAA,CAAAC;UACA;UACA,IAAAsD,2BAAA,EAAApC,MAAA,EAAAG,IAAA;YACA4B,MAAA,CAAAV,MAAA,CAAAC,UAAA;YACAS,MAAA,CAAAlE,eAAA;YACAkE,MAAA,CAAAlC,OAAA;UACA;QACA;MACA;IACA;IACA,aACAwC,YAAA,WAAAA,aAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,IAAAH,UAAA,GAAArB,GAAA,CAAAnC,SAAA,SAAArB,GAAA;MACA,KAAA+D,MAAA,CAAAK,OAAA,kBAAAS,UAAA,aAAAhC,IAAA;QACA,WAAAoC,mBAAA,EAAAJ,UAAA;MACA,GAAAhC,IAAA;QACAmC,MAAA,CAAAzC,OAAA;QACAyC,MAAA,CAAAjB,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAY,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAzC,MAAA,QAAAC,YAAA,MAAA7B,WAAA,OAAAF,SAAA;MACA,KAAAmD,MAAA,CAAAK,OAAA,mBAAAvB,IAAA;QACAsC,MAAA,CAAApF,OAAA;QACA,WAAAqF,sBAAA,EAAA1C,MAAA;MACA,GAAAG,IAAA,WAAAC,QAAA;QACAqC,MAAA,CAAAE,SAAA,CAAAC,KAAA,CAAAxC,QAAA;QACAqC,MAAA,CAAApF,OAAA;MACA,GAAAuE,KAAA;IACA;IACA,eACAiB,kBAAA,WAAAA,mBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA,eACAE,kBAAA,WAAAA,mBAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA,eACAG,kBAAA,WAAAA,mBAAA;MACA,KAAAC,iBAAA;MACA,KAAApF,UAAA;IACA;IACA,aACAoF,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,yBAAA,IAAAjD,IAAA,WAAAC,QAAA;QACA,IAAAiD,MAAA,GAAAjD,QAAA,CAAAhD,IAAA;QACA+F,MAAA,CAAApE,UAAA;UACAC,IAAA,EAAAqE,MAAA,CAAArE,IAAA;UACAC,KAAA,EAAAoE,MAAA,CAAApE,KAAA;UACAC,QAAA,EAAAmE,MAAA,CAAAnE,QAAA;QACA;MACA,GAAA0C,KAAA;QACAuB,MAAA,CAAA9B,MAAA,CAAAI,QAAA;MACA;IACA;IACA,WACA6B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAvB,KAAA,CAAAjD,UAAA,CAAAkD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAqB,MAAA,CAAAxF,aAAA;;UAEA;UACA,IAAAyF,UAAA,IACA,wBACA,wBACA,wBACA;UAEA,IAAAC,YAAA,IACAF,MAAA,CAAAxE,UAAA,CAAAC,IAAA,CAAA0E,QAAA,IACAH,MAAA,CAAAxE,UAAA,CAAAE,KAAA,CAAAyE,QAAA,IACAH,MAAA,CAAAxE,UAAA,CAAAG,QAAA,CAAAwE,QAAA,GACA;;UAEA;UACAH,MAAA,CAAAI,kBAAA,CAAAH,UAAA,EAAAC,YAAA;QACA;MACA;IACA;IACA,aACAE,kBAAA,WAAAA,mBAAAH,UAAA,EAAAC,YAAA;MAAA,IAAAG,MAAA;MACA;MACA,IAAAC,kBAAA;QACAxF,OAAA;QACAC,QAAA;QACAwF,SAAA;MACA,GAAA3D,IAAA,WAAAC,QAAA;QACA,IAAA2D,UAAA,GAAA3D,QAAA,CAAApB,IAAA;QACA,IAAAgF,aAAA,GAAAD,UAAA,CAAAE,MAAA,WAAAZ,MAAA;UAAA,OACAG,UAAA,CAAAU,QAAA,CAAAb,MAAA,CAAAS,SAAA;QAAA,CACA;;QAEA;QACAF,MAAA,CAAAO,qBAAA,CAAAH,aAAA,EAAAR,UAAA,EAAAC,YAAA;MACA,GAAA7B,KAAA,WAAAwC,KAAA;QACAR,MAAA,CAAA7F,aAAA;QACAsG,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAR,MAAA,CAAAvC,MAAA,CAAAI,QAAA;MACA;IACA;IACA,aACA0C,qBAAA,WAAAA,sBAAAG,eAAA,EAAAd,UAAA,EAAAC,YAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,WAAA;MACA,IAAAC,UAAA,GAAAjB,UAAA,CAAA5C,MAAA;MAEA4C,UAAA,CAAAkB,OAAA,WAAAC,GAAA,EAAAC,KAAA;QACA,IAAAC,cAAA,GAAAP,eAAA,CAAAQ,IAAA,WAAAzB,MAAA;UAAA,OAAAA,MAAA,CAAAS,SAAA,KAAAa,GAAA;QAAA;QAEA,IAAAE,cAAA;UACA;UACA,IAAAE,UAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAJ,cAAA;YACAK,WAAA,EAAAzB,YAAA,CAAAmB,KAAA;UAAA,EACA;UAEA,IAAAO,oBAAA,EAAAJ,UAAA,EAAA5E,IAAA;YACAqE,WAAA;YACA,IAAAA,WAAA,KAAAC,UAAA;cACAF,MAAA,CAAAxG,aAAA;cACAwG,MAAA,CAAAlD,MAAA,CAAAC,UAAA;cACAiD,MAAA,CAAAzG,UAAA;YACA;UACA,GAAA8D,KAAA,WAAAwC,KAAA;YACAG,MAAA,CAAAxG,aAAA;YACAsG,OAAA,CAAAD,KAAA,6BAAAgB,MAAA,CAAAT,GAAA,qBAAAP,KAAA;YACAG,MAAA,CAAAlD,MAAA,CAAAI,QAAA,0CAAA2D,MAAA,CAAAhB,KAAA,CAAA9E,OAAA;UACA;QACA;UACA;UACA+E,OAAA,CAAAgB,IAAA,iBAAAD,MAAA,CAAAT,GAAA;UACAH,WAAA;UACA,IAAAA,WAAA,KAAAC,UAAA;YACAF,MAAA,CAAAxG,aAAA;YACAwG,MAAA,CAAAlD,MAAA,CAAAiE,UAAA;YACAf,MAAA,CAAAzG,UAAA;UACA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}