{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-720a24f4\"],{\"2b88\":function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"弹幕内容\",prop:\"content\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入弹幕内容\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.content,callback:function(t){e.$set(e.queryParams,\"content\",t)},expression:\"queryParams.content\"}})],1),a(\"el-form-item\",{attrs:{label:\"发布者\",prop:\"userNickname\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入发布者昵称\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.userNickname,callback:function(t){e.$set(e.queryParams,\"userNickname\",t)},expression:\"queryParams.userNickname\"}})],1),a(\"el-form-item\",{attrs:{label:\"审核状态\",prop:\"auditStatus\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择审核状态\",clearable:\"\"},model:{value:e.queryParams.auditStatus,callback:function(t){e.$set(e.queryParams,\"auditStatus\",t)},expression:\"queryParams.auditStatus\"}},[a(\"el-option\",{attrs:{label:\"待审核\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"审核通过\",value:\"1\"}}),a(\"el-option\",{attrs:{label:\"审核拒绝\",value:\"2\"}})],1)],1),a(\"el-form-item\",{attrs:{label:\"发布时间\"}},[a(\"el-date-picker\",{staticStyle:{width:\"240px\"},attrs:{\"value-format\":\"yyyy-MM-dd\",type:\"daterange\",\"range-separator\":\"-\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:\"dateRange\"}})],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:audit\"],expression:\"['miniapp:barrage:audit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-check\",size:\"mini\",disabled:e.multiple},on:{click:e.handleBatchApprove}},[e._v(\"批量通过\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:audit\"],expression:\"['miniapp:barrage:audit']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-close\",size:\"mini\",disabled:e.multiple},on:{click:e.handleBatchReject}},[e._v(\"批量拒绝\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:remove\"],expression:\"['miniapp:barrage:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:export\"],expression:\"['miniapp:barrage:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"system:config:edit\"],expression:\"['system:config:edit']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-setting\",size:\"mini\"},on:{click:e.handleConfigManage}},[e._v(\"弹幕配置\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.barrageList,\"row-key\":\"barrageId\"},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"发布者\",align:\"center\",width:\"140\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"user-info\"},[a(\"el-avatar\",{staticClass:\"user-avatar\",attrs:{size:40,src:t.row.userAvatarUrl,fit:\"cover\"}},[a(\"i\",{staticClass:\"el-icon-user-solid\"})]),a(\"div\",{staticClass:\"user-details\"},[a(\"div\",{staticClass:\"user-nickname\"},[e._v(e._s(t.row.userNickName||\"未设置昵称\"))])])],1)]}}])}),a(\"el-table-column\",{attrs:{label:\"弹幕内容\",align:\"left\",prop:\"content\",\"min-width\":\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"barrage-content-cell\"},[e._v(\" \"+e._s(t.row.content)+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"审核状态\",align:\"center\",prop:\"auditStatus\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-tag\",{attrs:{type:e.getAuditStatusType(t.row.auditStatus),size:\"small\"}},[e._v(\" \"+e._s(e.getAuditStatusText(t.row.auditStatus))+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"发布时间\",align:\"center\",prop:\"createTime\",width:\"150\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"time-info\"},[a(\"div\",[e._v(e._s(e.parseTime(t.row.createTime,\"{y}-{m}-{d}\")))]),a(\"div\",{staticClass:\"time-detail\"},[e._v(e._s(e.parseTime(t.row.createTime,\"{h}:{i}:{s}\")))])])]}}])}),a(\"el-table-column\",{attrs:{label:\"审核时间\",align:\"center\",prop:\"auditTime\",width:\"150\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[t.row.auditTime?a(\"div\",{staticClass:\"time-info\"},[a(\"div\",[e._v(e._s(e.parseTime(t.row.auditTime,\"{y}-{m}-{d}\")))]),a(\"div\",{staticClass:\"time-detail\"},[e._v(e._s(e.parseTime(t.row.auditTime,\"{h}:{i}:{s}\")))])]):a(\"span\",{staticClass:\"no-data\"},[e._v(\"-\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"220\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"0\"===t.row.auditStatus?a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:audit\"],expression:\"['miniapp:barrage:audit']\"}],staticStyle:{color:\"#67C23A\"},attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-check\"},on:{click:function(a){return e.handleAudit(t.row,\"1\")}}},[e._v(\"通过\")]):e._e(),\"0\"===t.row.auditStatus?a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:audit\"],expression:\"['miniapp:barrage:audit']\"}],staticStyle:{color:\"#F56C6C\"},attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-close\"},on:{click:function(a){return e.handleAudit(t.row,\"2\")}}},[e._v(\"拒绝\")]):e._e(),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:query\"],expression:\"['miniapp:barrage:query']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-view\"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v(\"详情\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:barrage:remove\"],expression:\"['miniapp:barrage:remove']\"}],staticStyle:{color:\"#F56C6C\"},attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:\"弹幕详情\",visible:e.detailOpen,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.detailOpen=t}}},[a(\"el-descriptions\",{attrs:{column:2,border:\"\"}},[a(\"el-descriptions-item\",{attrs:{label:\"发布者信息\",span:2}},[a(\"div\",{staticClass:\"user-info-detail\"},[a(\"el-avatar\",{staticClass:\"user-avatar-detail\",attrs:{size:50,src:e.barrageDetail.userAvatarUrl,fit:\"cover\"}},[a(\"i\",{staticClass:\"el-icon-user-solid\"})]),a(\"div\",{staticClass:\"user-details-detail\"},[a(\"div\",{staticClass:\"user-nickname-detail\"},[e._v(e._s(e.barrageDetail.userNickName||\"未设置昵称\"))])])],1)]),a(\"el-descriptions-item\",{attrs:{label:\"弹幕内容\",span:2}},[a(\"div\",{staticClass:\"barrage-content\"},[e._v(e._s(e.barrageDetail.content))])]),a(\"el-descriptions-item\",{attrs:{label:\"审核状态\"}},[a(\"el-tag\",{attrs:{type:e.getAuditStatusType(e.barrageDetail.auditStatus)}},[e._v(\" \"+e._s(e.getAuditStatusText(e.barrageDetail.auditStatus))+\" \")])],1),a(\"el-descriptions-item\",{attrs:{label:\"审核时间\"}},[e._v(\" \"+e._s(e.barrageDetail.auditTime?e.parseTime(e.barrageDetail.auditTime,\"{y}-{m}-{d} {h}:{i}:{s}\"):\"-\")+\" \")]),e.barrageDetail.auditRemark?a(\"el-descriptions-item\",{attrs:{label:\"审核备注\",span:2}},[e._v(\" \"+e._s(e.barrageDetail.auditRemark)+\" \")]):e._e(),a(\"el-descriptions-item\",{attrs:{label:\"发布时间\",span:2}},[e._v(\" \"+e._s(e.parseTime(e.barrageDetail.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\"))+\" \")])],1),\"0\"===e.barrageDetail.auditStatus?a(\"div\",{staticStyle:{\"margin-top\":\"20px\",\"text-align\":\"center\"}},[a(\"el-button\",{attrs:{type:\"success\"},on:{click:function(t){return e.handleAudit(e.barrageDetail,\"1\")}}},[e._v(\"审核通过\")]),a(\"el-button\",{attrs:{type:\"danger\"},on:{click:function(t){return e.handleAudit(e.barrageDetail,\"2\")}}},[e._v(\"审核拒绝\")])],1):e._e()],1),a(\"el-dialog\",{attrs:{title:e.auditTitle,visible:e.auditOpen,width:\"500px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.auditOpen=t}}},[a(\"el-form\",{ref:\"auditForm\",attrs:{model:e.auditForm,rules:e.auditRules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"审核结果\"}},[a(\"el-tag\",{attrs:{type:\"1\"===e.auditForm.auditStatus?\"success\":\"danger\"}},[e._v(\" \"+e._s(\"1\"===e.auditForm.auditStatus?\"审核通过\":\"审核拒绝\")+\" \")])],1),a(\"el-form-item\",{attrs:{label:\"审核备注\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入审核备注（可选）\",rows:4},model:{value:e.auditForm.auditRemark,callback:function(t){e.$set(e.auditForm,\"auditRemark\",t)},expression:\"auditForm.auditRemark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitAudit}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:function(t){e.auditOpen=!1}}},[e._v(\"取 消\")])],1)],1),a(\"el-dialog\",{attrs:{title:\"批量拒绝\",visible:e.batchRejectOpen,width:\"500px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.batchRejectOpen=t}}},[a(\"el-form\",{ref:\"batchRejectForm\",attrs:{model:e.batchRejectForm,rules:e.batchRejectRules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"拒绝原因\",prop:\"rejectReason\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入拒绝原因\",rows:4},model:{value:e.batchRejectForm.rejectReason,callback:function(t){e.$set(e.batchRejectForm,\"rejectReason\",t)},expression:\"batchRejectForm.rejectReason\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitBatchReject}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:function(t){e.batchRejectOpen=!1}}},[e._v(\"取 消\")])],1)],1),a(\"el-dialog\",{attrs:{title:\"弹幕配置管理\",visible:e.configOpen,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.configOpen=t}}},[a(\"el-form\",{ref:\"configForm\",attrs:{model:e.configForm,rules:e.configRules,\"label-width\":\"120px\"}},[a(\"el-form-item\",{attrs:{label:\"弹幕行数\",prop:\"rows\"}},[a(\"el-input-number\",{staticStyle:{width:\"200px\"},attrs:{min:1,max:2,\"controls-position\":\"right\"},model:{value:e.configForm.rows,callback:function(t){e.$set(e.configForm,\"rows\",t)},expression:\"configForm.rows\"}}),a(\"span\",{staticStyle:{\"margin-left\":\"10px\",color:\"#909399\",\"font-size\":\"12px\"}},[e._v(\"最大值为2行\")])],1),a(\"el-form-item\",{attrs:{label:\"弹幕滚动速度\",prop:\"speed\"}},[a(\"el-input-number\",{staticStyle:{width:\"200px\"},attrs:{min:1,max:50,\"controls-position\":\"right\"},model:{value:e.configForm.speed,callback:function(t){e.$set(e.configForm,\"speed\",t)},expression:\"configForm.speed\"}}),a(\"span\",{staticStyle:{\"margin-left\":\"10px\",color:\"#909399\",\"font-size\":\"12px\"}},[e._v(\"数值越大滚动越快\")])],1),a(\"el-form-item\",{attrs:{label:\"弹幕发送间隔\",prop:\"interval\"}},[a(\"el-input-number\",{staticStyle:{width:\"200px\"},attrs:{min:1,max:60,\"controls-position\":\"right\"},model:{value:e.configForm.interval,callback:function(t){e.$set(e.configForm,\"interval\",t)},expression:\"configForm.interval\"}}),a(\"span\",{staticStyle:{\"margin-left\":\"10px\",color:\"#909399\",\"font-size\":\"12px\"}},[e._v(\"单位：秒\")])],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\",loading:e.configLoading},on:{click:e.submitConfig}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:function(t){e.configOpen=!1}}},[e._v(\"取 消\")])],1)],1)],1)},r=[],n=a(\"5530\"),s=(a(\"4de4\"),a(\"7db0\"),a(\"caad\"),a(\"d81d\"),a(\"d3b7\"),a(\"25f0\"),a(\"2532\"),a(\"0643\"),a(\"2382\"),a(\"fffc\"),a(\"4e3e\"),a(\"a573\"),a(\"159b\"),a(\"b775\"));function o(e){return Object(s[\"a\"])({url:\"/miniapp/barrage/list\",method:\"post\",data:e})}function l(e){return Object(s[\"a\"])({url:\"/miniapp/barrage/getInfo\",method:\"post\",data:e})}function c(e){var t=Array.isArray(e)?e:[e];return Object(s[\"a\"])({url:\"/miniapp/barrage/remove\",method:\"post\",data:t})}function u(e){return Object(s[\"a\"])({url:\"/miniapp/barrage/export\",method:\"post\",data:e})}function d(e){return Object(s[\"a\"])({url:\"/miniapp/barrage/audit\",method:\"post\",data:e})}function m(e){return Object(s[\"a\"])({url:\"/miniapp/barrage/batchApprove\",method:\"post\",data:e})}function p(e){return Object(s[\"a\"])({url:\"/miniapp/barrage/batchReject\",method:\"post\",data:e})}function g(){return Object(s[\"a\"])({url:\"/miniapp/barrage/app/getConfig\",method:\"post\"})}var f=a(\"c0c3\"),h={name:\"MiniBarrage\",data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,barrageList:[],detailOpen:!1,auditOpen:!1,batchRejectOpen:!1,configOpen:!1,configLoading:!1,auditTitle:\"\",currentBarrage:{},dateRange:[],barrageDetail:{},queryParams:{pageNum:1,pageSize:10,content:null,userNickname:null,auditStatus:null},auditForm:{barrageId:null,auditStatus:null,auditRemark:\"\"},batchRejectForm:{rejectReason:\"\"},configForm:{rows:2,speed:10,interval:6},auditRules:{},batchRejectRules:{rejectReason:[{required:!0,message:\"拒绝原因不能为空\",trigger:\"blur\"}]},configRules:{rows:[{required:!0,message:\"弹幕行数不能为空\",trigger:\"blur\"},{type:\"number\",min:1,max:3,message:\"弹幕行数必须在1-3之间\",trigger:\"blur\"}],speed:[{required:!0,message:\"弹幕滚动速度不能为空\",trigger:\"blur\"},{type:\"number\",min:1,max:50,message:\"弹幕滚动速度必须在1-50之间\",trigger:\"blur\"}],interval:[{required:!0,message:\"弹幕发送间隔不能为空\",trigger:\"blur\"},{type:\"number\",min:1,max:60,message:\"弹幕发送间隔必须在1-60秒之间\",trigger:\"blur\"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0;var t=this.addDateRange(this.queryParams,this.dateRange);o(t).then((function(t){e.barrageList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.barrageId})),this.multiple=!e.length},handleDetail:function(e){var t=this;l(e.barrageId).then((function(e){t.barrageDetail=e.data,t.detailOpen=!0}))},handleAudit:function(e,t){this.currentBarrage=e,this.auditForm={barrageId:e.barrageId,auditStatus:t,auditRemark:\"\"},this.auditTitle=\"1\"===t?\"审核通过\":\"审核拒绝\",this.auditOpen=!0},submitAudit:function(){var e=this;d(this.auditForm).then((function(){e.$modal.msgSuccess(\"审核成功\"),e.auditOpen=!1,e.detailOpen=!1,e.getList()}))},handleBatchApprove:function(){var e=this;0!==this.ids.length?this.$modal.confirm(\"是否确认批量审核通过选中的弹幕？\").then((function(){return m(e.ids)})).then((function(){e.getList(),e.$modal.msgSuccess(\"批量审核通过成功\")})).catch((function(){})):this.$modal.msgError(\"请选择要审核的弹幕\")},handleBatchReject:function(){0!==this.ids.length?(this.batchRejectForm.rejectReason=\"\",this.batchRejectOpen=!0):this.$modal.msgError(\"请选择要拒绝的弹幕\")},submitBatchReject:function(){var e=this;this.$refs.batchRejectForm.validate((function(t){if(t){var a={barrageIds:e.ids,rejectReason:e.batchRejectForm.rejectReason};p(a).then((function(){e.$modal.msgSuccess(\"批量拒绝成功\"),e.batchRejectOpen=!1,e.getList()}))}}))},handleDelete:function(e){var t=this,a=e.barrageId||this.ids;this.$modal.confirm('是否确认删除弹幕编号为\"'+a+'\"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){var e=this,t=this.addDateRange(this.queryParams,this.dateRange);this.$modal.confirm(\"是否确认导出所有弹幕数据项？\").then((function(){return e.loading=!0,u(t)})).then((function(t){e.$download.excel(t,\"弹幕数据.xlsx\"),e.loading=!1})).catch((function(){}))},getAuditStatusType:function(e){var t={0:\"warning\",1:\"success\",2:\"danger\"};return t[e]||\"info\"},getAuditStatusText:function(e){var t={0:\"待审核\",1:\"审核通过\",2:\"审核拒绝\"};return t[e]||\"未知\"},handleConfigManage:function(){this.loadBarrageConfig(),this.configOpen=!0},loadBarrageConfig:function(){var e=this;g().then((function(t){var a=t.data;e.configForm={rows:a.rows||2,speed:a.speed||10,interval:a.interval||6}})).catch((function(){e.$modal.msgError(\"获取配置失败\")}))},submitConfig:function(){var e=this;this.$refs.configForm.validate((function(t){if(t){e.configLoading=!0;var a=[\"danmaku.display.rows\",\"danmaku.scroll.speed\",\"danmaku.send.interval\"],i=[e.configForm.rows.toString(),e.configForm.speed.toString(),e.configForm.interval.toString()];e.getExistingConfigs(a,i)}}))},getExistingConfigs:function(e,t){var a=this;Object(f[\"e\"])({pageNum:1,pageSize:100,configKey:\"\"}).then((function(i){var r=i.rows||[],n=r.filter((function(t){return e.includes(t.configKey)}));a.updateExistingConfigs(n,e,t)})).catch((function(e){a.configLoading=!1,console.error(\"获取配置列表失败:\",e),a.$modal.msgError(\"获取配置信息失败\")}))},updateExistingConfigs:function(e,t,a){var i=this,r=0,s=t.length;t.forEach((function(t,o){var l=e.find((function(e){return e.configKey===t}));if(l){var c=Object(n[\"a\"])(Object(n[\"a\"])({},l),{},{configValue:a[o]});Object(f[\"g\"])(c).then((function(){r++,r===s&&(i.configLoading=!1,i.$modal.msgSuccess(\"配置更新成功\"),i.configOpen=!1)})).catch((function(e){i.configLoading=!1,console.error(\"更新配置 \".concat(t,\" 失败:\"),e),i.$modal.msgError(\"更新配置失败: \".concat(e.message||\"未知错误\"))}))}else console.warn(\"配置 \".concat(t,\" 不存在\")),r++,r===s&&(i.configLoading=!1,i.$modal.msgWarning(\"部分配置不存在，请联系管理员\"),i.configOpen=!1)}))}}},b=h,v=(a(\"bdf9\"),a(\"2877\")),y=Object(v[\"a\"])(b,i,r,!1,null,\"6dfab129\",null);t[\"default\"]=y.exports},bdf9:function(e,t,a){\"use strict\";a(\"c86c\")},c86c:function(e,t,a){}}]);", "extractedComments": []}