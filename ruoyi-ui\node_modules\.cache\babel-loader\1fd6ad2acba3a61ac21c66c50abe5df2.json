{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\park.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\park.js", "mtime": 1753694616736}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPark", "query", "request", "url", "method", "params", "getPark", "parkId", "addPark", "data", "updatePark", "delPark", "exportPark", "getParkIntroImage", "updateParkIntroImage", "imageUrl", "introImageUrl"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/park.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询园区管理列表\r\nexport function listPark(query) {\r\n  return request({\r\n    url: '/miniapp/park/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询园区管理详细\r\nexport function getPark(parkId) {\r\n  return request({\r\n    url: '/miniapp/park/' + parkId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增园区管理\r\nexport function addPark(data) {\r\n  return request({\r\n    url: '/miniapp/park',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改园区管理\r\nexport function updatePark(data) {\r\n  return request({\r\n    url: '/miniapp/park',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除园区管理\r\nexport function delPark(parkId) {\r\n  return request({\r\n    url: '/miniapp/park/' + parkId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出园区管理\r\nexport function exportPark(query) {\r\n  return request({\r\n    url: '/miniapp/park/export',\r\n    method: 'post',\r\n    data: query\r\n  })\r\n}\r\n\r\n// 获取园区简介图片\r\nexport function getParkIntroImage() {\r\n  return request({\r\n    url: '/miniapp/park/config/intro',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新园区简介图片\r\nexport function updateParkIntroImage(imageUrl) {\r\n  return request({\r\n    url: '/miniapp/park/config/intro',\r\n    method: 'put',\r\n    data: { introImageUrl: imageUrl }\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,MAAM;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,MAAM;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,UAAUA,CAACX,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAER;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,oBAAoBA,CAACC,QAAQ,EAAE;EAC7C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAE;MAAEO,aAAa,EAAED;IAAS;EAClC,CAAC,CAAC;AACJ", "ignoreList": []}]}