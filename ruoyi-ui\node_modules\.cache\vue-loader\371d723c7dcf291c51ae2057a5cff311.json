{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\consultation\\index.vue?vue&type=style&index=0&id=835edeac&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\consultation\\index.vue", "mtime": 1753943808699}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouY29udGFjdC1pbmZvLWNvbnRhaW5lciB7DQogIG1heC13aWR0aDogNjAwcHg7DQogIG1hcmdpbjogMjBweCBhdXRvOw0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5jb250YWN0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMjBweCAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLmNvbnRhY3QtaGVhZGVyIGgzIHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouY29udGFjdC1jb250ZW50IHsNCiAgcGFkZGluZzogMjRweDsNCiAgbWluLWhlaWdodDogMTIwcHg7DQp9DQoNCi5jb250YWN0LWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouY29udGFjdC1pdGVtOmxhc3QtY2hpbGQgew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQouY29udGFjdC1pdGVtIGxhYmVsIHsNCiAgbWluLXdpZHRoOiA4MHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KfQ0KDQouY29udGFjdC1pdGVtIHNwYW4gew0KICBjb2xvcjogIzMwMzEzMzsNCiAgZmxleDogMTsNCiAgd29yZC1icmVhazogYnJlYWstYWxsOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/consultation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"contact-info-container\">\r\n      <div class=\"contact-header\">\r\n        <h3>报名咨询联系方式</h3>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleEdit\"\r\n          v-hasPermi=\"['miniapp:xiqing:consultation:edit']\"\r\n        >编辑</el-button>\r\n      </div>\r\n\r\n      <div class=\"contact-content\" v-loading=\"loading\">\r\n        <div class=\"contact-item\">\r\n          <label>联系人：</label>\r\n          <span>{{ contactInfo.contactName || '暂未设置' }}</span>\r\n        </div>\r\n        <div class=\"contact-item\">\r\n          <label>联系方式：</label>\r\n          <span>{{ contactInfo.contactMethod || '暂未设置' }}</span>\r\n        </div>\r\n        <div class=\"contact-item\" v-if=\"contactInfo.remark\">\r\n          <label>备注：</label>\r\n          <span>{{ contactInfo.remark }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 编辑联系信息对话框 -->\r\n    <el-dialog title=\"编辑联系信息\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"联系人\" prop=\"contactName\">\r\n          <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系方式\" prop=\"contactMethod\">\r\n          <el-input v-model=\"form.contactMethod\" placeholder=\"请输入联系方式\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getContactInfo, updateConsultation } from \"@/api/miniapp/xiqing/consultation\";\r\n\r\nexport default {\r\n  name: \"XiqingConsultation\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 联系信息\r\n      contactInfo: {},\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contactName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactMethod: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getContactInfo();\r\n  },\r\n  methods: {\r\n    /** 获取联系信息 */\r\n    getContactInfo() {\r\n      this.loading = true;\r\n      getContactInfo().then(response => {\r\n        this.contactInfo = response.data || {};\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.contactInfo = {};\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        consultationId: null,\r\n        contactName: null,\r\n        contactMethod: null,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 编辑按钮操作 */\r\n    handleEdit() {\r\n      this.reset();\r\n      this.form = {\r\n        consultationId: this.contactInfo.consultationId || null,\r\n        contactName: this.contactInfo.contactName || '',\r\n        contactMethod: this.contactInfo.contactMethod || '',\r\n        status: this.contactInfo.status || \"0\",\r\n        remark: this.contactInfo.remark || ''\r\n      };\r\n      this.open = true;\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          updateConsultation(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"保存成功\");\r\n            this.open = false;\r\n            this.getContactInfo();\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.contact-info-container {\r\n  max-width: 600px;\r\n  margin: 20px auto;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.contact-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contact-content {\r\n  padding: 24px;\r\n  min-height: 120px;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.contact-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.contact-item label {\r\n  min-width: 80px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  margin-right: 12px;\r\n}\r\n\r\n.contact-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n</style>"]}]}