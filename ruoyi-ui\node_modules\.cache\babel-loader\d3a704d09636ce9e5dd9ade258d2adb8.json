{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753760187026}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}