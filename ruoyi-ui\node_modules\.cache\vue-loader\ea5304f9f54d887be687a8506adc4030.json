{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=style&index=0&id=369868a7&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753760187026}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog6K+m5oOF6aG16Z2i5qC35byPICovDQouZGV0YWlsLWNvbnRlbnQgew0KICBtYXgtaGVpZ2h0OiA2MDBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg0KLmluZm8tc2VjdGlvbiwgLmZvcm0tZGF0YS1zZWN0aW9uLCAuZG9ja2luZy1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnNlY3Rpb24taGVhZGVyIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmctYm90dG9tOiA4cHg7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjNDA5RUZGOw0KfQ0KDQouaW5mby1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCn0NCg0KLmluZm8tbGFiZWwgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIG1pbi13aWR0aDogMTAwcHg7DQogIGZsZXgtc2hyaW5rOiAwOw0KfQ0KDQouaW5mby12YWx1ZSB7DQogIGZsZXg6IDE7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouZGVzY3JpcHRpb24tdGV4dCB7DQogIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsNCiAgd29yZC1icmVhazogYnJlYWstd29yZDsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgcGFkZGluZzogOHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQp9DQoNCi50ZXh0YXJlYS1jb250ZW50IHsNCiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOw0KICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KICBsaW5lLWhlaWdodDogMS41Ow0KICBtYXgtaGVpZ2h0OiAxMjBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgcGFkZGluZzogOHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQp9DQoNCi5waG9uZS1udW1iZXIgew0KICBjb2xvcjogIzQwOWVmZjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmZpbGUtaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDVweDsNCn0NCg0KLmZpbGUtbGluayB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICB0ZXh0LWRlY29yYXRpb246IG5vbmU7DQogIG1hcmdpbi1sZWZ0OiA1cHg7DQp9DQoNCi5maWxlLWxpbms6aG92ZXIgew0KICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsNCn0NCg0KLyog5a+55o6l6K6w5b2V6KGo5qC85qC35byPICovDQouZG9ja2luZy1zZWN0aW9uIC5lbC10YWJsZSB7DQogIG1hcmdpbi10b3A6IDA7IC8qIOenu+mZpOS4iui+uei3ne+8jOiuqeihqOagvOe0p+i0tOagh+mimCAqLw0KfQ0KDQovKiDnqbrmlbDmja7mj5DnpLrmoLflvI8gKi8NCi5lbXB0eS1kYXRhIHsNCiAgcGFkZGluZzogNDBweCAwOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouZW1wdHktZGF0YSBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBjb2xvcjogI0MwQzRDQzsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi5lbXB0eS1kYXRhIHAgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5kb2NraW5nLXNlY3Rpb24gLnVzZXItbmFtZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLmRvY2tpbmctc2VjdGlvbiAudXNlci1waG9uZSB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQoubm8tZGF0YS1zaW1wbGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5jb250YWN0ZWQgew0KICBjb2xvcjogIzY3YzIzYTsNCiAgbWFyZ2luLXJpZ2h0OiA1cHg7DQp9DQoNCi51bmNvbnRhY3RlZCB7DQogIGNvbG9yOiAjZTZhMjNjOw0KfQ0KDQovKiDooajmoLzmk43kvZzliJfmoLflvI/kvJjljJYgKi8NCi50YWJsZS1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiBub3dyYXA7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBnYXA6IDJweDsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCn0NCg0KLnRhYmxlLWFjdGlvbnMgLmVsLWJ1dHRvbiB7DQogIHBhZGRpbmc6IDRweCA2cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbWFyZ2luOiAwOw0KICBtaW4td2lkdGg6IGF1dG87DQp9DQoNCi50YWJsZS1hY3Rpb25zIC5lbC1idXR0b24gKyAuZWwtYnV0dG9uIHsNCiAgbWFyZ2luLWxlZnQ6IDJweDsNCn0NCg0KLmVsLXRhYmxlIC5zbWFsbC1wYWRkaW5nIC5jZWxsIHsNCiAgcGFkZGluZy1sZWZ0OiA0cHg7DQogIHBhZGRpbmctcmlnaHQ6IDRweDsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgb3ZlcmZsb3c6IHZpc2libGU7DQp9DQoNCi5lbC10YWJsZSAuZml4ZWQtd2lkdGggLmNlbGwgew0KICBwYWRkaW5nLWxlZnQ6IDRweDsNCiAgcGFkZGluZy1yaWdodDogNHB4Ow0KfQ0KDQovKiDooajmoLzliJfpl7Tot53kvJjljJYgKi8NCi5lbC10YWJsZSB0aCwNCi5lbC10YWJsZSB0ZCB7DQogIHBhZGRpbmc6IDZweCAwOw0KfQ0KDQouZWwtdGFibGUgLmNlbGwgew0KICBwYWRkaW5nLWxlZnQ6IDZweDsNCiAgcGFkZGluZy1yaWdodDogNnB4Ow0KfQ0KDQovKiDlr7nmjqXorrDlvZXooajmoLzmoLflvI8gKi8NCi5kb2NraW5nLXRhYmxlIHsNCiAgbWFyZ2luLXRvcDogMDsgLyog56e76Zmk5LiK6L656Led77yM6K6p6KGo5qC857Sn6LS05qCH6aKYICovDQp9DQoNCi5kb2NraW5nLXRhYmxlIC51c2VyLWluZm8gew0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQouZG9ja2luZy10YWJsZSAudXNlci1uYW1lIHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIG1hcmdpbi1ib3R0b206IDRweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmRvY2tpbmctdGFibGUgLnVzZXItbmFtZSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0KDQouZG9ja2luZy10YWJsZSAudXNlci1waG9uZSB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5kb2NraW5nLXRhYmxlIC51c2VyLXBob25lIGkgew0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCiAgY29sb3I6ICM2N0MyM0E7DQp9DQoNCi5kb2NraW5nLXRhYmxlIC53b3JrLWluZm8gew0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQouZG9ja2luZy10YWJsZSAuY29tcGFueSwNCi5kb2NraW5nLXRhYmxlIC5wb3NpdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDRweDsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmRvY2tpbmctdGFibGUgLmNvbXBhbnkgaSwNCi5kb2NraW5nLXRhYmxlIC5wb3NpdGlvbiBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouZG9ja2luZy10YWJsZSAubm8taW5mbyB7DQogIGNvbG9yOiAjQzBDNENDOw0KICBmb250LXNpemU6IDEycHg7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLmRvY2tpbmctdGFibGUgLmRvY2tpbmctdGltZSB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDEycHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouZG9ja2luZy10YWJsZSAuZG9ja2luZy10aW1lIGkgew0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCiAgY29sb3I6ICNFNkEyM0M7DQp9DQoNCi8qIOihqOagvOihjOagt+W8jyAqLw0KLmRvY2tpbmctdGFibGUgLmVsLXRhYmxlX19yb3cgew0KICBjdXJzb3I6IGRlZmF1bHQ7DQp9DQoNCi5kb2NraW5nLXRhYmxlIC5lbC10YWJsZV9fcm93OmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCn0NCg0KLyog5YiG6ZqU57q/5qC35byPICovDQouZWwtZGl2aWRlciB7DQogIG1hcmdpbjogMjBweCAwOw0KfQ0KDQouZWwtZGl2aWRlcl9fdGV4dCB7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5lbC1kaXZpZGVyX190ZXh0IGkgew0KICBtYXJnaW4tcmlnaHQ6IDZweDsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi8qIOivpuaDheW8ueeql+WGheWuueWMuuWfnyAqLw0KLmRvY2tpbmctc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5mb3JtLWRhdGEtc2VjdGlvbiB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoNCi8qIOWIhumalOe6v+agt+W8jyAqLw0KLmVsLWRpdmlkZXIgew0KICBtYXJnaW46IDIwcHggMDsNCn0NCg0KLmVsLWRpdmlkZXJfX3RleHQgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouZWwtZGl2aWRlcl9fdGV4dCBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0KDQovKiDor6bmg4XlvLnnqpflhoXlrrnljLrln58gKi8NCi5kb2NraW5nLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouZm9ybS1kYXRhLXNlY3Rpb24gew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KfQ0K"}, null]}