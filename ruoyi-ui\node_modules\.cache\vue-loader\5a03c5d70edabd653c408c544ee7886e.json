{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue", "mtime": 1753931003624}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0QmFycmFnZSwgZ2V0QmFycmFnZSwgZGVsQmFycmFnZSwgYXVkaXRCYXJyYWdlLCBiYXRjaEFwcHJvdmVCYXJyYWdlLCBiYXRjaFJlamVjdEJhcnJhZ2UsIGV4cG9ydEJhcnJhZ2UsIGdldEJhcnJhZ2VDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL2JhcnJhZ2UiOw0KaW1wb3J0IHsgbGlzdENvbmZpZywgdXBkYXRlQ29uZmlnIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2NvbmZpZyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk1pbmlCYXJyYWdlIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlvLnluZXooajmoLzmlbDmja4NCiAgICAgIGJhcnJhZ2VMaXN0OiBbXSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuivpuaDheW8ueWHuuWxgg0KICAgICAgZGV0YWlsT3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrlrqHmoLjlvLnlh7rlsYINCiAgICAgIGF1ZGl0T3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrmibnph4/mi5Lnu53lvLnlh7rlsYINCiAgICAgIGJhdGNoUmVqZWN0T3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrphY3nva7nrqHnkIblvLnlh7rlsYINCiAgICAgIGNvbmZpZ09wZW46IGZhbHNlLA0KICAgICAgLy8g6YWN572u5Yqg6L2954q25oCBDQogICAgICBjb25maWdMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOWuoeaguOagh+mimA0KICAgICAgYXVkaXRUaXRsZTogIiIsDQogICAgICAvLyDlvZPliY3lvLnluZUNCiAgICAgIGN1cnJlbnRCYXJyYWdlOiB7fSwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOW8ueW5leivpuaDhQ0KICAgICAgYmFycmFnZURldGFpbDoge30sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgY29udGVudDogbnVsbCwNCiAgICAgICAgdXNlck5pY2tuYW1lOiBudWxsLA0KICAgICAgICBhdWRpdFN0YXR1czogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOWuoeaguOihqOWNleWPguaVsA0KICAgICAgYXVkaXRGb3JtOiB7DQogICAgICAgIGJhcnJhZ2VJZDogbnVsbCwNCiAgICAgICAgYXVkaXRTdGF0dXM6IG51bGwsDQogICAgICAgIGF1ZGl0UmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOaJuemHj+aLkue7neihqOWNleWPguaVsA0KICAgICAgYmF0Y2hSZWplY3RGb3JtOiB7DQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0sDQogICAgICAvLyDphY3nva7ooajljZXlj4LmlbANCiAgICAgIGNvbmZpZ0Zvcm06IHsNCiAgICAgICAgcm93czogMiwNCiAgICAgICAgc3BlZWQ6IDEwLA0KICAgICAgICBpbnRlcnZhbDogNg0KICAgICAgfSwNCiAgICAgIC8vIOWuoeaguOihqOWNleagoemqjA0KICAgICAgYXVkaXRSdWxlczogew0KICAgICAgICAvLyDlrqHmoLjlpIfms6jmlLnkuLrpnZ7lv4XloasNCiAgICAgIH0sDQogICAgICAvLyDmibnph4/mi5Lnu53ooajljZXmoKHpqowNCiAgICAgIGJhdGNoUmVqZWN0UnVsZXM6IHsNCiAgICAgICAgcmVqZWN0UmVhc29uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLkue7neWOn+WboOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICAvLyDphY3nva7ooajljZXmoKHpqowNCiAgICAgIGNvbmZpZ1J1bGVzOiB7DQogICAgICAgIHJvd3M6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5by55bmV6KGM5pWw5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDMsIG1lc3NhZ2U6ICLlvLnluZXooYzmlbDlv4XpobvlnKgxLTPkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzcGVlZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlvLnluZXmu5rliqjpgJ/luqbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtaW46IDEsIG1heDogNTAsIG1lc3NhZ2U6ICLlvLnluZXmu5rliqjpgJ/luqblv4XpobvlnKgxLTUw5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgaW50ZXJ2YWw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5by55bmV5Y+R6YCB6Ze06ZqU5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDYwLCBtZXNzYWdlOiAi5by55bmV5Y+R6YCB6Ze06ZqU5b+F6aG75ZyoMS02MOenkuS5i+mXtCIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouW8ueW5leWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKTsNCiAgICAgIGxpc3RCYXJyYWdlKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYmFycmFnZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5iYXJyYWdlSWQpOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLyoqIOivpuaDheaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIGdldEJhcnJhZ2Uocm93LmJhcnJhZ2VJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYmFycmFnZURldGFpbCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlrqHmoLjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBdWRpdChyb3csIGF1ZGl0U3RhdHVzKSB7DQogICAgICB0aGlzLmN1cnJlbnRCYXJyYWdlID0gcm93Ow0KICAgICAgdGhpcy5hdWRpdEZvcm0gPSB7DQogICAgICAgIGJhcnJhZ2VJZDogcm93LmJhcnJhZ2VJZCwNCiAgICAgICAgYXVkaXRTdGF0dXM6IGF1ZGl0U3RhdHVzLA0KICAgICAgICBhdWRpdFJlbWFyazogJycNCiAgICAgIH07DQogICAgICB0aGlzLmF1ZGl0VGl0bGUgPSBhdWRpdFN0YXR1cyA9PT0gJzEnID8gJ+WuoeaguOmAmui/hycgOiAn5a6h5qC45ouS57udJzsNCiAgICAgIHRoaXMuYXVkaXRPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTlrqHmoLggKi8NCiAgICBzdWJtaXRBdWRpdCgpIHsNCiAgICAgIC8vIOebtOaOpeaPkOS6pO+8jOS4jemcgOimgeihqOWNlemqjOivgQ0KICAgICAgYXVkaXRCYXJyYWdlKHRoaXMuYXVkaXRGb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5a6h5qC45oiQ5YqfIik7DQogICAgICAgIHRoaXMuYXVkaXRPcGVuID0gZmFsc2U7DQogICAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaJuemHj+WuoeaguOmAmui/hyAqLw0KICAgIGhhbmRsZUJhdGNoQXBwcm92ZSgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeWuoeaguOeahOW8ueW5lSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmibnph4/lrqHmoLjpgJrov4fpgInkuK3nmoTlvLnluZXvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgcmV0dXJuIGJhdGNoQXBwcm92ZUJhcnJhZ2UodGhpcy5pZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmibnph4/lrqHmoLjpgJrov4fmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDmibnph4/lrqHmoLjmi5Lnu50gKi8NCiAgICBoYW5kbGVCYXRjaFJlamVjdCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeaLkue7neeahOW8ueW5lSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmJhdGNoUmVqZWN0Rm9ybS5yZWplY3RSZWFzb24gPSAnJzsNCiAgICAgIHRoaXMuYmF0Y2hSZWplY3RPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmibnph4/mi5Lnu50gKi8NCiAgICBzdWJtaXRCYXRjaFJlamVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuYmF0Y2hSZWplY3RGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgICAgYmFycmFnZUlkczogdGhpcy5pZHMsDQogICAgICAgICAgICByZWplY3RSZWFzb246IHRoaXMuYmF0Y2hSZWplY3RGb3JtLnJlamVjdFJlYXNvbg0KICAgICAgICAgIH07DQogICAgICAgICAgYmF0Y2hSZWplY3RCYXJyYWdlKHBhcmFtcykudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmibnph4/mi5Lnu53miJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuYmF0Y2hSZWplY3RPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgYmFycmFnZUlkcyA9IHJvdy5iYXJyYWdlSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlvLnluZXnvJblj7fkuLoiJyArIGJhcnJhZ2VJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxCYXJyYWdlKGJhcnJhZ2VJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBsZXQgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5by55bmV5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgIHJldHVybiBleHBvcnRCYXJyYWdlKHBhcmFtcyk7DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kZG93bmxvYWQuZXhjZWwocmVzcG9uc2UsICflvLnluZXmlbDmja4ueGxzeCcpOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrqHmoLjnirbmgIHnsbvlnosgKi8NCiAgICBnZXRBdWRpdFN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICcwJzogJ3dhcm5pbmcnLA0KICAgICAgICAnMSc6ICdzdWNjZXNzJywNCiAgICAgICAgJzInOiAnZGFuZ2VyJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAnaW5mbyc7DQogICAgfSwNCiAgICAvKiog6I635Y+W5a6h5qC454q25oCB5paH5pysICovDQogICAgZ2V0QXVkaXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAnMCc6ICflvoXlrqHmoLgnLA0KICAgICAgICAnMSc6ICflrqHmoLjpgJrov4cnLA0KICAgICAgICAnMic6ICflrqHmoLjmi5Lnu50nDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQogICAgLyoqIOmFjee9rueuoeeQhuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUNvbmZpZ01hbmFnZSgpIHsNCiAgICAgIHRoaXMubG9hZEJhcnJhZ2VDb25maWcoKTsNCiAgICAgIHRoaXMuY29uZmlnT3BlbiA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5Yqg6L295by55bmV6YWN572uICovDQogICAgbG9hZEJhcnJhZ2VDb25maWcoKSB7DQogICAgICBnZXRCYXJyYWdlQ29uZmlnKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnN0IGNvbmZpZyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuY29uZmlnRm9ybSA9IHsNCiAgICAgICAgICByb3dzOiBjb25maWcucm93cyB8fCAyLA0KICAgICAgICAgIHNwZWVkOiBjb25maWcuc3BlZWQgfHwgMTAsDQogICAgICAgICAgaW50ZXJ2YWw6IGNvbmZpZy5pbnRlcnZhbCB8fCA2DQogICAgICAgIH07DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bphY3nva7lpLHotKUiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOmFjee9riAqLw0KICAgIHN1Ym1pdENvbmZpZygpIHsNCiAgICAgIHRoaXMuJHJlZnMuY29uZmlnRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuY29uZmlnTG9hZGluZyA9IHRydWU7DQoNCiAgICAgICAgICAvLyDlh4blpIfopoHmm7TmlrDnmoTphY3nva7plK7lkI3lkozlgLwNCiAgICAgICAgICBjb25zdCBjb25maWdLZXlzID0gWw0KICAgICAgICAgICAgJ2Rhbm1ha3UuZGlzcGxheS5yb3dzJywNCiAgICAgICAgICAgICdkYW5tYWt1LnNjcm9sbC5zcGVlZCcsDQogICAgICAgICAgICAnZGFubWFrdS5zZW5kLmludGVydmFsJw0KICAgICAgICAgIF07DQoNCiAgICAgICAgICBjb25zdCBjb25maWdWYWx1ZXMgPSBbDQogICAgICAgICAgICB0aGlzLmNvbmZpZ0Zvcm0ucm93cy50b1N0cmluZygpLA0KICAgICAgICAgICAgdGhpcy5jb25maWdGb3JtLnNwZWVkLnRvU3RyaW5nKCksDQogICAgICAgICAgICB0aGlzLmNvbmZpZ0Zvcm0uaW50ZXJ2YWwudG9TdHJpbmcoKQ0KICAgICAgICAgIF07DQoNCiAgICAgICAgICAvLyDlhYjojrflj5bmiYDmnInnm7jlhbPphY3nva4NCiAgICAgICAgICB0aGlzLmdldEV4aXN0aW5nQ29uZmlncyhjb25maWdLZXlzLCBjb25maWdWYWx1ZXMpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bnjrDmnInphY3nva4gKi8NCiAgICBnZXRFeGlzdGluZ0NvbmZpZ3MoY29uZmlnS2V5cywgY29uZmlnVmFsdWVzKSB7DQogICAgICAvLyDmn6Xor6Lns7vnu5/phY3nva7vvIzojrflj5bov5nkuInkuKrphY3nva7nmoTor6bnu4bkv6Hmga8NCiAgICAgIGxpc3RDb25maWcoew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAwLA0KICAgICAgICBjb25maWdLZXk6ICcnICAvLyDojrflj5bmiYDmnInphY3nva7vvIznhLblkI7ov4fmu6QNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zdCBhbGxDb25maWdzID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgY29uc3QgdGFyZ2V0Q29uZmlncyA9IGFsbENvbmZpZ3MuZmlsdGVyKGNvbmZpZyA9Pg0KICAgICAgICAgIGNvbmZpZ0tleXMuaW5jbHVkZXMoY29uZmlnLmNvbmZpZ0tleSkNCiAgICAgICAgKTsNCg0KICAgICAgICAvLyDmm7TmlrDnjrDmnInphY3nva4NCiAgICAgICAgdGhpcy51cGRhdGVFeGlzdGluZ0NvbmZpZ3ModGFyZ2V0Q29uZmlncywgY29uZmlnS2V5cywgY29uZmlnVmFsdWVzKTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy5jb25maWdMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumFjee9ruWIl+ihqOWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bphY3nva7kv6Hmga/lpLHotKUiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOabtOaWsOeOsOaciemFjee9riAqLw0KICAgIHVwZGF0ZUV4aXN0aW5nQ29uZmlncyhleGlzdGluZ0NvbmZpZ3MsIGNvbmZpZ0tleXMsIGNvbmZpZ1ZhbHVlcykgew0KICAgICAgbGV0IHVwZGF0ZUNvdW50ID0gMDsNCiAgICAgIGNvbnN0IHRvdGFsQ291bnQgPSBjb25maWdLZXlzLmxlbmd0aDsNCg0KICAgICAgY29uZmlnS2V5cy5mb3JFYWNoKChrZXksIGluZGV4KSA9PiB7DQogICAgICAgIGNvbnN0IGV4aXN0aW5nQ29uZmlnID0gZXhpc3RpbmdDb25maWdzLmZpbmQoY29uZmlnID0+IGNvbmZpZy5jb25maWdLZXkgPT09IGtleSk7DQoNCiAgICAgICAgaWYgKGV4aXN0aW5nQ29uZmlnKSB7DQogICAgICAgICAgLy8g5pu05paw546w5pyJ6YWN572uDQogICAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgICAgIC4uLmV4aXN0aW5nQ29uZmlnLA0KICAgICAgICAgICAgY29uZmlnVmFsdWU6IGNvbmZpZ1ZhbHVlc1tpbmRleF0NCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgdXBkYXRlQ29uZmlnKHVwZGF0ZURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdXBkYXRlQ291bnQrKzsNCiAgICAgICAgICAgIGlmICh1cGRhdGVDb3VudCA9PT0gdG90YWxDb3VudCkgew0KICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6YWN572u5pu05paw5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuY29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIHRoaXMuY29uZmlnTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgY29uc29sZS5lcnJvcihg5pu05paw6YWN572uICR7a2V5fSDlpLHotKU6YCwgZXJyb3IpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOabtOaWsOmFjee9ruWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nfWApOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOmFjee9ruS4jeWtmOWcqO+8jOiusOW9lemUmeivrw0KICAgICAgICAgIGNvbnNvbGUud2Fybihg6YWN572uICR7a2V5fSDkuI3lrZjlnKhgKTsNCiAgICAgICAgICB1cGRhdGVDb3VudCsrOw0KICAgICAgICAgIGlmICh1cGRhdGVDb3VudCA9PT0gdG90YWxDb3VudCkgew0KICAgICAgICAgICAgdGhpcy5jb25maWdMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dXYXJuaW5nKCLpg6jliIbphY3nva7kuI3lrZjlnKjvvIzor7fogZTns7vnrqHnkIblkZgiKTsNCiAgICAgICAgICAgIHRoaXMuY29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICB9DQp9Ow0K"}, null]}