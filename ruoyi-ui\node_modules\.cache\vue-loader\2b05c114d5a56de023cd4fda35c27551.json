{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue?vue&type=template&id=6fb8a184&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue", "mtime": 1753931003624}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}