-- 页面内容管理菜单权限配置SQL
-- 已完成配置，无需重复执行

-- ============================================
-- 菜单权限配置总结
-- ============================================

-- 1. 现有菜单结构（已更新）
/*
内容管理 (menu_id: 2110)
├── 轮播图管理 (menu_id: 2111)
├── 通知管理 (menu_id: 2112)
├── 弹幕管理 (menu_id: 2113)
├── top图片管理 (menu_id: 2114)
├── 联系人管理 (menu_id: 21652)
└── 页面内容管理 (menu_id: 2132) ← 已更新
    ├── 页面内容查询 (menu_id: 21321)
    ├── 页面内容新增 (menu_id: 21322)
    ├── 页面内容修改 (menu_id: 21323)
    ├── 页面内容删除 (menu_id: 21324)
    └── 页面内容导出 (menu_id: 21325)
*/

-- 2. 已执行的更新操作
UPDATE sys_menu SET
  menu_name = '页面内容管理',
  parent_id = 2110,
  order_num = 6,
  perms = 'miniapp:content:page:list'
WHERE menu_id = 2132;

UPDATE sys_menu SET perms = 'miniapp:content:page:query' WHERE menu_id = 21321;
UPDATE sys_menu SET perms = 'miniapp:content:page:add' WHERE menu_id = 21322;
UPDATE sys_menu SET perms = 'miniapp:content:page:edit' WHERE menu_id = 21323;
UPDATE sys_menu SET perms = 'miniapp:content:page:remove' WHERE menu_id = 21324;
UPDATE sys_menu SET perms = 'miniapp:content:page:export' WHERE menu_id = 21325;

-- 3. 权限配置验证
-- 管理员角色(role_id: 1)已拥有所有相关权限

-- 4. 最终权限列表
/*
miniapp:content:page:list   - 页面内容管理列表
miniapp:content:page:query  - 页面内容查询
miniapp:content:page:add    - 页面内容新增
miniapp:content:page:edit   - 页面内容修改
miniapp:content:page:remove - 页面内容删除
miniapp:content:page:export - 页面内容导出
*/
