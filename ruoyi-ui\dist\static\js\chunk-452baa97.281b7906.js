(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-452baa97"],{"27bf":function(e,t,r){"use strict";r("6854")},6854:function(e,t,r){},c3fe:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-card",[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("span",[e._v("产业树管理")]),r("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:e.handleAddRoot}},[e._v("新增根节点")])],1),r("el-tree",{attrs:{data:e.industryTree,"node-key":"id",props:e.treeProps,"default-expand-all":"","highlight-current":""},on:{"node-click":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var n=t.data;return r("span",{staticClass:"custom-tree-node"},[r("div",{staticClass:"node-content"},[r("div",{staticClass:"node-info"},[r("span",{staticClass:"level-badge",class:"level-"+n.nodeLevel},[e._v("L"+e._s(n.nodeLevel))]),r("span",{staticClass:"node-name"},[e._v(e._s(n.nodeName))]),e.shouldShowStreamTypeInTree(n)?r("span",{staticClass:"stream-badge",class:"stream-"+n.streamType},[e._v(" "+e._s(e.getStreamTypeName(n.streamType))+" ")]):e._e(),r("span",{staticClass:"status-badge",class:"0"===n.status?"status-normal":"status-disabled"},[e._v(" "+e._s("0"===n.status?"正常":"停用")+" ")])]),r("div",{staticClass:"node-actions"},[n.nodeLevel<3?r("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){return t.stopPropagation(),e.handleAdd(n)}}},[e._v("新增")]):e._e(),r("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){return t.stopPropagation(),e.handleEdit(n)}}},[e._v("编辑")]),r("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){return t.stopPropagation(),e.handleDelete(n)}}},[e._v("删除")])],1)])])}}])})],1),r("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"600px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[r("el-form",{ref:"formRef",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"节点名称",prop:"nodeName"}},[r("el-input",{attrs:{placeholder:"请输入节点名称"},model:{value:e.form.nodeName,callback:function(t){e.$set(e.form,"nodeName",t)},expression:"form.nodeName"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1)],1),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"节点层级",prop:"nodeLevel"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:3,disabled:""},model:{value:e.form.nodeLevel,callback:function(t){e.$set(e.form,"nodeLevel",t)},expression:"form.nodeLevel"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{staticStyle:{width:"100%"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[r("el-option",{attrs:{label:"正常",value:"0"}}),r("el-option",{attrs:{label:"停用",value:"1"}})],1)],1)],1)],1),2===e.form.nodeLevel&&e.shouldShowStreamType?r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"产业链位置",prop:"streamType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择产业链位置"},model:{value:e.form.streamType,callback:function(t){e.$set(e.form,"streamType",t)},expression:"form.streamType"}},[r("el-option",{attrs:{label:"上游",value:"upstream"}}),r("el-option",{attrs:{label:"中游",value:"midstream"}}),r("el-option",{attrs:{label:"下游",value:"downstream"}})],1)],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"节点类型",prop:"nodeType"}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.nodeType,callback:function(t){e.$set(e.form,"nodeType",t)},expression:"form.nodeType"}})],1)],1)],1):e._e(),2!==e.form.nodeLevel||e.shouldShowStreamType?e._e():r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"节点类型",prop:"nodeType"}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.nodeType,callback:function(t){e.$set(e.form,"nodeType",t)},expression:"form.nodeType"}})],1)],1)],1),1===e.form.nodeLevel?r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"是否有上中下游",prop:"hasStreamType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择是否有上中下游"},model:{value:e.form.hasStreamType,callback:function(t){e.$set(e.form,"hasStreamType",t)},expression:"form.hasStreamType"}},[r("el-option",{attrs:{label:"否",value:"0"}}),r("el-option",{attrs:{label:"是",value:"1"}})],1)],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"节点类型",prop:"nodeType"}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.nodeType,callback:function(t){e.$set(e.form,"nodeType",t)},expression:"form.nodeType"}})],1)],1)],1):e._e(),3===e.form.nodeLevel?r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"节点类型",prop:"nodeType"}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.nodeType,callback:function(t){e.$set(e.form,"nodeType",t)},expression:"form.nodeType"}})],1)],1)],1):e._e(),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"节点描述",prop:"nodeDescription"}},[r("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入节点描述（选填）",maxlength:"500","show-word-limit":""},model:{value:e.form.nodeDescription,callback:function(t){e.$set(e.form,"nodeDescription",t)},expression:"form.nodeDescription"}})],1)],1)],1),r("el-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{title:e.getLevelDescription(e.form.nodeLevel),type:"info",closable:!1,"show-icon":""}})],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确定")])],1)],1)],1)},o=[],a=r("b85c"),s=(r("d9e2"),r("e292")),i={name:"IndustryTree",data:function(){var e=this;return{industryTree:[],treeProps:{children:"children",label:"nodeName"},dialogVisible:!1,dialogTitle:"",form:{id:null,parentId:0,nodeName:"",nodeDescription:"",sortOrder:0,status:"0",nodeLevel:1,streamType:"",hasStreamType:"0",nodeType:"type"},rules:{nodeName:[{required:!0,message:"请输入节点名称",trigger:"blur"}],sortOrder:[{required:!0,message:"请输入排序",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],streamType:[{validator:function(t,r,n){e.shouldShowStreamType&&!r?n(new Error("请选择产业链位置")):n()},trigger:"change"}]},currentNode:null,isEdit:!1,parentNodeInfo:null}},computed:{shouldShowStreamType:function(){return!(2!==this.form.nodeLevel||!this.parentNodeInfo)&&"1"===this.parentNodeInfo.hasStreamType}},created:function(){this.loadTree()},methods:{loadTree:function(){var e=this;Object(s["c"])().then((function(t){e.industryTree=t.data||[]}))},handleNodeClick:function(e){this.currentNode=e},handleAddRoot:function(){this.isEdit=!1,this.dialogTitle="新增根节点",this.parentNodeInfo=null,this.form={id:null,parentId:0,nodeName:"",nodeDescription:"",sortOrder:0,status:"0",nodeLevel:1,streamType:"",hasStreamType:"0",nodeType:"type"},this.dialogVisible=!0},handleAdd:function(e){var t=this;this.isEdit=!1,this.dialogTitle="新增子节点";var r=(e.nodeLevel||1)+1;r>3?this.$message.warning("最多支持三层级结构"):2===r?Object(s["e"])(e.id).then((function(n){t.parentNodeInfo=n.data,t.setupAddForm(e,r)})):(this.parentNodeInfo=e,this.setupAddForm(e,r))},setupAddForm:function(e,t){var r=this.getNodeTypeByLevel(t);this.form={id:null,parentId:e.id,nodeName:"",nodeDescription:"",sortOrder:0,status:"0",nodeLevel:t,streamType:"",hasStreamType:"0",nodeType:r},this.dialogVisible=!0},handleEdit:function(e){var t=this;Object(s["e"])(e.id).then((function(e){t.isEdit=!0,t.dialogTitle="编辑节点",2===e.data.nodeLevel&&e.data.parentId?t.parentNodeInfo=t.findNodeById(t.industryTree,e.data.parentId):t.parentNodeInfo=null;var r=e.data.nodeType;r||(1===e.data.nodeLevel?r="type":2===e.data.nodeLevel?r="position":3===e.data.nodeLevel&&(r="segment")),t.form=Object.assign({},e.data,{nodeType:r}),t.dialogVisible=!0}))},handleDelete:function(e){var t=this,r="确定要删除该节点吗？";e.nodeLevel<3&&(r="删除该节点将同时删除其所有子节点，确定要删除吗？"),this.$confirm(r,"删除确认",{type:"warning",confirmButtonText:"确定删除",cancelButtonText:"取消"}).then((function(){Object(s["h"])([e.id]).then((function(){t.$message.success("删除成功"),t.loadTree()})).catch((function(e){t.$message.error(e.msg||"删除失败")}))}))},submitForm:function(){var e=this;this.$refs.formRef.validate((function(t){if(t){if(e.isEdit){var r=e.findNodeById(e.industryTree,e.form.id),n=e.hasChildrenNodes(e.form.id);if(r&&"0"===r.status&&"1"===e.form.status&&n)return void e.$confirm("停用该节点将同时停用其所有子节点，确定要继续吗？","停用确认",{type:"warning",confirmButtonText:"确定停用",cancelButtonText:"取消"}).then((function(){e.doSubmit()}));if(r&&"1"===r.status&&"0"===e.form.status&&n)return void e.$confirm("启用该节点将同时启用其所有子节点，确定要继续吗？","启用确认",{type:"info",confirmButtonText:"确定启用",cancelButtonText:"取消"}).then((function(){e.doSubmit()}))}e.doSubmit()}}))},doSubmit:function(){var e=this;this.isEdit?Object(s["b"])(this.form).then((function(){e.$message.success("修改成功"),e.dialogVisible=!1,e.loadTree()})):Object(s["a"])(this.form).then((function(){e.$message.success("新增成功"),e.dialogVisible=!1,e.loadTree()}))},hasChildrenNodes:function(e){var t=this.findNodeById(this.industryTree,e);return t&&t.children&&t.children.length>0},getNodeTypeByLevel:function(e){switch(e){case 1:return"type";case 2:return"position";case 3:return"segment";default:return"type"}},getStreamTypeName:function(e){switch(e){case"upstream":return"上游";case"midstream":return"中游";case"downstream":return"下游";default:return e}},getLevelDescription:function(e){switch(e){case 1:return"第一层级：行业大类（如：新能源、硬科技等）";case 2:return this.shouldShowStreamType?"第二层级：产业位置，需要选择上中下游（如：太阳能发电-上游、储能系统-中游等）":"第二层级：产业细分（如：太阳能发电、储能系统等）";case 3:return"第三层级：具体细分领域（如：硅片制造、电池组件等）";default:return"请选择正确的层级"}},findNodeById:function(e,t){var r,n=Object(a["a"])(e);try{for(n.s();!(r=n.n()).done;){var o=r.value;if(o.id===t)return o;if(o.children&&o.children.length>0){var s=this.findNodeById(o.children,t);if(s)return s}}}catch(i){n.e(i)}finally{n.f()}return null},shouldShowStreamTypeInTree:function(e){if(2!==e.nodeLevel||!e.streamType)return!1;var t=this.findParentNodeInTree(e);return t&&"1"===t.hasStreamType},findParentNodeInTree:function(e){return this.findNodeById(this.industryTree,e.parentId)}}},l=i,d=(r("27bf"),r("2877")),u=Object(d["a"])(l,n,o,!1,null,"78be207e",null);t["default"]=u.exports},e292:function(e,t,r){"use strict";r.d(t,"f",(function(){return o})),r.d(t,"c",(function(){return a})),r.d(t,"a",(function(){return s})),r.d(t,"b",(function(){return i})),r.d(t,"h",(function(){return l})),r.d(t,"e",(function(){return d})),r.d(t,"g",(function(){return u})),r.d(t,"d",(function(){return c}));var n=r("b775");function o(){return Object(n["a"])({url:"/miniapp/industry/tree",method:"get"})}function a(){return Object(n["a"])({url:"/miniapp/industry/tree/all",method:"get"})}function s(e){return Object(n["a"])({url:"/miniapp/industry/add",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/miniapp/industry/edit",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/miniapp/industry/remove",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/miniapp/industry/info",method:"get",params:{id:e}})}function u(e){return Object(n["a"])({url:"/miniapp/industry/level/".concat(e),method:"get"})}function c(e){return Object(n["a"])({url:"/miniapp/industry/batchInfo",method:"post",data:e})}}}]);