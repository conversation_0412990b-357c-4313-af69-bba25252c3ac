package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniContact;
import org.apache.ibatis.annotations.Param;

/**
 * 联系人管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface MiniContactMapper 
{
    /**
     * 查询联系人管理
     * 
     * @param contactId 联系人管理主键
     * @return 联系人管理
     */
    public MiniContact selectMiniContactByContactId(Long contactId);

    /**
     * 根据联系人编码查询联系人管理
     * 
     * @param contactCode 联系人编码
     * @return 联系人管理
     */
    public MiniContact selectMiniContactByContactCode(String contactCode);

    /**
     * 查询联系人管理列表
     * 
     * @param miniContact 联系人管理
     * @return 联系人管理集合
     */
    public List<MiniContact> selectMiniContactList(MiniContact miniContact);

    /**
     * 查询启用的联系人管理列表（小程序端用）
     * 
     * @return 联系人管理集合
     */
    public List<MiniContact> selectEnabledMiniContactList();

    /**
     * 新增联系人管理
     * 
     * @param miniContact 联系人管理
     * @return 结果
     */
    public int insertMiniContact(MiniContact miniContact);

    /**
     * 修改联系人管理
     * 
     * @param miniContact 联系人管理
     * @return 结果
     */
    public int updateMiniContact(MiniContact miniContact);

    /**
     * 删除联系人管理
     * 
     * @param contactId 联系人管理主键
     * @return 结果
     */
    public int deleteMiniContactByContactId(Long contactId);

    /**
     * 批量删除联系人管理
     * 
     * @param contactIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniContactByContactIds(Long[] contactIds);

    /**
     * 更新联系人排序
     * 
     * @param contactId 联系人ID
     * @param sortOrder 排序值
     * @return 结果
     */
    public int updateMiniContactSort(@Param("contactId") Long contactId, @Param("sortOrder") Integer sortOrder);
}
