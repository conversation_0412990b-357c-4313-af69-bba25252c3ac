{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1753694616742}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UHJvamVjdCwgZGVsUHJvamVjdCwgYXVkaXRQcm9qZWN0LCB1cGRhdGVTcG9uc29ySW1hZ2UsIGdldFNwb25zb3JJbWFnZSB9IGZyb20gIkAvYXBpL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0IjsNCmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJQcm9qZWN0IiwNCiAgY29tcG9uZW50czogew0KICAgIEltYWdlVXBsb2FkDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6aG555uu5oql5ZCN6KGo5qC85pWw5o2uDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICAvLyDmmK/lkKbmmL7npLrotZ7liqnllYblm77niYflvLnlh7rlsYINCiAgICAgIHNwb25zb3JPcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuWuoeaguOW8ueWHuuWxgg0KICAgICAgYXVkaXRPcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuafpeeci+ivpuaDheW8ueWHuuWxgg0KICAgICAgdmlld09wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBjaXR5OiBudWxsLA0KICAgICAgICBpbmR1c3RyeTogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6LWe5Yqp5ZWG6KGo5Y2V5Y+C5pWwDQogICAgICBzcG9uc29yRm9ybTogew0KICAgICAgICBzcG9uc29yVW5pdDogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOWuoeaguOihqOWNleWPguaVsA0KICAgICAgYXVkaXRGb3JtOiB7fSwNCiAgICAgIC8vIOafpeeci+ivpuaDheihqOWNleWPguaVsA0KICAgICAgdmlld0Zvcm06IHt9LA0KICAgICAgLy8g5a6h5qC46KGo5Y2V5qCh6aqMDQogICAgICBhdWRpdFJ1bGVzOiB7DQogICAgICAgIHN0YXR1czogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrqHmoLjnu5PmnpzkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoumhueebruaKpeWQjeWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFByb2plY3QodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucHJvamVjdExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQoNCiAgICAvKiog6LWe5Yqp5ZWG5Zu+54mH5LiK5Lyg5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlU3BvbnNvclVwbG9hZCgpIHsNCiAgICAgIGdldFNwb25zb3JJbWFnZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnNwb25zb3JGb3JtLnNwb25zb3JVbml0ID0gcmVzcG9uc2UuZGF0YSB8fCAnJzsNCiAgICAgICAgdGhpcy5zcG9uc29yT3BlbiA9IHRydWU7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuc3BvbnNvckZvcm0uc3BvbnNvclVuaXQgPSAnJzsNCiAgICAgICAgdGhpcy5zcG9uc29yT3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlj5bmtojotZ7liqnllYblm77niYfkuIrkvKAgKi8NCiAgICBjYW5jZWxTcG9uc29yKCkgew0KICAgICAgdGhpcy5zcG9uc29yT3BlbiA9IGZhbHNlOw0KICAgICAgLy8g5LiN6KaB5riF56m65pWw5o2u77yM5L+d5oyB5Y6f5pyJ5pWw5o2uDQogICAgICAvLyB0aGlzLnNwb25zb3JGb3JtLnNwb25zb3JVbml0ID0gbnVsbDsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTotZ7liqnllYblm77niYcgKi8NCiAgICBzdWJtaXRTcG9uc29yRm9ybSgpIHsNCiAgICAgIHVwZGF0ZVNwb25zb3JJbWFnZSh0aGlzLnNwb25zb3JGb3JtLnNwb25zb3JVbml0KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6LWe5Yqp5ZWG5Zu+54mH5pu05paw5oiQ5YqfIik7DQogICAgICAgIHRoaXMuc3BvbnNvck9wZW4gPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuabtOaWsOi1nuWKqeWVhuWbvueJh+Wksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5p+l55yL5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVmlldyhyb3cpIHsNCiAgICAgIHRoaXMudmlld0Zvcm0gPSByb3c7DQogICAgICB0aGlzLnZpZXdPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDlrqHmoLjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBdWRpdChyb3cpIHsNCiAgICAgIHRoaXMuYXVkaXRGb3JtID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBzdGF0dXM6IDIsDQogICAgICAgIGF1ZGl0UmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5hdWRpdE9wZW4gPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvKiog5a6h5qC45o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0QXVkaXQoKSB7DQogICAgICB0aGlzLiRyZWZzWyJhdWRpdEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGF1ZGl0UHJvamVjdCh0aGlzLmF1ZGl0Rm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuYXVkaXRPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5a6h5qC4ICovDQogICAgY2FuY2VsQXVkaXQoKSB7DQogICAgICB0aGlzLmF1ZGl0T3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5hdWRpdEZvcm0gPSB7fTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpobnnm67miqXlkI3nvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFByb2plY3QoaWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC9oYWl0YW5nL3Byb2plY3QvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcHJvamVjdF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDojrflj5bnirbmgIHmlofmnKwgKi8NCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAxOiByZXR1cm4gJ+W+heWuoeaguCc7DQogICAgICAgIGNhc2UgMjogcmV0dXJuICflrqHmoLjpgJrov4cnOw0KICAgICAgICBjYXNlIDM6IHJldHVybiAn5a6h5qC45LiN6YCa6L+HJzsNCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICfmnKrnn6UnOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOiOt+WPlueKtuaAgeexu+WeiyAqLw0KICAgIGdldFN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBzd2l0Y2ggKHN0YXR1cykgew0KICAgICAgICBjYXNlIDE6IHJldHVybiAnd2FybmluZyc7DQogICAgICAgIGNhc2UgMjogcmV0dXJuICdzdWNjZXNzJzsNCiAgICAgICAgY2FzZSAzOiByZXR1cm4gJ2Rhbmdlcic7DQogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnaW5mbyc7DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, null]}