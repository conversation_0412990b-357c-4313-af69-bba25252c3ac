package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 园区管理对象 mini_park
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public class MiniPark extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 园区ID */
    private Long parkId;

    /** 园区名称 */
    @Excel(name = "园区名称")
    private String parkName;

    /** 园区编码 */
    @Excel(name = "园区编码")
    private String parkCode;

    /** 园区简介 */
    @Excel(name = "园区简介")
    private String description;

    /** 园区详细内容（富文本） */
    private String content;

    /** 园区封面图片 */
    @Excel(name = "园区封面图片")
    private String coverImage;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setParkId(Long parkId) 
    {
        this.parkId = parkId;
    }

    public Long getParkId() 
    {
        return parkId;
    }
    public void setParkName(String parkName) 
    {
        this.parkName = parkName;
    }

    public String getParkName() 
    {
        return parkName;
    }
    public void setParkCode(String parkCode) 
    {
        this.parkCode = parkCode;
    }

    public String getParkCode()
    {
        return parkCode;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setCoverImage(String coverImage)
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage()
    {
        return coverImage;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("parkId", getParkId())
            .append("parkName", getParkName())
            .append("parkCode", getParkCode())
            .append("description", getDescription())
            .append("content", getContent())
            .append("coverImage", getCoverImage())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
