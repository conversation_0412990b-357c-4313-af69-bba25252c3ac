(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d6546"],{"71a7":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[a("el-form-item",{attrs:{label:"活动名称",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入活动名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handle<PERSON><PERSON>y(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),a("el-form-item",{attrs:{label:"公众号来源",prop:"wechatSource"}},[a("el-input",{attrs:{placeholder:"请输入公众号来源",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.wechatSource,callback:function(t){e.$set(e.queryParams,"wechatSource",t)},expression:"queryParams.wechatSource"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"success",plain:"",icon:"el-icon-setting",size:"mini"},on:{click:e.handleWechatConfig}},[e._v("公众号配置")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.handleSyncFromWechat}},[e._v("同步活动")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.activityList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"活动ID",align:"center",prop:"activityId",width:"80"}}),a("el-table-column",{attrs:{label:"活动标题",align:"center",prop:"title"}}),a("el-table-column",{attrs:{label:"活动描述",align:"center",prop:"description","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"封面图片",align:"center",prop:"coverImage",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.coverImage?a("div",{staticStyle:{display:"flex","justify-content":"center"}},[a("img",{staticStyle:{width:"100px",height:"60px","object-fit":"cover","border-radius":"4px",cursor:"pointer",transition:"all 0.3s"},attrs:{src:e.getImageUrl(t.row.coverImage),alt:"封面图片",referrerpolicy:"no-referrer"},on:{click:function(a){return e.previewImage(t.row.coverImage)},error:function(t){return e.handleImageError(t)},load:function(t){return e.handleImageLoad(t)}}})]):a("span",{staticStyle:{color:"#999"}},[e._v("无图片")])]}}])}),a("el-table-column",{attrs:{label:"公众号来源",align:"center",prop:"wechatSource",width:"120"}}),a("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"80"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"活动标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入活动标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),a("el-form-item",{attrs:{label:"活动描述",prop:"description"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入活动描述",rows:4},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),a("el-form-item",{attrs:{label:"封面图片",prop:"coverImage"}},[a("image-upload",{model:{value:e.form.coverImage,callback:function(t){e.$set(e.form,"coverImage",t)},expression:"form.coverImage"}})],1),a("el-form-item",{attrs:{label:"文章链接",prop:"articleUrl"}},[a("el-input",{attrs:{placeholder:"请输入公众号文章链接"},model:{value:e.form.articleUrl,callback:function(t){e.$set(e.form,"articleUrl",t)},expression:"form.articleUrl"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[a("el-input-number",{attrs:{min:0,max:9999},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:"0"}},[e._v("启用")]),a("el-radio",{attrs:{label:"1"}},[e._v("停用")])],1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"微信公众号配置",visible:e.wechatConfigOpen,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.wechatConfigOpen=t}}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAddWechatConfig}},[e._v("新增配置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.wechatConfigLoading,expression:"wechatConfigLoading"}],attrs:{data:e.wechatConfigList}},[a("el-table-column",{attrs:{label:"配置名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"AppID",align:"center",prop:"appId"}}),a("el-table-column",{attrs:{label:"AppSecret",align:"center",prop:"appSecret","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.appSecret?t.row.appSecret.substring(0,10)+"...":"")+" ")]}}])}),a("el-table-column",{attrs:{label:"是否启用",align:"center",prop:"enabled",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:t.row.enabled?"success":"danger"}},[e._v(" "+e._s(t.row.enabled?"启用":"禁用")+" ")])]}}])}),a("el-table-column",{attrs:{label:"最后同步时间",align:"center",prop:"lastSyncTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.lastSyncTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleEditWechatConfig(t.row)}}},[e._v("修改")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-connection"},on:{click:function(a){return e.handleTestWechatConfig(t.row)}}},[e._v("测试")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-refresh"},on:{click:function(a){return e.handleSyncFromSingleWechat(t.row.configId)}}},[e._v("同步")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDeleteWechatConfig(t.row)}}},[e._v("删除")])]}}])})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.wechatConfigOpen=!1}}},[e._v("关 闭")])],1)],1),a("el-dialog",{attrs:{title:e.wechatConfigTitle,visible:e.wechatConfigFormOpen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.wechatConfigFormOpen=t}}},[a("el-form",{ref:"wechatConfigForm",attrs:{model:e.wechatConfigForm,rules:e.wechatConfigRules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"配置名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入配置名称"},model:{value:e.wechatConfigForm.name,callback:function(t){e.$set(e.wechatConfigForm,"name",t)},expression:"wechatConfigForm.name"}})],1),a("el-form-item",{attrs:{label:"AppID",prop:"appId"}},[a("el-input",{attrs:{placeholder:"请输入微信公众号AppID"},model:{value:e.wechatConfigForm.appId,callback:function(t){e.$set(e.wechatConfigForm,"appId",t)},expression:"wechatConfigForm.appId"}})],1),a("el-form-item",{attrs:{label:"AppSecret",prop:"appSecret"}},[a("el-input",{attrs:{placeholder:"请输入微信公众号AppSecret",type:"password"},model:{value:e.wechatConfigForm.appSecret,callback:function(t){e.$set(e.wechatConfigForm,"appSecret",t)},expression:"wechatConfigForm.appSecret"}})],1),a("el-form-item",{attrs:{label:"是否启用",prop:"enabled"}},[a("el-radio-group",{model:{value:e.wechatConfigForm.enabled,callback:function(t){e.$set(e.wechatConfigForm,"enabled",t)},expression:"wechatConfigForm.enabled"}},[a("el-radio",{attrs:{label:!0}},[e._v("启用")]),a("el-radio",{attrs:{label:!1}},[e._v("禁用")])],1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.wechatConfigForm.remark,callback:function(t){e.$set(e.wechatConfigForm,"remark",t)},expression:"wechatConfigForm.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitWechatConfigForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelWechatConfig}},[e._v("取 消")])],1)],1)],1)},r=[],n=(a("d81d"),a("b0c0"),a("d3b7"),a("0643"),a("a573"),a("b775"));function i(e){return Object(n["a"])({url:"/miniapp/activity/list",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/miniapp/activity/getInfo",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/miniapp/activity/add",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/miniapp/activity/edit",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/miniapp/activity/remove",method:"post",data:e})}function m(){return Object(n["a"])({url:"/miniapp/activity/wechat/getConfigs",method:"post"})}function d(e){return Object(n["a"])({url:"/miniapp/activity/wechat/saveConfig",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/miniapp/activity/wechat/deleteConfig",method:"post",params:{configId:e}})}function f(e){return Object(n["a"])({url:"/miniapp/activity/wechat/testConfig",method:"post",data:e})}function g(){return Object(n["a"])({url:"/miniapp/activity/wechat/syncActivities",method:"post"})}function h(e){return Object(n["a"])({url:"/miniapp/activity/wechat/syncFromSingle",method:"post",params:{configId:e}})}var b={name:"MiniActivity",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,activityList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,status:null,wechatSource:null},form:{},rules:{title:[{required:!0,message:"活动标题不能为空",trigger:"blur"}],coverImage:[{required:!0,message:"封面图片不能为空",trigger:"blur"}],articleUrl:[{required:!0,message:"文章链接不能为空",trigger:"blur"}]},wechatConfigOpen:!1,wechatConfigFormOpen:!1,wechatConfigLoading:!1,wechatConfigList:[],wechatConfigTitle:"",wechatConfigForm:{},wechatConfigRules:{name:[{required:!0,message:"配置名称不能为空",trigger:"blur"}],appId:[{required:!0,message:"AppID不能为空",trigger:"blur"}],appSecret:[{required:!0,message:"AppSecret不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.activityList=t.rows,e.total=t.total,e.loading=!1})).catch((function(t){console.error("获取活动列表失败:",t),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={activityId:null,title:null,description:null,coverImage:null,articleUrl:null,sortOrder:0,status:"0",remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.activityId})),this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加活动"},handleUpdate:function(e){var t=this;this.reset();var a=e.activityId||this.ids;l(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改活动"})).catch((function(e){console.error("获取活动详情失败:",e),t.$modal.msgError("获取活动详情失败")}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){t&&(null!=e.form.activityId?s(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})).catch((function(t){console.error("修改活动失败:",t),e.$modal.msgError("修改活动失败")})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})).catch((function(t){console.error("新增活动失败:",t),e.$modal.msgError("新增活动失败")})))}))},handleDelete:function(e){var t=this,a=e.activityId?[e.activityId]:this.ids;this.$modal.confirm('是否确认删除活动编号为"'+a+'"的数据项？').then((function(){return u(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleWechatConfig:function(){this.wechatConfigOpen=!0,this.getWechatConfigList()},getWechatConfigList:function(){var e=this;this.wechatConfigLoading=!0,m().then((function(t){e.wechatConfigList=t.data,e.wechatConfigLoading=!1})).catch((function(t){console.error("获取微信配置失败:",t),e.wechatConfigLoading=!1}))},handleAddWechatConfig:function(){this.resetWechatConfigForm(),this.wechatConfigFormOpen=!0,this.wechatConfigTitle="新增微信公众号配置"},handleEditWechatConfig:function(e){this.resetWechatConfigForm(),this.wechatConfigForm=Object.assign({},e),this.wechatConfigFormOpen=!0,this.wechatConfigTitle="修改微信公众号配置"},resetWechatConfigForm:function(){this.wechatConfigForm={configId:null,name:null,appId:null,appSecret:null,enabled:!0,remark:null},this.$refs.wechatConfigForm&&this.$refs.wechatConfigForm.resetFields()},cancelWechatConfig:function(){this.wechatConfigFormOpen=!1,this.resetWechatConfigForm()},submitWechatConfigForm:function(){var e=this;this.$refs.wechatConfigForm.validate((function(t){t&&d(e.wechatConfigForm).then((function(t){e.$modal.msgSuccess("保存成功"),e.wechatConfigFormOpen=!1,e.getWechatConfigList()})).catch((function(t){console.error("保存微信配置失败:",t),e.$modal.msgError("保存失败")}))}))},handleTestWechatConfig:function(e){var t=this;this.$modal.loading("正在测试连接..."),f(e).then((function(e){t.$modal.closeLoading(),t.$modal.msgSuccess("连接测试成功")})).catch((function(e){t.$modal.closeLoading(),console.error("测试连接失败:",e),t.$modal.msgError("连接测试失败")}))},handleDeleteWechatConfig:function(e){var t=this;this.$modal.confirm('是否确认删除配置"'+e.name+'"？').then((function(){return p(e.configId)})).then((function(){t.getWechatConfigList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleSyncFromWechat:function(){var e=this;this.$modal.loading("正在同步活动..."),g().then((function(t){e.$modal.closeLoading(),e.$modal.msgSuccess(t.msg),e.getList()})).catch((function(t){e.$modal.closeLoading(),console.error("同步活动失败:",t),e.$modal.msgError("同步活动失败")}))},handleSyncFromSingleWechat:function(e){var t=this;this.$modal.loading("正在同步活动..."),h(e).then((function(e){t.$modal.closeLoading(),t.$modal.msgSuccess("同步成功"),t.getList(),t.getWechatConfigList()})).catch((function(e){t.$modal.closeLoading(),console.error("同步活动失败:",e),t.$modal.msgError("同步活动失败")}))},previewImage:function(e){var t=this;if(e){var a=new Image;a.referrerPolicy="no-referrer",a.onload=function(){var t=window.open("","_blank");t.document.write('\n            <html>\n              <head><title>图片预览</title></head>\n              <body style="margin:0;padding:20px;text-align:center;background:#f5f5f5;">\n                <img src="'.concat(e,'" referrerpolicy="no-referrer" style="max-width:100%;max-height:100%;object-fit:contain;" alt="图片预览" />\n              </body>\n            </html>\n          '))},a.onerror=function(){t.$modal.msgError("图片加载失败")},a.src=e}},handleImageError:function(e){var t=e.target.getAttribute("data-original-src")||e.target.src;if(console.error("图片加载失败:",t),e.target.hasAttribute("data-proxy-tried"))e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiB2aWV3Qm94PSIwIDAgMTAwIDYwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAyNUw0NSAzNUw1NSAyNUw2NSAzNVY0NUgzNVYyNVoiIGZpbGw9IiNEOUQ5RDkiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDEuNjY2NjdDNS4zOTk5MSAxLjY2NjY3IDEuNjY2NTggNS4zOTk5OSAxLjY2NjU4IDEwQzEuNjY2NTggMTQuNiA1LjM5OTkxIDE4LjMzMzMgMTAgMTguMzMzM0MxNC42IDE4LjMzMzMgMTguMzMzMyAxNC42IDE4LjMzMzMgMTBDMTguMzMzMyA1LjM5OTk5IDE0LjYgMS42NjY2NyAxMCAxLjY2NjY3Wk0xMCA1QzExLjM4MDcgNSAxMi41IDYuMTE5MjkgMTIuNSA3LjVDMTIuNSA4Ljg4MDcxIDExLjM4MDcgMTAgMTAgMTBDOC42MTkyOSAxMCA3LjUgOC44ODA3MSA3LjUgNy41QzcuNSA2LjExOTI5IDguNjE5MjkgNSAxMCA1Wk0xMCAxNS44MzMzQzguMzMzMjUgMTUuODMzMyA2Ljg3NDkxIDE0Ljk5MTcgNi4wNDE1OCAxMy43NUM2LjA0MTU4IDEyLjUgOC4zMzMyNSAxMS42NjY3IDEwIDExLjY2NjdDMTEuNjY2NyAxMS42NjY3IDEzLjk1ODMgMTIuNSAxMy45NTgzIDEzLjc1QzEzLjEyNSAxNC45OTE3IDExLjY2NjcgMTUuODMzMyAxMCAxNS44MzMzWiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4KPC9zdmc+",e.target.style.backgroundColor="#f5f5f5",e.target.style.border="1px dashed #ddd",e.target.title="图片加载失败";else{e.target.setAttribute("data-proxy-tried","true"),e.target.setAttribute("data-original-src",t);var a="/miniapp/activity/imageProxy?url=".concat(encodeURIComponent(t));e.target.src=a}},getImageUrl:function(e){return e||""},handleImageLoad:function(e){e.target.style.backgroundColor="",e.target.style.border="",e.target.title="点击预览"}}},w=b,y=a("2877"),v=Object(y["a"])(w,o,r,!1,null,null,null);t["default"]=v.exports}}]);