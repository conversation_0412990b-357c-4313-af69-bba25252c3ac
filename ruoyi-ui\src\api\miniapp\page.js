import request from '@/utils/request'

// 查询页面内容管理列表
export function listPageContent(query) {
  return request({
    url: '/miniapp/content/page/list',
    method: 'get',
    params: query
  })
}

// 查询页面内容管理详细
export function getPageContent(contentId) {
  return request({
    url: '/miniapp/content/page/' + contentId,
    method: 'get'
  })
}

// 新增页面内容管理
export function addPageContent(data) {
  return request({
    url: '/miniapp/content/page',
    method: 'post',
    data: data
  })
}

// 修改页面内容管理
export function updatePageContent(data) {
  return request({
    url: '/miniapp/content/page',
    method: 'put',
    data: data
  })
}

// 删除页面内容管理
export function delPageContent(contentId) {
  return request({
    url: '/miniapp/content/page/' + contentId,
    method: 'delete'
  })
}

// 根据页面编码获取页面内容（小程序端）
export function getPageContentByCode(pageCode) {
  return request({
    url: '/miniapp/content/page/app/getByCode/' + pageCode,
    method: 'get'
  })
}

// 获取加入我们页面内容（小程序端）
export function getJoinUsContent() {
  return request({
    url: '/miniapp/content/page/app/getJoinUs',
    method: 'get'
  })
}

// 获取关于我们页面内容（小程序端）
export function getAboutUsContent() {
  return request({
    url: '/miniapp/content/page/app/getAboutUs',
    method: 'get'
  })
}

// 获取用户协议内容（小程序端）
export function getUserAgreementContent() {
  return request({
    url: '/miniapp/content/page/app/getUserAgreement',
    method: 'get'
  })
}

// 获取隐私协议内容（小程序端）
export function getPrivacyPolicyContent() {
  return request({
    url: '/miniapp/content/page/app/getPrivacyPolicy',
    method: 'get'
  })
}
