{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753946374542}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlLCBnZXRSZWdpc3RyYXRpb25NYW5hZ2UsIGRlbFJlZ2lzdHJhdGlvbk1hbmFnZSwgZXhwb3J0UmVnaXN0cmF0aW9uTWFuYWdlLCBhdWRpdFJlZ2lzdHJhdGlvbk1hbmFnZSB9IGZyb20gIkAvYXBpL21pbmlhcHAveGlxaW5nL3JlZ2lzdHJhdGlvbi1tYW5hZ2UiOw0KaW1wb3J0IHsgZ2V0QWN0aXZpdHlDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS1jb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJYaXFpbmdSZWdpc3RyYXRpb25NYW5hZ2UiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6KW/6Z2S6YeR56eN5a2Q6Lev5ryU5oql5ZCN566h55CG6KGo5qC85pWw5o2uDQogICAgICByZWdpc3RyYXRpb25NYW5hZ2VMaXN0OiBbXSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuafpeeci+W8ueWHuuWxgg0KICAgICAgdmlld09wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S65a6h5qC45by55Ye65bGCDQogICAgICBhdWRpdE9wZW46IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5pWw5o2u5YiX6KGoDQogICAgICBmb3JtRGF0YUxpc3Q6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGFjdGl2aXR5SWQ6IG51bGwsDQogICAgICAgIHN0YXR1czogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDlrqHmoLjooajljZXlj4LmlbANCiAgICAgIGF1ZGl0Rm9ybToge30NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouilv+mdkumHkeenjeWtkOi3r+a8lOaKpeWQjeeuoeeQhuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFJlZ2lzdHJhdGlvbk1hbmFnZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5yZWdpc3RyYXRpb25NYW5hZ2VMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnJlZ2lzdHJhdGlvbklkKQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5p+l55yL5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVmlldyhyb3cpIHsNCiAgICAgIGNvbnN0IHJlZ2lzdHJhdGlvbklkID0gcm93LnJlZ2lzdHJhdGlvbklkOw0KICAgICAgZ2V0UmVnaXN0cmF0aW9uTWFuYWdlKHJlZ2lzdHJhdGlvbklkKS50aGVuKGFzeW5jIHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgYXdhaXQgdGhpcy5wYXJzZUZvcm1EYXRhKCk7DQogICAgICAgIHRoaXMudmlld09wZW4gPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6Kej5p6Q6KGo5Y2V5pWw5o2uICovDQogICAgYXN5bmMgcGFyc2VGb3JtRGF0YSgpIHsNCiAgICAgIHRoaXMuZm9ybURhdGFMaXN0ID0gW107DQogICAgICBpZiAodGhpcy5mb3JtLmZvcm1EYXRhKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgZGF0YSA9IEpTT04ucGFyc2UodGhpcy5mb3JtLmZvcm1EYXRhKTsNCg0KICAgICAgICAgIC8vIOajgOafpeaVsOaNruagvOW8jw0KICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpKSB7DQogICAgICAgICAgICAvLyDmlrDmoLzlvI/vvJrmlbDnu4TmoLzlvI/vvIzmr4/kuKrlhYPntKDljIXlkKtuYW1l44CBdHlwZeOAgWxhYmVs44CBdmFsdWXnrYnlsZ7mgKcNCiAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgIGlmIChmaWVsZC5uYW1lICYmIGZpZWxkLnZhbHVlICE9PSB1bmRlZmluZWQgJiYgZmllbGQudmFsdWUgIT09IG51bGwgJiYgZmllbGQudmFsdWUgIT09ICcnKSB7DQogICAgICAgICAgICAgICAgY29uc3QgZm9ybURhdGFJdGVtID0gew0KICAgICAgICAgICAgICAgICAga2V5OiBmaWVsZC5sYWJlbCB8fCBmaWVsZC5uYW1lLCAvLyDkvJjlhYjkvb/nlKhsYWJlbO+8jOayoeacieWImeS9v+eUqG5hbWUNCiAgICAgICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmZvcm1hdEZpZWxkVmFsdWUoZmllbGQudmFsdWUsIGZpZWxkLnR5cGUpLA0KICAgICAgICAgICAgICAgICAgdHlwZTogZmllbGQudHlwZQ0KICAgICAgICAgICAgICAgIH07DQoNCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/mlofku7bnsbvlnovvvIzop6PmnpDmlofku7bliJfooagNCiAgICAgICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2ZpbGUnICYmIGZpZWxkLnZhbHVlKSB7DQogICAgICAgICAgICAgICAgICBmb3JtRGF0YUl0ZW0uZmlsZUxpc3QgPSB0aGlzLnBhcnNlRmlsZUxpc3QoZmllbGQudmFsdWUpOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybURhdGFMaXN0LnB1c2goZm9ybURhdGFJdGVtKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIGlmICh0eXBlb2YgZGF0YSA9PT0gJ29iamVjdCcpIHsNCiAgICAgICAgICAgIC8vIOaXp+agvOW8j++8muWvueixoeagvOW8j++8jGtleS12YWx1ZeW9ouW8jw0KICAgICAgICAgICAgLy8g6I635Y+W5rS75Yqo55qE6KGo5Y2V6YWN572u5p2l5pi+56S65q2j56Gu55qE5a2X5q615qCH562+DQogICAgICAgICAgICBjb25zdCBmb3JtQ29uZmlnID0gYXdhaXQgdGhpcy5nZXRBY3Rpdml0eUZvcm1Db25maWcoKTsNCiAgICAgICAgICAgIGNvbnN0IGZpZWxkTGFiZWxNYXAgPSB7fTsNCiAgICAgICAgICAgIGlmIChmb3JtQ29uZmlnKSB7DQogICAgICAgICAgICAgIGZvcm1Db25maWcuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgZmllbGRMYWJlbE1hcFtmaWVsZC5uYW1lXSA9IGZpZWxkLmxhYmVsOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkgew0KICAgICAgICAgICAgICBpZiAoZGF0YVtrZXldICE9PSB1bmRlZmluZWQgJiYgZGF0YVtrZXldICE9PSBudWxsICYmIGRhdGFba2V5XSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmZvcm1EYXRhTGlzdC5wdXNoKHsNCiAgICAgICAgICAgICAgICAgIGtleTogZmllbGRMYWJlbE1hcFtrZXldIHx8IGtleSwgLy8g5LyY5YWI5L2/55So5Lit5paH5qCH562+77yM5rKh5pyJ5YiZ5L2/55So5Y6f5a2X5q615ZCNDQogICAgICAgICAgICAgICAgICB2YWx1ZTogZGF0YVtrZXldLA0KICAgICAgICAgICAgICAgICAgdHlwZTogJ3RleHQnDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDooajljZXmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmoLzlvI/ljJblrZfmrrXlgLwgKi8NCiAgICBmb3JtYXRGaWVsZFZhbHVlKHZhbHVlLCB0eXBlKSB7DQogICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICfmnKrloavlhpknOw0KICAgICAgfQ0KDQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAnY2hlY2tib3gnOg0KICAgICAgICAgIC8vIOWkjemAieahhuexu+Wei++8jHZhbHVl5Y+v6IO95piv5pWw57uEDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7DQogICAgICAgICAgICByZXR1cm4gdmFsdWUubGVuZ3RoID4gMCA/IHZhbHVlLmpvaW4oJywgJykgOiAn5pyq6YCJ5oupJzsNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICBjYXNlICdyYWRpbyc6DQogICAgICAgIGNhc2UgJ3BpY2tlcic6DQogICAgICAgIGNhc2UgJ3NlbGVjdCc6DQogICAgICAgICAgLy8g5Y2V6YCJ57G75Z6LDQogICAgICAgICAgcmV0dXJuIHZhbHVlIHx8ICfmnKrpgInmi6knOw0KICAgICAgICBjYXNlICd0ZXh0YXJlYSc6DQogICAgICAgICAgLy8g5paH5pys5Z+f57G75Z6L77yM5L+d5oyB5o2i6KGMDQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICBjYXNlICdkYXRlJzoNCiAgICAgICAgICAvLyDml6XmnJ/nsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWUgfHwgJ+acqumAieaLqSc7DQogICAgICAgIGNhc2UgJ3RlbCc6DQogICAgICAgIGNhc2UgJ3Bob25lJzoNCiAgICAgICAgICAvLyDnlLXor53nsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgIGNhc2UgJ2ZpbGUnOg0KICAgICAgICAgIC8vIOaWh+S7tuexu+Wei++8jOi/lOWbnuWOn+Wni+WAvO+8jOWcqOaooeadv+S4reeJueauiuWkhOeQhg0KICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICAvLyDpu5jorqTmlofmnKznsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog6Kej5p6Q5paH5Lu25YiX6KGoICovDQogICAgcGFyc2VGaWxlTGlzdChmaWxlVmFsdWUpIHsNCiAgICAgIGlmICghZmlsZVZhbHVlKSByZXR1cm4gW107DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWmguaenOaYr+Wtl+espuS4su+8jOWwneivleino+aekOS4ukpTT04NCiAgICAgICAgaWYgKHR5cGVvZiBmaWxlVmFsdWUgPT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgLy8g5Y+v6IO95pivSlNPTuWtl+espuS4sg0KICAgICAgICAgIGlmIChmaWxlVmFsdWUuc3RhcnRzV2l0aCgnWycpIHx8IGZpbGVWYWx1ZS5zdGFydHNXaXRoKCd7JykpIHsNCiAgICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UoZmlsZVZhbHVlKTsNCiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHBhcnNlZCkpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHBhcnNlZC5tYXAoZmlsZSA9PiAoew0KICAgICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSB8fCBmaWxlLmZpbGVOYW1lIHx8ICfmnKrnn6Xmlofku7YnLA0KICAgICAgICAgICAgICAgIHVybDogZmlsZS51cmwgfHwgZmlsZS5wYXRoIHx8IGZpbGUNCiAgICAgICAgICAgICAgfSkpOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJzZWQudXJsIHx8IHBhcnNlZC5wYXRoKSB7DQogICAgICAgICAgICAgIHJldHVybiBbew0KICAgICAgICAgICAgICAgIG5hbWU6IHBhcnNlZC5uYW1lIHx8IHBhcnNlZC5maWxlTmFtZSB8fCAn5pyq55+l5paH5Lu2JywNCiAgICAgICAgICAgICAgICB1cmw6IHBhcnNlZC51cmwgfHwgcGFyc2VkLnBhdGgNCiAgICAgICAgICAgICAgfV07DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWPr+iDveaYr+WNleS4quaWh+S7tlVSTA0KICAgICAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgICAgIG5hbWU6IHRoaXMuZ2V0RmlsZU5hbWVGcm9tVXJsKGZpbGVWYWx1ZSksDQogICAgICAgICAgICAgIHVybDogZmlsZVZhbHVlDQogICAgICAgICAgICB9XTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g5aaC5p6c5piv5pWw57uEDQogICAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoZmlsZVZhbHVlKSkgew0KICAgICAgICAgIHJldHVybiBmaWxlVmFsdWUubWFwKGZpbGUgPT4gew0KICAgICAgICAgICAgaWYgKHR5cGVvZiBmaWxlID09PSAnc3RyaW5nJykgew0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuZ2V0RmlsZU5hbWVGcm9tVXJsKGZpbGUpLA0KICAgICAgICAgICAgICAgIHVybDogZmlsZQ0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWUgfHwgZmlsZS5maWxlTmFtZSB8fCAn5pyq55+l5paH5Lu2JywNCiAgICAgICAgICAgICAgICB1cmw6IGZpbGUudXJsIHx8IGZpbGUucGF0aCB8fCBmaWxlDQogICAgICAgICAgICAgIH07DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgICAgLy8g5aaC5p6c5piv5a+56LGhDQogICAgICAgIGVsc2UgaWYgKHR5cGVvZiBmaWxlVmFsdWUgPT09ICdvYmplY3QnKSB7DQogICAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgICBuYW1lOiBmaWxlVmFsdWUubmFtZSB8fCBmaWxlVmFsdWUuZmlsZU5hbWUgfHwgJ+acquefpeaWh+S7ticsDQogICAgICAgICAgICB1cmw6IGZpbGVWYWx1ZS51cmwgfHwgZmlsZVZhbHVlLnBhdGggfHwgZmlsZVZhbHVlDQogICAgICAgICAgfV07DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5paH5Lu25YiX6KGo5aSx6LSlOicsIGUpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gW107DQogICAgfSwNCiAgICAvKiog5LuOVVJM5Lit5o+Q5Y+W5paH5Lu25ZCNICovDQogICAgZ2V0RmlsZU5hbWVGcm9tVXJsKHVybCkgew0KICAgICAgaWYgKCF1cmwpIHJldHVybiAn5pyq55+l5paH5Lu2JzsNCiAgICAgIGNvbnN0IHBhcnRzID0gdXJsLnNwbGl0KCcvJyk7DQogICAgICBjb25zdCBmaWxlTmFtZSA9IHBhcnRzW3BhcnRzLmxlbmd0aCAtIDFdOw0KICAgICAgcmV0dXJuIGZpbGVOYW1lIHx8ICfmnKrnn6Xmlofku7YnOw0KICAgIH0sDQogICAgLyoqIOiOt+WPlua0u+WKqOihqOWNlemFjee9riAqLw0KICAgIGFzeW5jIGdldEFjdGl2aXR5Rm9ybUNvbmZpZygpIHsNCiAgICAgIGlmICghdGhpcy5mb3JtLmFjdGl2aXR5SWQpIHsNCiAgICAgICAgcmV0dXJuIG51bGw7DQogICAgICB9DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEFjdGl2aXR5Q29uZmlnKHRoaXMuZm9ybS5hY3Rpdml0eUlkKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKSB7DQogICAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UocmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmtLvliqjooajljZXphY3nva7lpLHotKU6JywgZSk7DQogICAgICB9DQogICAgICByZXR1cm4gbnVsbDsNCiAgICB9LA0KICAgIC8qKiDlrqHmoLjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBdWRpdChyb3cpIHsNCiAgICAgIHRoaXMuYXVkaXRGb3JtID0gew0KICAgICAgICByZWdpc3RyYXRpb25JZDogcm93LnJlZ2lzdHJhdGlvbklkLA0KICAgICAgICBzdGF0dXM6ICcxJywNCiAgICAgICAgYXVkaXRSZW1hcms6ICcnDQogICAgICB9Ow0KICAgICAgdGhpcy5hdWRpdE9wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOWuoeaguCAqLw0KICAgIHN1Ym1pdEF1ZGl0KCkgew0KICAgICAgYXVkaXRSZWdpc3RyYXRpb25NYW5hZ2UodGhpcy5hdWRpdEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiJDlip8iKTsNCiAgICAgICAgdGhpcy5hdWRpdE9wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCByZWdpc3RyYXRpb25JZHMgPSByb3cucmVnaXN0cmF0aW9uSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmiqXlkI3nvJblj7fkuLoiJyArIHJlZ2lzdHJhdGlvbklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFJlZ2lzdHJhdGlvbk1hbmFnZShyZWdpc3RyYXRpb25JZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL3hpcWluZy9yZWdpc3RyYXRpb24tbWFuYWdlL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHJlZ2lzdHJhdGlvbl9tYW5hZ2VfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfQ0KICB9DQp9Ow0K"}, null]}