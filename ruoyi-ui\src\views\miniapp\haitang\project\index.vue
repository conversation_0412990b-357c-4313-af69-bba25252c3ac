<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入城市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行业" prop="industry">
        <el-input
          v-model="queryParams.industry"
          placeholder="请输入行业"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待审核" value="1" />
          <el-option label="审核通过" value="2" />
          <el-option label="审核不通过" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-picture"
          size="mini"
          @click="handleSponsorUpload"
          v-hasPermi="['miniapp:haitang:project:edit']"
        >赞助商图片</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:haitang:project:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:haitang:project:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true"/>
      <el-table-column label="团队规模" align="center" prop="teamSize" />
      <el-table-column label="城市" align="center" prop="city" />
      <el-table-column label="赛区" align="center" prop="competitionArea" />
      <el-table-column label="行业" align="center" prop="industry" />
      <el-table-column label="天大校友" align="center" prop="isTjuAlumni">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isTjuAlumni ? 'success' : 'info'">
            {{ scope.row.isTjuAlumni ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="有公司" align="center" prop="hasCompany">
        <template slot-scope="scope">
          <el-tag :type="scope.row.hasCompany ? 'success' : 'info'">
            {{ scope.row.hasCompany ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center" prop="companyName" :show-overflow-tooltip="true"/>
      <el-table-column label="联系人" align="center" prop="contactName" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:haitang:project:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['miniapp:haitang:project:audit']"
            v-if="scope.row.status === 1"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:haitang:project:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />



    <!-- 审核对话框 -->
    <el-dialog title="审核项目报名" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="2">审核通过</el-radio>
            <el-radio :label="3">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" :rows="4" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 赞助商图片上传对话框 -->
    <el-dialog title="赞助商图片管理" :visible.sync="sponsorOpen" width="700px" append-to-body>
      <el-form ref="sponsorForm" :model="sponsorForm" label-width="120px">
        <el-form-item label="当前赞助商图片">
          <div v-if="sponsorForm.sponsorUnit && sponsorForm.sponsorUnit.trim() !== ''" style="padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;">
            <i class="el-icon-success" style="color: #67c23a; margin-right: 5px;"></i>
            <span style="color: #409eff;">已设置赞助商图片</span>
          </div>
          <div v-else style="padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;">
            <i class="el-icon-warning" style="color: #e6a23c; margin-right: 5px;"></i>
            <span style="color: #e6a23c;">暂未设置赞助商图片</span>
          </div>
        </el-form-item>
        <el-form-item label="上传新图片">
          <image-upload v-model="sponsorForm.sponsorUnit" :limit="1"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSponsorForm">确 定</el-button>
        <el-button @click="cancelSponsor">取 消</el-button>
      </div>
    </el-dialog>



    <!-- 查看详情对话框 -->
    <el-dialog title="项目报名详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">{{ viewForm.projectName }}</el-descriptions-item>
        <el-descriptions-item label="团队规模">{{ viewForm.teamSize }}人</el-descriptions-item>
        <el-descriptions-item label="城市">{{ viewForm.city }}</el-descriptions-item>
        <el-descriptions-item label="赛区">{{ viewForm.competitionArea }}</el-descriptions-item>
        <el-descriptions-item label="行业">{{ viewForm.industry }}</el-descriptions-item>
        <el-descriptions-item label="天大校友">{{ viewForm.isTjuAlumni ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="项目描述" :span="2">{{ viewForm.projectDescription }}</el-descriptions-item>
        <el-descriptions-item label="是否有公司">{{ viewForm.hasCompany ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="公司名称" v-if="viewForm.hasCompany">{{ viewForm.companyName }}</el-descriptions-item>
        <el-descriptions-item label="去年营收" v-if="viewForm.hasCompany">{{ viewForm.lastYearRevenue }}万元</el-descriptions-item>
        <el-descriptions-item label="项目估值" v-if="viewForm.hasCompany">{{ viewForm.projectValuation }}万元</el-descriptions-item>
        <el-descriptions-item label="最新融资轮次" v-if="viewForm.hasCompany">{{ viewForm.latestFundingRound }}</el-descriptions-item>
        <el-descriptions-item label="投资机构" v-if="viewForm.hasCompany">{{ viewForm.investmentInstitution }}</el-descriptions-item>
        <el-descriptions-item label="公司logo" v-if="viewForm.hasCompany && viewForm.companyLogo">
          <el-image
            style="width: 100px; height: 60px"
            :src="viewForm.companyLogo"
            :preview-src-list="[viewForm.companyLogo]"
            fit="cover">
          </el-image>
        </el-descriptions-item>
        <el-descriptions-item label="项目BP" v-if="viewForm.projectBp">
          <el-link type="primary" :href="viewForm.projectBp" target="_blank" :underline="false">
            <i class="el-icon-download"></i> 下载项目BP
          </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="推荐人">{{ viewForm.recommender }}</el-descriptions-item>
        <el-descriptions-item label="赞助单位" v-if="viewForm.sponsorUnit">
          <el-image
            style="width: 120px; height: 60px"
            :src="viewForm.sponsorUnit"
            :preview-src-list="[viewForm.sponsorUnit]"
            fit="contain">
          </el-image>
        </el-descriptions-item>
        <el-descriptions-item label="联系人姓名">{{ viewForm.contactName }}</el-descriptions-item>
        <el-descriptions-item label="联系人电话">{{ viewForm.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="联系人微信">{{ viewForm.contactWechat }}</el-descriptions-item>
        <el-descriptions-item label="联系人职位">{{ viewForm.contactPosition }}</el-descriptions-item>
        <el-descriptions-item label="报名时间">{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ getStatusText(viewForm.status) }}</el-descriptions-item>
        <el-descriptions-item label="审核时间" v-if="viewForm.auditTime">{{ parseTime(viewForm.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="审核备注" v-if="viewForm.auditRemark">{{ viewForm.auditRemark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProject, delProject, auditProject, updateSponsorImage, getSponsorImage } from "@/api/miniapp/haitang/project";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "Project",
  components: {
    ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目报名表格数据
      projectList: [],
      // 是否显示赞助商图片弹出层
      sponsorOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        city: null,
        industry: null,
        status: null
      },
      // 赞助商表单参数
      sponsorForm: {
        sponsorUnit: null
      },
      // 审核表单参数
      auditForm: {},
      // 查看详情表单参数
      viewForm: {},
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目报名列表 */
    getList() {
      this.loading = true;
      listProject(this.queryParams).then(response => {
        this.projectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },

    /** 赞助商图片上传按钮操作 */
    handleSponsorUpload() {
      getSponsorImage().then(response => {
        this.sponsorForm.sponsorUnit = response.data || '';
        this.sponsorOpen = true;
      }).catch(error => {
        this.sponsorForm.sponsorUnit = '';
        this.sponsorOpen = true;
      });
    },
    /** 取消赞助商图片上传 */
    cancelSponsor() {
      this.sponsorOpen = false;
      // 不要清空数据，保持原有数据
      // this.sponsorForm.sponsorUnit = null;
    },
    /** 提交赞助商图片 */
    submitSponsorForm() {
      updateSponsorImage(this.sponsorForm.sponsorUnit).then(response => {
        this.$modal.msgSuccess("赞助商图片更新成功");
        this.sponsorOpen = false;
      }).catch(error => {
        this.$modal.msgError("更新赞助商图片失败");
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = row;
      this.viewOpen = true;
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: 2,
        auditRemark: null
      };
      this.auditOpen = true;
    },

    /** 审核提交按钮 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditProject(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消审核 */
    cancelAudit() {
      this.auditOpen = false;
      this.auditForm = {};
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除项目报名编号为"' + ids + '"的数据项？').then(function() {
        return delProject(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/haitang/project/export', {
        ...this.queryParams
      }, `project_${new Date().getTime()}.xlsx`)
    },
    /** 获取状态文本 */
    getStatusText(status) {
      switch (status) {
        case 1: return '待审核';
        case 2: return '审核通过';
        case 3: return '审核不通过';
        default: return '未知';
      }
    },
    /** 获取状态类型 */
    getStatusType(status) {
      switch (status) {
        case 1: return 'warning';
        case 2: return 'success';
        case 3: return 'danger';
        default: return 'info';
      }
    }
  }
};
</script> 