{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753944331124}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRBY3Rpdml0eUNvbmZpZywgdXBkYXRlQWN0aXZpdHlDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS1jb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJYaXFpbmdBY3Rpdml0eUNvbmZpZyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuihqOWNlemFjee9ruW8ueWHuuWxgg0KICAgICAgZm9ybUNvbmZpZ09wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S66KGo5Y2V6aKE6KeI5by55Ye65bGCDQogICAgICBwcmV2aWV3RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDooajljZXlrZfmrrXphY3nva4NCiAgICAgIGZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5rS75YqoSUTvvIjlm7rlrprkuLox77yM5Zug5Li65Y+q5pyJ5LiA5Liq6Lev5ryU5rS75Yqo6YWN572u77yJDQogICAgICBhY3Rpdml0eUlkOiAxDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmxvYWRGb3JtQ29uZmlnKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5Yqg6L296KGo5Y2V6YWN572uICovDQogICAgbG9hZEZvcm1Db25maWcoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2V0QWN0aXZpdHlDb25maWcodGhpcy5hY3Rpdml0eUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IEpTT04ucGFyc2UocmVzcG9uc2UuZGF0YS5mb3JtQ29uZmlnKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOihqOWNlemFjee9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUZvcm1Db25maWcoKSB7DQogICAgICB0aGlzLmZvcm1Db25maWdPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmt7vliqDooajljZXlrZfmrrUgKi8NCiAgICBhZGRGb3JtRmllbGQoKSB7DQogICAgICAvLyDnlJ/miJDpu5jorqTnmoTllK/kuIDlrZfmrrXlkI0NCiAgICAgIGNvbnN0IGRlZmF1bHROYW1lID0gdGhpcy5nZW5lcmF0ZVVuaXF1ZUZpZWxkTmFtZSgnZmllbGQnKTsNCg0KICAgICAgdGhpcy5mb3JtRmllbGRzLnB1c2goew0KICAgICAgICBuYW1lOiBkZWZhdWx0TmFtZSwNCiAgICAgICAgbGFiZWw6ICcnLA0KICAgICAgICB0eXBlOiAnaW5wdXQnLA0KICAgICAgICByZXF1aXJlZDogZmFsc2UsDQogICAgICAgIG9wdGlvbnM6ICcnDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTooajljZXlrZfmrrUgKi8NCiAgICByZW1vdmVGb3JtRmllbGQoaW5kZXgpIHsNCiAgICAgIHRoaXMuZm9ybUZpZWxkcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgIH0sDQogICAgLyoqIOenu+WKqOWtl+auteS9jee9riAqLw0KICAgIG1vdmVGaWVsZChpbmRleCwgZGlyZWN0aW9uKSB7DQogICAgICBjb25zdCBuZXdJbmRleCA9IGluZGV4ICsgZGlyZWN0aW9uOw0KICAgICAgaWYgKG5ld0luZGV4ID49IDAgJiYgbmV3SW5kZXggPCB0aGlzLmZvcm1GaWVsZHMubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IHRlbXAgPSB0aGlzLmZvcm1GaWVsZHNbaW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtRmllbGRzLCBpbmRleCwgdGhpcy5mb3JtRmllbGRzW25ld0luZGV4XSk7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1GaWVsZHMsIG5ld0luZGV4LCB0ZW1wKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmm7TmlrDlrZfmrrXlkI3np7AgKi8NCiAgICB1cGRhdGVGaWVsZE5hbWUoZmllbGQsIGxhYmVsKSB7DQogICAgICBpZiAoIWZpZWxkLm5hbWUgfHwgZmllbGQubmFtZSA9PT0gJycpIHsNCiAgICAgICAgZmllbGQubmFtZSA9IHRoaXMuZ2VuZXJhdGVVbmlxdWVGaWVsZE5hbWUobGFiZWwpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOeUn+aIkOWUr+S4gOWtl+auteWQjeensCAqLw0KICAgIGdlbmVyYXRlVW5pcXVlRmllbGROYW1lKGxhYmVsKSB7DQogICAgICBjb25zdCBwaW55aW4gPSB7DQogICAgICAgICflp5PlkI0nOiAnbmFtZScsDQogICAgICAgICfogZTns7vnlLXor50nOiAncGhvbmUnLA0KICAgICAgICAn55S16K+dJzogJ3Bob25lJywNCiAgICAgICAgJ+mCrueusSc6ICdlbWFpbCcsDQogICAgICAgICfpgq7nrrHlnLDlnYAnOiAnZW1haWwnLA0KICAgICAgICAn5YWs5Y+4JzogJ2NvbXBhbnknLA0KICAgICAgICAn6aG555uu5ZCN56ewJzogJ3Byb2plY3RfbmFtZScsDQogICAgICAgICfpobnnm67mj4/ov7AnOiAncHJvamVjdF9kZXNjcmlwdGlvbicsDQogICAgICAgICflm6LpmJ/op4TmqKEnOiAndGVhbV9zaXplJw0KICAgICAgfTsNCg0KICAgICAgLy8g55Sf5oiQ5Z+656GA5ZCN56ewDQogICAgICBsZXQgYmFzZU5hbWUgPSBwaW55aW5bbGFiZWxdIHx8IGxhYmVsLnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvW1xzXHU0ZTAwLVx1OWZhNV0rL2csICdfJykucmVwbGFjZSgvW15cd19dL2csICcnKTsNCg0KICAgICAgLy8g56Gu5L+d5ZCN56ew5LiN5Li656m6DQogICAgICBpZiAoIWJhc2VOYW1lKSB7DQogICAgICAgIGJhc2VOYW1lID0gJ2ZpZWxkJzsNCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l5ZCN56ew5piv5ZCm5bey5a2Y5Zyo77yM5aaC5p6c5a2Y5Zyo5YiZ5re75Yqg5pWw5a2X5ZCO57yADQogICAgICBsZXQgdW5pcXVlTmFtZSA9IGJhc2VOYW1lOw0KICAgICAgbGV0IGNvdW50ZXIgPSAxOw0KDQogICAgICB3aGlsZSAodGhpcy5pc0ZpZWxkTmFtZUV4aXN0cyh1bmlxdWVOYW1lKSkgew0KICAgICAgICB1bmlxdWVOYW1lID0gYCR7YmFzZU5hbWV9XyR7Y291bnRlcn1gOw0KICAgICAgICBjb3VudGVyKys7DQogICAgICB9DQoNCiAgICAgIHJldHVybiB1bmlxdWVOYW1lOw0KICAgIH0sDQogICAgLyoqIOajgOafpeWtl+auteWQjeensOaYr+WQpuW3suWtmOWcqCAqLw0KICAgIGlzRmllbGROYW1lRXhpc3RzKG5hbWUpIHsNCiAgICAgIHJldHVybiB0aGlzLmZvcm1GaWVsZHMuc29tZShmaWVsZCA9PiBmaWVsZC5uYW1lID09PSBuYW1lKTsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrZfmrrXlm77moIcgKi8NCiAgICBnZXRGaWVsZEljb24odHlwZSkgew0KICAgICAgY29uc3QgaWNvbnMgPSB7DQogICAgICAgIGlucHV0OiAnZWwtaWNvbi1lZGl0JywNCiAgICAgICAgdGV4dGFyZWE6ICdlbC1pY29uLWRvY3VtZW50JywNCiAgICAgICAgbnVtYmVyOiAnZWwtaWNvbi1zLWRhdGEnLA0KICAgICAgICBlbWFpbDogJ2VsLWljb24tbWVzc2FnZScsDQogICAgICAgIHRlbDogJ2VsLWljb24tcGhvbmUnLA0KICAgICAgICByYWRpbzogJ2VsLWljb24tc3VjY2VzcycsDQogICAgICAgIGNoZWNrYm94OiAnZWwtaWNvbi1jaGVjaycsDQogICAgICAgIHNlbGVjdDogJ2VsLWljb24tYXJyb3ctZG93bicsDQogICAgICAgIHJhZGlvX290aGVyOiAnZWwtaWNvbi1jaXJjbGUtcGx1cycsDQogICAgICAgIGNoZWNrYm94X290aGVyOiAnZWwtaWNvbi1zcXVhcmUtcGx1cycsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ2VsLWljb24tcGx1cycsDQogICAgICAgIGRhdGU6ICdlbC1pY29uLWRhdGUnLA0KICAgICAgICBmaWxlOiAnZWwtaWNvbi11cGxvYWQnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICdlbC1pY29uLWVkaXQnOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhuaooeadv+WRveS7pCAqLw0KICAgIGhhbmRsZVRlbXBsYXRlQ29tbWFuZChjb21tYW5kKSB7DQogICAgICBpZiAoY29tbWFuZCA9PT0gJ2NsZWFyJykgew0KICAgICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHmuIXnqbrmiYDmnInlrZfmrrXlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gW107DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7LmuIXnqbrmiYDmnInlrZfmrrUnKTsNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGVtcGxhdGVzID0gew0KICAgICAgICBiYXNpYzogWw0KICAgICAgICAgIHsgbGFiZWw6ICflp5PlkI0nLCBuYW1lOiAnJywgdHlwZTogJ2lucHV0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+iBlOezu+eUteivnScsIG5hbWU6ICcnLCB0eXBlOiAndGVsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mCrueuseWcsOWdgCcsIG5hbWU6ICcnLCB0eXBlOiAnZW1haWwnLCByZXF1aXJlZDogZmFsc2UsIG9wdGlvbnM6ICcnIH0NCiAgICAgICAgXSwNCiAgICAgICAgcm9hZHNob3c6IFsNCiAgICAgICAgICB7IGxhYmVsOiAn5aeT5ZCNJywgbmFtZTogJycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAnJywgdHlwZTogJ3RlbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpgq7nrrHlnLDlnYAnLCBuYW1lOiAnJywgdHlwZTogJ2VtYWlsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+WFrOWPuC/lm6LpmJ8nLCBuYW1lOiAnJywgdHlwZTogJ2lucHV0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebruWQjeensCcsIG5hbWU6ICcnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu5p2l5rqQJywgbmFtZTogJycsIHR5cGU6ICdyYWRpb19vdGhlcicsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAn56S+5LyaLOmrmOagoSznp5HnoJTpmaLmiYAs5LyB5Lia5YaF6YOoJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67pmLbmrrUnLCBuYW1lOiAnJywgdHlwZTogJ3JhZGlvJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICfmpoLlv7XpmLbmrrUs5byA5Y+R6Zi25q61LOa1i+ivlemYtuautSzkuIrnur/pmLbmrrUnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+WboumYn+inhOaooScsIG5hbWU6ICcnLCB0eXBlOiAnc2VsZWN0JywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcx5Lq6LDItM+S6uiw0LTXkurosNi0xMOS6uiwxMOS6uuS7peS4iicgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6J6N6LWE6ZyA5rGCJywgbmFtZTogJycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5ZWG5Lia6K6h5YiS5LmmJywgbmFtZTogJycsIHR5cGU6ICdmaWxlJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebruaPj+i/sCcsIG5hbWU6ICcnLCB0eXBlOiAndGV4dGFyZWEnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfQ0KICAgICAgICBdDQogICAgICB9Ow0KDQogICAgICBpZiAodGVtcGxhdGVzW2NvbW1hbmRdKSB7DQogICAgICAgIC8vIOa4heepuueOsOacieWtl+autQ0KICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSBbXTsNCg0KICAgICAgICAvLyDkuLrmr4/kuKrmqKHmnb/lrZfmrrXnlJ/miJDllK/kuIDnmoRuYW1lDQogICAgICAgIGNvbnN0IHRlbXBsYXRlRmllbGRzID0gdGVtcGxhdGVzW2NvbW1hbmRdLm1hcChmaWVsZCA9PiAoew0KICAgICAgICAgIC4uLmZpZWxkLA0KICAgICAgICAgIG5hbWU6IHRoaXMuZ2VuZXJhdGVVbmlxdWVGaWVsZE5hbWUoZmllbGQubGFiZWwpDQogICAgICAgIH0pKTsNCg0KICAgICAgICB0aGlzLmZvcm1GaWVsZHMgPSB0ZW1wbGF0ZUZpZWxkczsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmqKHmnb/lupTnlKjmiJDlip8nKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDpooTop4jooajljZUgKi8NCiAgICBwcmV2aWV3Rm9ybSgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm1GaWVsZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI5re75Yqg6KGo5Y2V5a2X5q61Jyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMucHJldmlld0RpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOS/neWtmOihqOWNlemFjee9riAqLw0KICAgIHNhdmVGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybUZpZWxkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7foh7PlsJHmt7vliqDkuIDkuKrooajljZXlrZfmrrUnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4HlrZfmrrXphY3nva4NCiAgICAgIGNvbnN0IGZpZWxkTmFtZXMgPSBbXTsNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5mb3JtRmllbGRzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGZpZWxkID0gdGhpcy5mb3JtRmllbGRzW2ldOw0KDQogICAgICAgIC8vIOmqjOivgeagh+etvuS4jeiDveS4uuepug0KICAgICAgICBpZiAoIWZpZWxkLmxhYmVsKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg56ysJHtpICsgMX3kuKrlrZfmrrXnmoTmoIfnrb7kuI3og73kuLrnqbpgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4HlrZfmrrXlkI3np7DkuI3og73kuLrnqboNCiAgICAgICAgaWYgKCFmaWVsZC5uYW1lKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg56ysJHtpICsgMX3kuKrlrZfmrrXnmoTlkI3np7DkuI3og73kuLrnqbpgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4HlrZfmrrXlkI3np7DllK/kuIDmgKcNCiAgICAgICAgaWYgKGZpZWxkTmFtZXMuaW5jbHVkZXMoZmllbGQubmFtZSkpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlrZfmrrXlkI3np7AiJHtmaWVsZC5uYW1lfSLph43lpI3vvIzor7fnoa7kv53mr4/kuKrlrZfmrrXlkI3np7DllK/kuIBgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgZmllbGROYW1lcy5wdXNoKGZpZWxkLm5hbWUpOw0KDQogICAgICAgIC8vIOmqjOivgemAiemhuemFjee9rg0KICAgICAgICBpZiAoWydyYWRpbycsICdjaGVja2JveCcsICdzZWxlY3QnLCAncmFkaW9fb3RoZXInLCAnY2hlY2tib3hfb3RoZXInLCAnc2VsZWN0X290aGVyJ10uaW5jbHVkZXMoZmllbGQudHlwZSkgJiYgIWZpZWxkLm9wdGlvbnMpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDlrZfmrrUiJHtmaWVsZC5sYWJlbH0i6ZyA6KaB6YWN572u6YCJ6aG5YCk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGNvbmZpZ0RhdGEgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm1GaWVsZHMpOw0KICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgYWN0aXZpdHlJZDogdGhpcy5hY3Rpdml0eUlkLA0KICAgICAgICBmb3JtQ29uZmlnOiBjb25maWdEYXRhDQogICAgICB9Ow0KDQogICAgICB1cGRhdGVBY3Rpdml0eUNvbmZpZyh1cGRhdGVEYXRhKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6KGo5Y2V6YWN572u5L+d5a2Y5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5sb2FkRm9ybUNvbmZpZygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5ZCN56ewICovDQogICAgZ2V0RmllbGRUeXBlTmFtZSh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTmFtZXMgPSB7DQogICAgICAgIGlucHV0OiAn5paH5pys6L6T5YWlJywNCiAgICAgICAgdGV4dGFyZWE6ICflpJrooYzmlofmnKwnLA0KICAgICAgICBudW1iZXI6ICfmlbDlrZfovpPlhaUnLA0KICAgICAgICBlbWFpbDogJ+mCrueusScsDQogICAgICAgIHRlbDogJ+eUteivnScsDQogICAgICAgIHJhZGlvOiAn5Y2V6YCJJywNCiAgICAgICAgY2hlY2tib3g6ICflpJrpgIknLA0KICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLA0KICAgICAgICByYWRpb19vdGhlcjogJ+WNlemAiSvlhbbku5YnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ+WkmumAiSvlhbbku5YnLA0KICAgICAgICBzZWxlY3Rfb3RoZXI6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgZGF0ZTogJ+aXpeacnycsDQogICAgICAgIGZpbGU6ICfmlofku7bkuIrkvKAnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVOYW1lc1t0eXBlXSB8fCAn5pyq55+l57G75Z6LJzsNCiAgICB9DQogIH0NCn07DQo="}, null]}