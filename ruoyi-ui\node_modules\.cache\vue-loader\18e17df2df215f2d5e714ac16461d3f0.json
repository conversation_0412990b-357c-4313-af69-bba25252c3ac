{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\consultation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\consultation\\index.vue", "mtime": 1753943808699}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/consultation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"contact-info-container\">\r\n      <div class=\"contact-header\">\r\n        <h3>报名咨询联系方式</h3>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleEdit\"\r\n          v-hasPermi=\"['miniapp:xiqing:consultation:edit']\"\r\n        >编辑</el-button>\r\n      </div>\r\n\r\n      <div class=\"contact-content\" v-loading=\"loading\">\r\n        <div class=\"contact-item\">\r\n          <label>联系人：</label>\r\n          <span>{{ contactInfo.contactName || '暂未设置' }}</span>\r\n        </div>\r\n        <div class=\"contact-item\">\r\n          <label>联系方式：</label>\r\n          <span>{{ contactInfo.contactMethod || '暂未设置' }}</span>\r\n        </div>\r\n        <div class=\"contact-item\" v-if=\"contactInfo.remark\">\r\n          <label>备注：</label>\r\n          <span>{{ contactInfo.remark }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 编辑联系信息对话框 -->\r\n    <el-dialog title=\"编辑联系信息\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"联系人\" prop=\"contactName\">\r\n          <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系方式\" prop=\"contactMethod\">\r\n          <el-input v-model=\"form.contactMethod\" placeholder=\"请输入联系方式\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getContactInfo, updateConsultation } from \"@/api/miniapp/xiqing/consultation\";\r\n\r\nexport default {\r\n  name: \"XiqingConsultation\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 联系信息\r\n      contactInfo: {},\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contactName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactMethod: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getContactInfo();\r\n  },\r\n  methods: {\r\n    /** 获取联系信息 */\r\n    getContactInfo() {\r\n      this.loading = true;\r\n      getContactInfo().then(response => {\r\n        this.contactInfo = response.data || {};\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.contactInfo = {};\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        consultationId: null,\r\n        contactName: null,\r\n        contactMethod: null,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 编辑按钮操作 */\r\n    handleEdit() {\r\n      this.reset();\r\n      this.form = {\r\n        consultationId: this.contactInfo.consultationId || null,\r\n        contactName: this.contactInfo.contactName || '',\r\n        contactMethod: this.contactInfo.contactMethod || '',\r\n        status: this.contactInfo.status || \"0\",\r\n        remark: this.contactInfo.remark || ''\r\n      };\r\n      this.open = true;\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          updateConsultation(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"保存成功\");\r\n            this.open = false;\r\n            this.getContactInfo();\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.contact-info-container {\r\n  max-width: 600px;\r\n  margin: 20px auto;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.contact-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contact-content {\r\n  padding: 24px;\r\n  min-height: 120px;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.contact-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.contact-item label {\r\n  min-width: 80px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  margin-right: 12px;\r\n}\r\n\r\n.contact-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n</style>"]}]}