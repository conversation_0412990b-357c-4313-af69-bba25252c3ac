{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\contact.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\contact.js", "mtime": 1753857767028}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listContact", "query", "request", "url", "method", "data", "getContact", "contactId", "addContact", "updateContact", "delContact", "contactIds", "ids", "Array", "isArray", "updateContactSort", "getEnabledContactList", "getContactByCode", "contactCode", "getContactByCodeForApp"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/contact.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询联系人管理列表\nexport function listContact(query) {\n  return request({\n    url: '/miniapp/contact/list',\n    method: 'post',\n    data: query\n  })\n}\n\n// 查询联系人管理详细\nexport function getContact(contactId) {\n  return request({\n    url: '/miniapp/contact/getInfo',\n    method: 'post',\n    data: contactId\n  })\n}\n\n// 新增联系人管理\nexport function addContact(data) {\n  return request({\n    url: '/miniapp/contact/add',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改联系人管理\nexport function updateContact(data) {\n  return request({\n    url: '/miniapp/contact/edit',\n    method: 'post',\n    data: data\n  })\n}\n\n// 删除联系人管理\nexport function delContact(contactIds) {\n  // 确保传递的是数组格式\n  const ids = Array.isArray(contactIds) ? contactIds : [contactIds];\n  return request({\n    url: '/miniapp/contact/remove',\n    method: 'post',\n    data: ids\n  })\n}\n\n// 更新联系人排序\nexport function updateContactSort(data) {\n  return request({\n    url: '/miniapp/contact/updateSort',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取启用的联系人列表（小程序端调用）\nexport function getEnabledContactList() {\n  return request({\n    url: '/miniapp/contact/app/getEnabledList',\n    method: 'post'\n  })\n}\n\n// 根据联系人编码获取联系人信息（管理端）\nexport function getContactByCode(contactCode) {\n  return request({\n    url: '/miniapp/contact/getByContactCode',\n    method: 'post',\n    data: contactCode\n  })\n}\n\n// 根据联系人编码获取联系人信息（小程序端）\nexport function getContactByCodeForApp(contactCode) {\n  return request({\n    url: '/miniapp/contact/app/getByContactCode',\n    method: 'post',\n    data: contactCode\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEJ;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACH,IAAI,EAAE;EAC/B,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACJ,IAAI,EAAE;EAClC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,UAAU,EAAE;EACrC;EACA,IAAMC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;EACjE,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEO;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,iBAAiBA,CAACV,IAAI,EAAE;EACtC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,gBAAgBA,CAACC,WAAW,EAAE;EAC5C,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEa;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,sBAAsBA,CAACD,WAAW,EAAE;EAClD,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEa;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}