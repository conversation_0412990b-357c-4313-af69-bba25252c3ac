{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753760187026}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_demand", "require", "_demandcategory", "_request", "_interopRequireDefault", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "demandList", "contactDialogVisible", "contactForm", "dockingId", "userName", "userPhone", "isContacted", "contactResult", "contactNotes", "contactTime", "contactRules", "required", "message", "trigger", "detailDialogVisible", "detailForm", "dockingList", "formDataList", "tableR<PERSON><PERSON><PERSON><PERSON>", "categoryList", "dynamicFields", "selectedCategoryName", "categoryFieldsData", "uploadHeaders", "Authorization", "$store", "getters", "token", "title", "open", "queryParams", "pageNum", "pageSize", "demandTitle", "categoryId", "demandStatus", "hasDocking", "timeFilter", "form", "rules", "demandDesc", "contactName", "contactPhone", "pattern", "computed", "groupedDynamicFields", "grouped", "for<PERSON>ach", "field", "moduleTitle", "push", "safeDynamicData", "safeData", "_objectSpread2", "default", "dynamicData", "type", "Array", "isArray", "created", "getList", "getCategoryList", "testNewDataFormat", "methods", "_defineProperty2", "_this", "listDemand", "then", "response", "rows", "catch", "error", "console", "$modal", "msgError", "_this2", "getEnabledDemandCategoryList", "cancel", "reset", "_this3", "demandId", "isTop", "remark", "Object", "keys", "key", "startsWith", "$delete", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this4", "<PERSON><PERSON><PERSON><PERSON>", "$set", "formData", "JSON", "parse", "fields", "categoryData", "value", "undefined", "processCategoryFieldsData", "loadDynamicFields", "e", "$nextTick", "$refs", "clearValidate", "submitForm", "_this5", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstErrorField", "isEmpty", "label", "concat", "validate", "valid", "categoryDataWithValues", "stringify", "log", "updateDemand", "msgSuccess", "<PERSON><PERSON><PERSON><PERSON>", "handleDetail", "_this6", "dockingLoading", "parseFormDataForDisplay", "request", "url", "method", "code", "msg", "finally", "handleContactRecord", "dockingRow", "submitContactForm", "_this7", "parseTime", "Date", "newIsContacted", "newContactResult", "newContactNotes", "newContactTime", "updateContactStatus", "dockingItem", "find", "refreshDetailDockingList", "_this8", "debugDockingData", "displayList", "_typeof2", "getFileNameFromUrl", "parts", "split", "getContactResultType", "result", "typeMap", "index", "isContactedType", "handleDelete", "_this9", "demandIds", "confirmText", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "handleExport", "download", "getTime", "handleToggleTop", "_this0", "text", "updateData", "handleOffShelf", "_this1", "offShelfDemand", "handleOnShelf", "_this10", "onShelfDemand", "onCategoryChange", "category", "cat", "formFields", "formConfig", "_this11", "categoryName", "module", "fileData", "trim", "fileName", "pop", "ruleName", "getFieldOptions", "options", "option", "filter", "handleFileSuccess", "file", "fileList", "fileUrl", "handleFieldInput", "handleFileRemove", "getCheckboxValue", "fieldName", "updateCheckboxValue", "getFileList", "_this12", "files", "uid", "status", "getUploadedFiles", "handleFilePreview", "window", "downloadFile", "link", "document", "createElement", "href", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "removeUploadedFile", "newValue", "_toConsumableArray2", "splice", "removeFileUrl", "match", "updateFieldValue", "getCategoryName", "_this13", "testData"], "sources": ["src/views/miniapp/business/demand/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n        <el-input\r\n          v-model=\"queryParams.demandTitle\"\r\n          placeholder=\"请输入需求标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"需求类型\" prop=\"categoryId\">\r\n        <el-select v-model=\"queryParams.categoryId\" placeholder=\"请选择需求类型\" clearable>\r\n          <el-option\r\n            v-for=\"category in categoryList\"\r\n            :key=\"category.categoryId\"\r\n            :label=\"category.categoryName\"\r\n            :value=\"category.categoryId\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"需求状态\" prop=\"demandStatus\">\r\n        <el-select v-model=\"queryParams.demandStatus\" placeholder=\"请选择需求状态\" clearable>\r\n          <el-option label=\"已发布\" value=\"0\" />\r\n          <el-option label=\"已对接\" value=\"1\" />\r\n          <el-option label=\"已下架\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"对接状态\" prop=\"hasDocking\">\r\n        <el-select v-model=\"queryParams.hasDocking\" placeholder=\"请选择对接状态\" clearable>\r\n          <el-option label=\"未对接\" value=\"0\" />\r\n          <el-option label=\"对接中\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"时间筛选\" prop=\"timeFilter\">\r\n        <el-select v-model=\"queryParams.timeFilter\" placeholder=\"请选择时间范围\" clearable>\r\n          <el-option label=\"一周内发布\" value=\"week_within\" />\r\n          <el-option label=\"发布满一周\" value=\"week_over\" />\r\n          <el-option label=\"发布满一月\" value=\"month_over\" />\r\n          <el-option label=\"发布满一年\" value=\"year_over\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:demand:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:demand:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:demand:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:demand:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"demandList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n      <el-table-column label=\"需求ID\" align=\"center\" prop=\"demandId\" width=\"70\" />\r\n      <el-table-column label=\"需求标题\" align=\"left\" prop=\"demandTitle\" min-width=\"180\" show-overflow-tooltip />\r\n      <el-table-column label=\"需求类型\" align=\"center\" prop=\"categoryName\" width=\"90\" />\r\n      <el-table-column label=\"需求状态\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.demandStatus === '0'\" type=\"success\" size=\"small\">已发布</el-tag>\r\n          <el-tag v-else-if=\"scope.row.demandStatus === '1'\" type=\"warning\" size=\"small\">已对接</el-tag>\r\n          <el-tag v-else type=\"danger\" size=\"small\">已下架</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"对接状态\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.hasDocking === true || scope.row.hasDocking === 1\" type=\"warning\" size=\"small\">对接中</el-tag>\r\n          <el-tag v-else type=\"info\" size=\"small\">未对接</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否置顶\" align=\"center\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.isTop === '1'\" type=\"warning\" size=\"mini\">置顶</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"320\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"table-actions\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleDetail(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:query']\"\r\n            >详情</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n            >修改</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:remove']\"\r\n            >删除</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleToggleTop(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n            >{{ scope.row.isTop === '1' ? '取消置顶' : '置顶' }}</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.demandStatus !== '2'\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleOffShelf(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n              style=\"color: #E6A23C;\"\r\n            >下架</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.demandStatus === '2'\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleOnShelf(scope.row)\"\r\n              v-hasPermi=\"['miniapp:demand:edit']\"\r\n              style=\"color: #67C23A;\"\r\n            >上架</el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 联系记录弹窗 -->\r\n    <el-dialog title=\"联系记录\" :visible.sync=\"contactDialogVisible\" width=\"50%\" append-to-body>\r\n      <el-form ref=\"contactForm\" :model=\"contactForm\" :rules=\"contactRules\" label-width=\"100px\">\r\n        <el-form-item label=\"对接用户\">\r\n          <span>{{ contactForm.userName }} ({{ contactForm.userPhone }})</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否已联系\" prop=\"isContacted\">\r\n          <el-radio-group v-model=\"contactForm.isContacted\">\r\n            <el-radio label=\"0\">未联系</el-radio>\r\n            <el-radio label=\"1\">已联系</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系结果\" prop=\"contactResult\" v-if=\"contactForm.isContacted === '1'\">\r\n          <el-select v-model=\"contactForm.contactResult\" placeholder=\"请选择联系结果\" clearable style=\"width: 100%;\">\r\n            <el-option label=\"联系成功\" value=\"联系成功\" />\r\n            <el-option label=\"无人接听\" value=\"无人接听\" />\r\n            <el-option label=\"号码错误\" value=\"号码错误\" />\r\n            <el-option label=\"拒绝沟通\" value=\"拒绝沟通\" />\r\n            <el-option label=\"稍后联系\" value=\"稍后联系\" />\r\n            <el-option label=\"已有合作\" value=\"已有合作\" />\r\n            <el-option label=\"不感兴趣\" value=\"不感兴趣\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系备注\" prop=\"contactNotes\">\r\n          <el-input\r\n            v-model=\"contactForm.contactNotes\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入联系备注，如沟通内容、后续计划等\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联系时间\" prop=\"contactTime\" v-if=\"contactForm.isContacted === '1'\">\r\n          <el-date-picker\r\n            v-model=\"contactForm.contactTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择联系时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%;\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"contactDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitContactForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 需求详情弹窗 -->\r\n    <el-dialog title=\"需求详情\" :visible.sync=\"detailDialogVisible\" width=\"70%\" append-to-body>\r\n      <div class=\"detail-content\">\r\n        <!-- 内容信息 -->\r\n        <div class=\"info-section\">\r\n          <h4 class=\"section-header\">内容信息</h4>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">标题：</span>\r\n            <span class=\"info-value\">{{ detailForm.demandTitle || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">类型：</span>\r\n            <span class=\"info-value\">{{ detailForm.categoryName || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">状态：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.demandStatus === '0'\" type=\"success\" size=\"small\">已发布</el-tag>\r\n              <el-tag v-else-if=\"detailForm.demandStatus === '1'\" type=\"warning\" size=\"small\">已对接</el-tag>\r\n              <el-tag v-else type=\"danger\" size=\"small\">已下架</el-tag>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">对接状态：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.hasDocking === true || detailForm.hasDocking === 1\" type=\"warning\" size=\"small\">对接中</el-tag>\r\n              <el-tag v-else type=\"info\" size=\"small\">未对接</el-tag>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">是否置顶：</span>\r\n            <span class=\"info-value\">\r\n              <el-tag v-if=\"detailForm.isTop === '1'\" type=\"warning\" size=\"small\">置顶</el-tag>\r\n              <span v-else>否</span>\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">浏览次数：</span>\r\n            <span class=\"info-value\">{{ detailForm.viewCount || 0 }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">发布时间：</span>\r\n            <span class=\"info-value\">{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\" v-if=\"detailForm.demandDesc\">\r\n            <span class=\"info-label\">需求描述：</span>\r\n            <div class=\"info-value description-text\">{{ detailForm.demandDesc }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">联系人：</span>\r\n            <span class=\"info-value\">{{ detailForm.contactName || '' }}</span>\r\n          </div>\r\n\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">联系电话：</span>\r\n            <span class=\"info-value\">{{ detailForm.contactPhone || '' }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分隔线 -->\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-document\"></i>\r\n          表单数据\r\n        </el-divider>\r\n\r\n        <!-- 表单数据部分 -->\r\n        <div class=\"form-data-section\" v-if=\"detailForm.formDataList && detailForm.formDataList.length > 0\">\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item\r\n              v-for=\"(item, index) in detailForm.formDataList\"\r\n              :key=\"index\"\r\n              :label=\"item.label\"\r\n              :label-style=\"{ width: '120px', fontWeight: 'bold' }\"\r\n            >\r\n              <template v-if=\"item.type === 'textarea'\">\r\n                <div class=\"textarea-content\">{{ item.value || '未填写' }}</div>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'select' || item.type === 'radio'\">\r\n                <el-tag type=\"primary\" size=\"small\">{{ item.value || '未选择' }}</el-tag>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'checkbox'\">\r\n                <div v-if=\"Array.isArray(item.value) && item.value.length > 0\">\r\n                  <el-tag v-for=\"val in item.value\" :key=\"val\" type=\"primary\" size=\"small\" style=\"margin-right: 5px;\">{{ val }}</el-tag>\r\n                </div>\r\n                <span v-else>未选择</span>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'file'\">\r\n                <div v-if=\"Array.isArray(item.value) && item.value.length > 0\">\r\n                  <div v-for=\"(file, fileIndex) in item.value\" :key=\"fileIndex\" class=\"file-item\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <a :href=\"file.url || file\" target=\"_blank\" class=\"file-link\">\r\n                      {{ file.name || getFileNameFromUrl(file.url || file) }}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n                <span v-else-if=\"typeof item.value === 'string' && item.value\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <a :href=\"item.value\" target=\"_blank\" class=\"file-link\">\r\n                    {{ getFileNameFromUrl(item.value) }}\r\n                  </a>\r\n                </span>\r\n                <span v-else>未上传</span>\r\n              </template>\r\n              <template v-else-if=\"item.type === 'tel'\">\r\n                <span class=\"phone-number\">{{ item.value || '未填写' }}</span>\r\n              </template>\r\n              <template v-else>\r\n                <span>{{ item.value || '未填写' }}</span>\r\n              </template>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n\r\n        <!-- 分隔线 -->\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          对接记录\r\n          <el-tag size=\"small\" type=\"info\" style=\"margin-left: 8px;\">\r\n            共 {{ (detailForm.dockingList || []).length }} 条记录\r\n          </el-tag>\r\n        </el-divider>\r\n\r\n        <!-- 对接记录部分 -->\r\n        <div class=\"docking-section\">\r\n          <el-table\r\n            :data=\"detailForm.dockingList || []\"\r\n            size=\"small\"\r\n            border\r\n            :key=\"'docking-table-' + tableRefreshKey\"\r\n            class=\"docking-table\"\r\n            v-loading=\"detailForm.dockingLoading\"\r\n          >\r\n            <!-- 自定义空数据提示 -->\r\n            <template slot=\"empty\">\r\n              <div class=\"empty-data\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <p>暂无对接记录</p>\r\n              </div>\r\n            </template>\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n\r\n              <el-table-column label=\"对接用户\" width=\"200\" align=\"left\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"user-info\">\r\n                    <div class=\"user-name\">\r\n                      <i class=\"el-icon-user\"></i>\r\n                      <strong>{{ scope.row.userName || '未知用户' }}</strong>\r\n                    </div>\r\n                    <div class=\"user-phone\" v-if=\"scope.row.userPhone\">\r\n                      <i class=\"el-icon-phone\"></i>\r\n                      {{ scope.row.userPhone }}\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"工作信息\" min-width=\"200\" align=\"left\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"work-info\">\r\n                    <div class=\"company\" v-if=\"scope.row.userCompany\">\r\n                      <i class=\"el-icon-office-building\"></i>\r\n                      {{ scope.row.userCompany }}\r\n                    </div>\r\n                    <div class=\"position\" v-if=\"scope.row.userPosition\">\r\n                      <i class=\"el-icon-suitcase\"></i>\r\n                      {{ scope.row.userPosition }}\r\n                    </div>\r\n                    <div v-if=\"!scope.row.userCompany && !scope.row.userPosition\" class=\"no-info\">\r\n                      暂无工作信息\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"对接时间\" width=\"140\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"docking-time\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    {{ parseTime(scope.row.dockingTime, '{y}-{m}-{d} {h}:{i}') }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"联系状态\" width=\"100\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag\r\n                    :type=\"(scope.row.isContacted === '1' || scope.row.isContacted === 1) ? 'success' : 'warning'\"\r\n                    size=\"small\"\r\n                  >\r\n                    {{ (scope.row.isContacted === '1' || scope.row.isContacted === 1) ? '已联系' : '未联系' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-edit-outline\"\r\n                    @click=\"handleContactRecord(scope.row)\"\r\n                  >\r\n                    联系记录\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改需求对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"需求类型\" prop=\"categoryId\">\r\n              <el-select v-model=\"form.categoryId\" placeholder=\"请选择需求类型\" style=\"width: 100%\" @change=\"onCategoryChange\">\r\n                <el-option\r\n                  v-for=\"category in categoryList\"\r\n                  :key=\"category.categoryId\"\r\n                  :label=\"category.categoryName\"\r\n                  :value=\"category.categoryId\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"需求状态\" prop=\"demandStatus\">\r\n              <el-select v-model=\"form.demandStatus\" placeholder=\"请选择需求状态\" style=\"width: 100%\">\r\n                <el-option label=\"已发布\" value=\"0\" />\r\n                <el-option label=\"已对接\" value=\"1\" />\r\n                <el-option label=\"已下架\" value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n          <el-input v-model=\"form.demandTitle\" placeholder=\"请输入需求标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"需求描述\" prop=\"demandDesc\">\r\n          <el-input v-model=\"form.demandDesc\" type=\"textarea\" placeholder=\"请输入需求描述\" :rows=\"4\" />\r\n        </el-form-item>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人姓名\" prop=\"contactName\">\r\n              <el-input v-model=\"form.contactName\" placeholder=\"请输入联系人姓名\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系人电话\" prop=\"contactPhone\">\r\n              <el-input v-model=\"form.contactPhone\" placeholder=\"请输入联系人电话\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否置顶\" prop=\"isTop\">\r\n              <el-radio-group v-model=\"form.isTop\">\r\n                <el-radio label=\"0\">否</el-radio>\r\n                <el-radio label=\"1\">是</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 动态表单字段 -->\r\n        <div v-if=\"categoryFieldsData && categoryFieldsData.length > 0\" class=\"dynamic-fields-section\">\r\n          <el-divider content-position=\"left\">\r\n            <span style=\"color: #409EFF; font-weight: bold;\">{{ getCategoryName() }}专属字段</span>\r\n          </el-divider>\r\n\r\n          <!-- 渲染分类字段 -->\r\n          <div v-for=\"(categoryData, categoryIndex) in categoryFieldsData\" :key=\"`category-${categoryIndex}`\" class=\"category-group\">\r\n            <div v-if=\"categoryData.name\" class=\"category-title\">\r\n              <i class=\"el-icon-folder-opened\"></i>\r\n              <span>{{ categoryData.name }}</span>\r\n            </div>\r\n            <div v-if=\"categoryData.description\" class=\"category-description\">\r\n              {{ categoryData.description }}\r\n            </div>\r\n\r\n            <div v-for=\"(field, fieldIndex) in categoryData.fields\" :key=\"`field-${field.name}-${fieldIndex}`\" class=\"dynamic-field-item\">\r\n              <div class=\"field-label\">\r\n                <span v-if=\"field.required\" class=\"required-mark\">*</span>\r\n                {{ field.label }}\r\n              </div>\r\n              <div class=\"field-content\">\r\n                <!-- 静态内容 -->\r\n                <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                  {{ field.staticContent }}\r\n                </div>\r\n                <!-- 文本输入 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'input'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 多行文本 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'textarea'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  type=\"textarea\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  :rows=\"3\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 数字输入 -->\r\n                <el-input-number\r\n                  v-else-if=\"field.type === 'number'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || 0\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 电话号码 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'tel'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 邮箱地址 -->\r\n                <el-input\r\n                  v-else-if=\"field.type === 'email'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请输入' + field.label\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 单选框 -->\r\n                <el-radio-group\r\n                  v-else-if=\"field.type === 'radio'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-radio\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-radio-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-radio>\r\n                </el-radio-group>\r\n                <!-- 多选框 -->\r\n                <el-checkbox-group\r\n                  v-else-if=\"field.type === 'checkbox'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || []\"\r\n                  @input=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-checkbox\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-checkbox-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-checkbox>\r\n                </el-checkbox-group>\r\n                <!-- 下拉选择 -->\r\n                <el-select\r\n                  v-else-if=\"field.type === 'select'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || ''\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"(option, optionIndex) in getFieldOptions(field)\"\r\n                    :key=\"`${field.name}-option-${optionIndex}-${option}`\"\r\n                    :label=\"option\"\r\n                    :value=\"option\"\r\n                  />\r\n                </el-select>\r\n                <!-- 日期选择 -->\r\n                <el-date-picker\r\n                  v-else-if=\"field.type === 'date'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || null\"\r\n                  type=\"date\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 时间选择 -->\r\n                <el-time-picker\r\n                  v-else-if=\"field.type === 'time'\"\r\n                  :value=\"form.dynamicData[field.name] || field.value || null\"\r\n                  :placeholder=\"field.placeholder || '请选择' + field.label\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"value => handleFieldInput(field, value)\"\r\n                />\r\n                <!-- 文件上传 -->\r\n                <div v-else-if=\"field.type === 'file'\">\r\n                  <!-- 如果已有文件URL，显示文件信息 -->\r\n                  <div v-if=\"field.value && typeof field.value === 'string' && field.value.startsWith('http')\" class=\"existing-file\">\r\n                    <div class=\"file-display\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a :href=\"field.value\" target=\"_blank\" class=\"file-link\">\r\n                        {{ getFileNameFromUrl(field.value) }}\r\n                      </a>\r\n                      <el-button\r\n                        type=\"text\"\r\n                        size=\"mini\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"removeFileUrl(field)\"\r\n                        class=\"remove-file-btn\"\r\n                      >\r\n                        删除\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 文件上传组件 -->\r\n                  <el-upload\r\n                    v-else\r\n                    action=\"/dev-api/common/upload\"\r\n                    :headers=\"uploadHeaders\"\r\n                    :on-success=\"(response, file, fileList) => handleFileSuccess(response, file, fileList, field)\"\r\n                    :on-remove=\"(file, fileList) => handleFileRemove(file, fileList, field)\"\r\n                    :file-list=\"getFileList(field)\"\r\n                    :on-preview=\"handleFilePreview\"\r\n                  >\r\n                    <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n                  </el-upload>\r\n\r\n                  <!-- 已上传文件列表显示（数组格式） -->\r\n                  <div v-if=\"Array.isArray(field.value) && field.value.length > 0\" class=\"uploaded-files-list\">\r\n                    <div class=\"uploaded-files-title\">已上传文件：</div>\r\n                    <div\r\n                      v-for=\"(file, index) in field.value\"\r\n                      :key=\"`uploaded-${field.name}-${index}`\"\r\n                      class=\"uploaded-file-item\"\r\n                    >\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a\r\n                        :href=\"file.url || file\"\r\n                        target=\"_blank\"\r\n                        class=\"file-link\"\r\n                        @click=\"downloadFile(file.url || file, file.name || getFileNameFromUrl(file))\"\r\n                      >\r\n                        {{ file.name || getFileNameFromUrl(file.url || file) }}\r\n                      </a>\r\n                      <el-button\r\n                        type=\"text\"\r\n                        size=\"mini\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"removeUploadedFile(field, index)\"\r\n                        class=\"remove-file-btn\"\r\n                      >\r\n                        删除\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n/* 详情页面样式 */\r\n.detail-content {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.info-section, .form-data-section, .docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  color: #303133;\r\n}\r\n\r\n.description-text {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.6;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.textarea-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-link {\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  margin-left: 5px;\r\n}\r\n\r\n.file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n/* 对接记录表格样式 */\r\n.docking-section .el-table {\r\n  margin-top: 0; /* 移除上边距，让表格紧贴标题 */\r\n}\r\n\r\n/* 空数据提示样式 */\r\n.empty-data {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  color: #C0C4CC;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.docking-section .user-name {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.docking-section .user-phone {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.no-data-simple {\r\n  text-align: center;\r\n  color: #909399;\r\n  padding: 20px;\r\n  font-size: 14px;\r\n}\r\n\r\n.contacted {\r\n  color: #67c23a;\r\n  margin-right: 5px;\r\n}\r\n\r\n.uncontacted {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 表格操作列样式优化 */\r\n.table-actions {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 2px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n  min-width: auto;\r\n}\r\n\r\n.table-actions .el-button + .el-button {\r\n  margin-left: 2px;\r\n}\r\n\r\n.el-table .small-padding .cell {\r\n  padding-left: 4px;\r\n  padding-right: 4px;\r\n  white-space: nowrap;\r\n  overflow: visible;\r\n}\r\n\r\n.el-table .fixed-width .cell {\r\n  padding-left: 4px;\r\n  padding-right: 4px;\r\n}\r\n\r\n/* 表格列间距优化 */\r\n.el-table th,\r\n.el-table td {\r\n  padding: 6px 0;\r\n}\r\n\r\n.el-table .cell {\r\n  padding-left: 6px;\r\n  padding-right: 6px;\r\n}\r\n\r\n/* 对接记录表格样式 */\r\n.docking-table {\r\n  margin-top: 0; /* 移除上边距，让表格紧贴标题 */\r\n}\r\n\r\n.docking-table .user-info {\r\n  text-align: left;\r\n}\r\n\r\n.docking-table .user-name {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .user-name i {\r\n  margin-right: 4px;\r\n  color: #409EFF;\r\n}\r\n\r\n.docking-table .user-phone {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .user-phone i {\r\n  margin-right: 4px;\r\n  color: #67C23A;\r\n}\r\n\r\n.docking-table .work-info {\r\n  text-align: left;\r\n}\r\n\r\n.docking-table .company,\r\n.docking-table .position {\r\n  margin-bottom: 4px;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.docking-table .company i,\r\n.docking-table .position i {\r\n  margin-right: 4px;\r\n  color: #909399;\r\n}\r\n\r\n.docking-table .no-info {\r\n  color: #C0C4CC;\r\n  font-size: 12px;\r\n  font-style: italic;\r\n}\r\n\r\n.docking-table .docking-time {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.docking-table .docking-time i {\r\n  margin-right: 4px;\r\n  color: #E6A23C;\r\n}\r\n\r\n/* 表格行样式 */\r\n.docking-table .el-table__row {\r\n  cursor: default;\r\n}\r\n\r\n.docking-table .el-table__row:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 分隔线样式 */\r\n.el-divider {\r\n  margin: 20px 0;\r\n}\r\n\r\n.el-divider__text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.el-divider__text i {\r\n  margin-right: 6px;\r\n  color: #409EFF;\r\n}\r\n\r\n/* 详情弹窗内容区域 */\r\n.docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 分隔线样式 */\r\n.el-divider {\r\n  margin: 20px 0;\r\n}\r\n\r\n.el-divider__text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.el-divider__text i {\r\n  margin-right: 6px;\r\n  color: #409EFF;\r\n}\r\n\r\n/* 详情弹窗内容区域 */\r\n.docking-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { listDemand, getDemand, delDemand, addDemand, updateDemand, offShelfDemand, onShelfDemand, updateContactStatus } from \"@/api/miniapp/demand\";\r\nimport { getEnabledDemandCategoryList } from \"@/api/miniapp/demandcategory\";\r\nimport request from '@/utils/request';\r\n\r\nexport default {\r\n  name: \"MiniDemand\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 需求表格数据\r\n      demandList: [],\r\n      // 联系记录弹窗\r\n      contactDialogVisible: false,\r\n      // 联系记录表单\r\n      contactForm: {\r\n        dockingId: null,\r\n        userName: '',\r\n        userPhone: '',\r\n        isContacted: '0',\r\n        contactResult: '',\r\n        contactNotes: '',\r\n        contactTime: ''\r\n      },\r\n      // 联系记录表单验证\r\n      contactRules: {\r\n        isContacted: [\r\n          { required: true, message: \"请选择是否已联系\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 详情弹窗\r\n      detailDialogVisible: false,\r\n      // 详情数据\r\n      detailForm: {\r\n        dockingList: [],\r\n        formDataList: []\r\n      },\r\n      // 表格刷新key\r\n      tableRefreshKey: 0,\r\n      // 需求类型列表\r\n      categoryList: [],\r\n      // 动态表单字段\r\n      dynamicFields: [],\r\n      // 选中的类型名称\r\n      selectedCategoryName: '',\r\n      // 分类字段数据（新格式）\r\n      categoryFieldsData: [],\r\n      // 上传请求头\r\n      uploadHeaders: {\r\n        Authorization: \"Bearer \" + this.$store.getters.token\r\n      },\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        demandTitle: null,\r\n        categoryId: null,\r\n        demandStatus: null,\r\n        hasDocking: null,\r\n        timeFilter: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        categoryId: [\r\n          { required: true, message: \"需求类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" }\r\n        ],\r\n        demandDesc: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactName: [\r\n          { required: true, message: \"联系人姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系人电话不能为空\", trigger: \"blur\" },\r\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\r\n        ],\r\n        demandStatus: [\r\n          { required: true, message: \"需求状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    /** 按模块分组的动态字段 */\r\n    groupedDynamicFields() {\r\n      const grouped = {};\r\n      this.dynamicFields.forEach(field => {\r\n        const moduleTitle = field.moduleTitle || '其他字段';\r\n        if (!grouped[moduleTitle]) {\r\n          grouped[moduleTitle] = [];\r\n        }\r\n        grouped[moduleTitle].push(field);\r\n      });\r\n      return grouped;\r\n    },\r\n\r\n    /** 安全的动态数据访问器 */\r\n    safeDynamicData() {\r\n      const safeData = { ...this.form.dynamicData };\r\n      this.dynamicFields.forEach(field => {\r\n        if (field.name) {\r\n          if (field.type === 'checkbox' && !Array.isArray(safeData[field.name])) {\r\n            safeData[field.name] = [];\r\n          } else if (field.type === 'file' && !Array.isArray(safeData[field.name])) {\r\n            safeData[field.name] = [];\r\n          }\r\n        }\r\n      });\r\n      return safeData;\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getCategoryList();\r\n    // 测试新的数据格式\r\n    this.testNewDataFormat();\r\n  },\r\n  methods: {\r\n    /** 查询需求列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDemand(this.queryParams).then(response => {\r\n        this.demandList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取需求列表失败:', error);\r\n        this.loading = false;\r\n        this.$modal.msgError(\"获取需求列表失败\");\r\n      });\r\n    },\r\n    /** 获取需求类型列表 */\r\n    getCategoryList() {\r\n      getEnabledDemandCategoryList().then(response => {\r\n        this.categoryList = response.data;\r\n      }).catch(error => {\r\n        console.error('获取需求类型列表失败:', error);\r\n        this.$modal.msgError(\"获取需求类型列表失败\");\r\n      });\r\n    },\r\n\r\n\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        demandId: null,\r\n        categoryId: null,\r\n        demandTitle: \"\",\r\n        demandDesc: \"\",\r\n        contactName: \"\",\r\n        contactPhone: \"\",\r\n        demandStatus: \"0\",\r\n        isTop: \"0\",\r\n        remark: \"\",\r\n        dynamicData: {}\r\n      };\r\n\r\n      // 清除动态字段的验证规则\r\n      Object.keys(this.rules).forEach(key => {\r\n        if (key.startsWith('dynamicData.')) {\r\n          this.$delete(this.rules, key);\r\n        }\r\n      });\r\n\r\n      // 重置动态字段\r\n      this.dynamicFields = [];\r\n      this.selectedCategoryName = '';\r\n\r\n      this.resetForm(\"form\");\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.demandId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加需求\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // 先清理状态，但不重置表单\r\n      this.dynamicFields = [];\r\n      this.selectedCategoryName = '';\r\n\r\n      const demandId = row.demandId || this.ids;\r\n      getDemand(demandId).then(response => {\r\n        // 使用$set来保持响应式\r\n        const data = response.data;\r\n        this.$set(this.form, 'demandId', data.demandId);\r\n        this.$set(this.form, 'categoryId', data.categoryId);\r\n        this.$set(this.form, 'demandTitle', data.demandTitle || \"\");\r\n        this.$set(this.form, 'demandDesc', data.demandDesc || \"\");\r\n        this.$set(this.form, 'contactName', data.contactName || \"\");\r\n        this.$set(this.form, 'contactPhone', data.contactPhone || \"\");\r\n        this.$set(this.form, 'demandStatus', data.demandStatus || \"0\");\r\n        this.$set(this.form, 'isTop', data.isTop || \"0\");\r\n        this.$set(this.form, 'remark', data.remark || \"\");\r\n\r\n        // 解析动态表单数据\r\n        if (data.formData) {\r\n          try {\r\n            const formData = JSON.parse(data.formData);\r\n\r\n            // 检查是否是新格式的数据（包含fields数组的对象）\r\n            if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {\r\n              // 新格式：先设置表单数据，再处理分类字段数据\r\n              this.$set(this.form, 'dynamicData', {});\r\n\r\n              // 从fields中提取数据到dynamicData\r\n              formData.forEach(categoryData => {\r\n                if (categoryData.fields) {\r\n                  categoryData.fields.forEach(field => {\r\n                    if (field.value !== undefined && field.value !== null && field.value !== '') {\r\n                      this.$set(this.form.dynamicData, field.name, field.value);\r\n                    }\r\n                  });\r\n                }\r\n              });\r\n\r\n              // 处理分类字段数据\r\n              this.processCategoryFieldsData(formData);\r\n            } else {\r\n              // 旧格式：直接使用formData作为dynamicData\r\n              this.$set(this.form, 'dynamicData', formData);\r\n              this.loadDynamicFields(this.form.categoryId);\r\n            }\r\n          } catch (e) {\r\n            console.error('解析动态表单数据失败:', e);\r\n            this.$set(this.form, 'dynamicData', {});\r\n            this.loadDynamicFields(this.form.categoryId);\r\n          }\r\n        } else {\r\n          this.$set(this.form, 'dynamicData', {});\r\n          this.loadDynamicFields(this.form.categoryId);\r\n        }\r\n\r\n        // 在下一个tick中清除表单验证状态\r\n        this.$nextTick(() => {\r\n          if (this.$refs.form) {\r\n            this.$refs.form.clearValidate();\r\n          }\r\n        });\r\n\r\n        this.open = true;\r\n        this.title = \"修改需求\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 先验证动态字段\r\n      let dynamicFieldsValid = true;\r\n      let firstErrorField = null;\r\n\r\n      // 验证新格式的分类字段数据\r\n      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n        this.categoryFieldsData.forEach(categoryData => {\r\n          if (categoryData.fields) {\r\n            categoryData.fields.forEach(field => {\r\n              if (field.required && field.name && field.type !== 'static') {\r\n                const value = this.form.dynamicData[field.name];\r\n                let isEmpty = false;\r\n\r\n                if (field.type === 'checkbox' || field.type === 'file') {\r\n                  isEmpty = !Array.isArray(value) || value.length === 0;\r\n                } else {\r\n                  isEmpty = value === null || value === undefined || value === '';\r\n                }\r\n\r\n                if (isEmpty) {\r\n                  dynamicFieldsValid = false;\r\n                  if (!firstErrorField) {\r\n                    firstErrorField = field.label;\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        });\r\n      } else {\r\n        // 验证旧格式的动态字段\r\n        this.dynamicFields.forEach(field => {\r\n          if (field.required && field.name) {\r\n            const value = this.form.dynamicData[field.name];\r\n            let isEmpty = false;\r\n\r\n            if (field.type === 'checkbox' || field.type === 'file') {\r\n              isEmpty = !Array.isArray(value) || value.length === 0;\r\n            } else {\r\n              isEmpty = value === null || value === undefined || value === '';\r\n            }\r\n\r\n            if (isEmpty) {\r\n              dynamicFieldsValid = false;\r\n              if (!firstErrorField) {\r\n                firstErrorField = field.label;\r\n              }\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      if (!dynamicFieldsValid) {\r\n        this.$modal.msgError(`${firstErrorField}不能为空`);\r\n        return;\r\n      }\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          const formData = { ...this.form };\r\n\r\n          // 构建包含value的完整字段数据格式\r\n          if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n            // 新格式：使用分类字段数据，并更新每个字段的value\r\n            const categoryDataWithValues = this.categoryFieldsData.map(categoryData => ({\r\n              ...categoryData,\r\n              fields: categoryData.fields.map(field => ({\r\n                ...field,\r\n                value: this.form.dynamicData[field.name] || field.value || (field.type === 'checkbox' || field.type === 'file' ? [] : '')\r\n              }))\r\n            }));\r\n            formData.formData = JSON.stringify(categoryDataWithValues);\r\n          } else if (formData.dynamicData && Object.keys(formData.dynamicData).length > 0) {\r\n            // 旧格式：直接使用dynamicData\r\n            formData.formData = JSON.stringify(formData.dynamicData);\r\n          }\r\n\r\n          delete formData.dynamicData; // 删除临时字段\r\n\r\n          console.log('submitForm - formData.formData:', formData.formData);\r\n\r\n          if (this.form.demandId != null) {\r\n            updateDemand(formData).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDemand(formData).then(() => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 查看详情 */\r\n    handleDetail(row) {\r\n      console.log('查看详情 - 原始数据:', row);\r\n\r\n      // 使用Vue.set确保响应式\r\n      this.$set(this, 'detailForm', {\r\n        ...row,\r\n        dockingLoading: true,\r\n        dockingList: [],\r\n        formDataList: []\r\n      });\r\n\r\n      // 解析表单数据\r\n      if (row.formData) {\r\n        try {\r\n          const formData = JSON.parse(row.formData);\r\n          this.$set(this.detailForm, 'formDataList', this.parseFormDataForDisplay(formData));\r\n        } catch (e) {\r\n          console.error('解析表单数据失败:', e);\r\n          this.$set(this.detailForm, 'formDataList', []);\r\n        }\r\n      }\r\n\r\n      console.log('详情表单数据:', this.detailForm);\r\n\r\n      // 打开弹窗\r\n      this.detailDialogVisible = true;\r\n\r\n      // 加载对接记录\r\n      request({\r\n        url: `/miniapp/demand/${row.demandId}/dockings`,\r\n        method: 'get'\r\n      }).then(response => {\r\n        console.log('对接记录响应:', response);\r\n        if (response.code === 200) {\r\n          this.$set(this.detailForm, 'dockingList', response.data || []);\r\n        } else {\r\n          this.$set(this.detailForm, 'dockingList', []);\r\n          console.error('获取对接记录失败:', response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取对接记录异常:', error);\r\n        this.$set(this.detailForm, 'dockingList', []);\r\n      }).finally(() => {\r\n        this.$set(this.detailForm, 'dockingLoading', false);\r\n      });\r\n    },\r\n\r\n    /** 联系记录操作 */\r\n    handleContactRecord(dockingRow) {\r\n      console.log('打开联系记录弹窗，对接记录数据:', dockingRow);\r\n      this.contactForm = {\r\n        dockingId: dockingRow.dockingId,\r\n        userName: dockingRow.userName,\r\n        userPhone: dockingRow.userPhone,\r\n        isContacted: dockingRow.isContacted || '0',\r\n        contactResult: dockingRow.contactResult || '',\r\n        contactNotes: dockingRow.contactNotes || '',\r\n        contactTime: dockingRow.contactTime || ''\r\n      };\r\n      console.log('联系表单数据:', this.contactForm);\r\n      this.contactDialogVisible = true;\r\n    },\r\n\r\n    /** 提交联系记录表单 */\r\n    submitContactForm() {\r\n      this.$refs[\"contactForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 如果选择已联系但没有设置联系时间，使用当前时间\r\n          if (this.contactForm.isContacted === '1' && !this.contactForm.contactTime) {\r\n            this.contactForm.contactTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');\r\n          }\r\n\r\n          // 保存当前联系状态，用于直接更新本地数据\r\n          const dockingId = this.contactForm.dockingId;\r\n          const newIsContacted = this.contactForm.isContacted;\r\n          const newContactResult = this.contactForm.contactResult;\r\n          const newContactNotes = this.contactForm.contactNotes;\r\n          const newContactTime = this.contactForm.contactTime;\r\n\r\n          updateContactStatus(this.contactForm).then((response) => {\r\n            console.log('联系状态更新成功:', response);\r\n            this.$modal.msgSuccess(\"联系记录更新成功\");\r\n            this.contactDialogVisible = false;\r\n\r\n            // 如果详情弹窗是打开的，先直接更新本地数据，再刷新详情中的对接记录\r\n            if (this.detailDialogVisible && this.detailForm.demandId && this.detailForm.dockingList) {\r\n              console.log('开始更新本地对接记录...');\r\n\r\n              // 先直接更新本地数据，立即反映变化\r\n              const dockingItem = this.detailForm.dockingList.find(item => item.dockingId === dockingId);\r\n              console.log('找到的对接记录:', dockingItem);\r\n              console.log('要更新的联系状态:', newIsContacted);\r\n              console.log('当前dockingList:', this.detailForm.dockingList);\r\n\r\n              if (dockingItem) {\r\n                console.log('更新前的联系状态:', dockingItem.isContacted);\r\n                this.$set(dockingItem, 'isContacted', newIsContacted);\r\n                this.$set(dockingItem, 'contactResult', newContactResult);\r\n                this.$set(dockingItem, 'contactNotes', newContactNotes);\r\n                this.$set(dockingItem, 'contactTime', newContactTime);\r\n                console.log('更新后的联系状态:', dockingItem.isContacted);\r\n\r\n                // 强制刷新表格\r\n                this.tableRefreshKey++;\r\n                console.log('强制刷新表格，新key:', this.tableRefreshKey);\r\n              } else {\r\n                console.error('未找到对应的对接记录，dockingId:', dockingId);\r\n                console.error('所有对接记录的ID:', this.detailForm.dockingList.map(item => item.dockingId));\r\n              }\r\n\r\n              // 然后再从服务器刷新完整数据\r\n              console.log('开始刷新详情中的对接记录...');\r\n              this.refreshDetailDockingList();\r\n            }\r\n\r\n            // 刷新主列表以更新统计数据\r\n            this.getList();\r\n          }).catch((error) => {\r\n            console.error('联系状态更新失败:', error);\r\n            this.$modal.msgError(\"更新失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 刷新详情中的对接记录 */\r\n    refreshDetailDockingList() {\r\n      this.$set(this.detailForm, 'dockingLoading', true);\r\n      request({\r\n        url: `/miniapp/demand/${this.detailForm.demandId}/dockings`,\r\n        method: 'get'\r\n      }).then(response => {\r\n        console.log('刷新对接记录响应:', response);\r\n        if (response.code === 200) {\r\n          this.$set(this.detailForm, 'dockingList', response.data || []);\r\n          console.log('更新后的对接记录:', this.detailForm.dockingList);\r\n          // 强制刷新表格\r\n          this.tableRefreshKey++;\r\n          console.log('服务器数据刷新后，强制刷新表格，新key:', this.tableRefreshKey);\r\n          // 调试数据\r\n          this.debugDockingData();\r\n        } else {\r\n          this.$set(this.detailForm, 'dockingList', []);\r\n          console.error('获取对接记录失败:', response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取对接记录异常:', error);\r\n        this.$set(this.detailForm, 'dockingList', []);\r\n      }).finally(() => {\r\n        this.$set(this.detailForm, 'dockingLoading', false);\r\n      });\r\n    },\r\n\r\n    /** 解析表单数据为显示格式 */\r\n    parseFormDataForDisplay(formData) {\r\n      const displayList = [];\r\n\r\n      try {\r\n        // 检查是否是新格式的数据（包含fields数组的对象）\r\n        if (Array.isArray(formData) && formData.length > 0 && formData[0].fields) {\r\n          // 新格式：遍历所有分类和字段\r\n          formData.forEach(categoryData => {\r\n            if (categoryData.fields && Array.isArray(categoryData.fields)) {\r\n              categoryData.fields.forEach(field => {\r\n                // 跳过静态展示字段\r\n                if (field.type !== 'static' && field.name && field.value !== undefined && field.value !== null && field.value !== '') {\r\n                  displayList.push({\r\n                    label: field.label || field.name,\r\n                    value: field.value,\r\n                    type: field.type || 'input'\r\n                  });\r\n                }\r\n              });\r\n            }\r\n          });\r\n        } else if (typeof formData === 'object' && formData !== null) {\r\n          // 旧格式：直接遍历对象属性\r\n          Object.keys(formData).forEach(key => {\r\n            const value = formData[key];\r\n            if (value !== undefined && value !== null && value !== '') {\r\n              displayList.push({\r\n                label: key,\r\n                value: value,\r\n                type: 'input' // 默认类型\r\n              });\r\n            }\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error('解析表单数据失败:', e);\r\n      }\r\n\r\n      return displayList;\r\n    },\r\n\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      return parts[parts.length - 1] || '未知文件';\r\n    },\r\n\r\n    /** 获取联系结果标签类型 */\r\n    getContactResultType(result) {\r\n      const typeMap = {\r\n        '联系成功': 'success',\r\n        '已有合作': 'success',\r\n        '无人接听': 'warning',\r\n        '稍后联系': 'warning',\r\n        '号码错误': 'danger',\r\n        '拒绝沟通': 'danger',\r\n        '不感兴趣': 'info',\r\n        '其他': 'info'\r\n      };\r\n      return typeMap[result] || 'info';\r\n    },\r\n\r\n    /** 调试：检查当前对接记录数据 */\r\n    debugDockingData() {\r\n      console.log('=== 调试对接记录数据 ===');\r\n      console.log('detailForm.dockingList:', this.detailForm.dockingList);\r\n      if (this.detailForm.dockingList && this.detailForm.dockingList.length > 0) {\r\n        this.detailForm.dockingList.forEach((item, index) => {\r\n          console.log(`记录${index + 1}:`, {\r\n            dockingId: item.dockingId,\r\n            userName: item.userName,\r\n            isContacted: item.isContacted,\r\n            isContactedType: typeof item.isContacted\r\n          });\r\n        });\r\n      }\r\n      console.log('tableRefreshKey:', this.tableRefreshKey);\r\n      console.log('========================');\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const demandIds = row ? [row.demandId] : this.ids;\r\n      const confirmText = row\r\n        ? `是否确认删除需求编号为\"${row.demandId}\"的数据项？`\r\n        : `是否确认删除选中的${this.ids.length}条数据项？`;\r\n\r\n      this.$modal.confirm(confirmText).then(function() {\r\n        return delDemand(demandIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/demand/export', {\r\n        ...this.queryParams\r\n      }, `需求数据_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 置顶/取消置顶 */\r\n    handleToggleTop(row) {\r\n      const text = row.isTop === \"1\" ? \"取消置顶\" : \"置顶\";\r\n      const isTop = row.isTop === \"1\" ? \"0\" : \"1\";\r\n      this.$modal.confirm('确认要\"' + text + '\"需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        const updateData = {\r\n          demandId: row.demandId,\r\n          isTop: isTop\r\n        };\r\n        return updateDemand(updateData);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 下架需求 */\r\n    handleOffShelf(row) {\r\n      this.$modal.confirm('确认要下架需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        return offShelfDemand(row.demandId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"下架成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 上架需求 */\r\n    handleOnShelf(row) {\r\n      this.$modal.confirm('确认要上架需求\"' + row.demandTitle + '\"吗？').then(function() {\r\n        return onShelfDemand(row.demandId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"上架成功\");\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 需求类型变化事件 */\r\n    onCategoryChange(categoryId) {\r\n      console.log('onCategoryChange - categoryId:', categoryId);\r\n\r\n      // 清空动态表单数据\r\n      this.form.dynamicData = {};\r\n      // 清空分类字段数据\r\n      this.categoryFieldsData = [];\r\n\r\n      if (!categoryId) {\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n        return;\r\n      }\r\n\r\n      const category = this.categoryList.find(cat => cat.categoryId === categoryId);\r\n      console.log('onCategoryChange - found category:', category);\r\n\r\n      if (category && category.formFields) {\r\n        try {\r\n          const formConfig = JSON.parse(category.formFields);\r\n          console.log('onCategoryChange - formConfig:', formConfig);\r\n\r\n          // 检查是否是新格式的数据（包含fields数组的对象）\r\n          if (Array.isArray(formConfig) && formConfig.length > 0 && formConfig[0].fields) {\r\n            // 新格式：使用分类字段数据\r\n            this.processCategoryFieldsData(formConfig);\r\n            console.log('onCategoryChange - using new format, categoryFieldsData:', this.categoryFieldsData);\r\n          } else {\r\n            // 旧格式：使用传统的动态字段加载\r\n            this.loadDynamicFields(categoryId);\r\n            console.log('onCategoryChange - using old format, dynamicFields:', this.dynamicFields);\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单配置失败:', e);\r\n          this.loadDynamicFields(categoryId);\r\n        }\r\n      } else {\r\n        console.log('onCategoryChange - no category or formFields found');\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n      }\r\n    },\r\n\r\n    /** 加载动态表单字段 */\r\n    loadDynamicFields(categoryId) {\r\n      if (!categoryId) {\r\n        this.dynamicFields = [];\r\n        this.selectedCategoryName = '';\r\n        return;\r\n      }\r\n\r\n      const category = this.categoryList.find(cat => cat.categoryId === categoryId);\r\n      if (category) {\r\n        this.selectedCategoryName = category.categoryName;\r\n\r\n        if (category.formFields) {\r\n          try {\r\n            const formConfig = JSON.parse(category.formFields);\r\n            this.dynamicFields = [];\r\n\r\n            // 检查是否是新的模块化结构\r\n            if (Array.isArray(formConfig) && formConfig.length > 0) {\r\n              if (formConfig[0].fields) {\r\n                // 新的模块化结构：提取所有模块中的字段\r\n                formConfig.forEach(module => {\r\n                  if (module.fields && Array.isArray(module.fields)) {\r\n                    module.fields.forEach(field => {\r\n                      // 跳过静态展示字段\r\n                      if (field.type !== 'static' && field.name) {\r\n                        this.dynamicFields.push({\r\n                          ...field,\r\n                          moduleTitle: module.name // 添加模块标题用于分组显示\r\n                        });\r\n                      }\r\n                    });\r\n                  }\r\n                });\r\n              } else {\r\n                // 旧的扁平结构：直接使用\r\n                this.dynamicFields = formConfig;\r\n              }\r\n            }\r\n\r\n            // 初始化动态数据对象和验证规则\r\n            this.dynamicFields.forEach(field => {\r\n              if (field.name) {\r\n                // 确保字段总是有正确的初始值\r\n                if (field.type === 'checkbox') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    Array.isArray(this.form.dynamicData[field.name]) ? this.form.dynamicData[field.name] : []);\r\n                } else if (field.type === 'file') {\r\n                  // 处理文件字段的数据转换\r\n                  const fileData = this.form.dynamicData[field.name];\r\n                  if (typeof fileData === 'string' && fileData.trim() !== '') {\r\n                    // 如果是字符串URL，转换为对象数组格式\r\n                    const fileName = fileData.split('/').pop() || '下载文件';\r\n                    this.$set(this.form.dynamicData, field.name, [{\r\n                      name: fileName,\r\n                      url: fileData\r\n                    }]);\r\n                  } else if (Array.isArray(fileData)) {\r\n                    // 如果已经是数组，保持不变\r\n                    this.$set(this.form.dynamicData, field.name, fileData);\r\n                  } else {\r\n                    // 其他情况设为空数组\r\n                    this.$set(this.form.dynamicData, field.name, []);\r\n                  }\r\n                } else if (field.type === 'number') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);\r\n                } else if (field.type === 'date' || field.type === 'time') {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : null);\r\n                } else {\r\n                  this.$set(this.form.dynamicData, field.name,\r\n                    this.form.dynamicData[field.name] !== undefined ? this.form.dynamicData[field.name] : '');\r\n                }\r\n\r\n                // 添加动态字段的验证规则\r\n                if (field.required) {\r\n                  const ruleName = `dynamicData.${field.name}`;\r\n                  this.$set(this.rules, ruleName, [\r\n                    {\r\n                      required: true,\r\n                      message: `${field.label}不能为空`,\r\n                      trigger: field.type === 'checkbox' ? 'change' : 'blur'\r\n                    }\r\n                  ]);\r\n                }\r\n              }\r\n            });\r\n          } catch (e) {\r\n            console.error('解析表单字段配置失败:', e);\r\n            this.dynamicFields = [];\r\n          }\r\n        } else {\r\n          this.dynamicFields = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 获取字段选项 */\r\n    getFieldOptions(field) {\r\n      if (!field.options) return [];\r\n      return field.options.split(',').map(option => option.trim()).filter(option => option);\r\n    },\r\n\r\n    /** 文件上传成功回调 */\r\n    handleFileSuccess(response, file, fileList, field) {\r\n      console.log('handleFileSuccess - response:', response, 'file:', file, 'field:', field.name);\r\n\r\n      if (response.code === 200) {\r\n        const fileUrl = response.url || response.fileName || response.data;\r\n\r\n        // 对于文件类型字段，value直接存储URL链接，不存储文件名或对象结构\r\n        this.handleFieldInput(field, fileUrl);\r\n\r\n        console.log('handleFileSuccess - 文件上传成功，设置URL:', fileUrl);\r\n        console.log('handleFileSuccess - field.value after update:', field.value);\r\n      } else {\r\n        this.$modal.msgError(response.msg || '文件上传失败');\r\n      }\r\n    },\r\n\r\n    /** 文件删除回调 */\r\n    handleFileRemove(file, fileList, field) {\r\n      // 文件删除时，直接清空value字段\r\n      this.handleFieldInput(field, '');\r\n      console.log('handleFileRemove - 文件已删除，清空字段值');\r\n    },\r\n\r\n    /** 获取多选框的安全值 */\r\n    getCheckboxValue(fieldName) {\r\n      const value = this.form.dynamicData[fieldName];\r\n      return Array.isArray(value) ? value : [];\r\n    },\r\n\r\n    /** 更新多选框的值 */\r\n    updateCheckboxValue(fieldName, value) {\r\n      this.$set(this.form.dynamicData, fieldName, Array.isArray(value) ? value : []);\r\n    },\r\n\r\n    /** 获取文件列表（用于el-upload组件） */\r\n    getFileList(field) {\r\n      const files = field.value;\r\n      console.log('getFileList - field:', field.name, 'value:', files);\r\n\r\n      // 如果是字符串URL且不为空，转换为文件列表格式显示在upload组件中\r\n      if (typeof files === 'string' && files.trim() !== '') {\r\n        return [{\r\n          name: this.getFileNameFromUrl(files),\r\n          url: files,\r\n          uid: `${field.name}-0`,\r\n          status: 'success'\r\n        }];\r\n      }\r\n\r\n      // 如果是数组格式（兼容旧数据）\r\n      if (Array.isArray(files)) {\r\n        return files.map((file, index) => ({\r\n          name: file.name || this.getFileNameFromUrl(file.url || file),\r\n          url: file.url || file,\r\n          uid: `${field.name}-${index}`,\r\n          status: 'success'\r\n        }));\r\n      }\r\n\r\n      // 其他情况返回空数组\r\n      console.log('getFileList - 无有效文件数据，返回空数组');\r\n      return [];\r\n    },\r\n\r\n    /** 获取已上传的文件列表（用于显示） */\r\n    getUploadedFiles(field) {\r\n      const files = field.value;\r\n      return Array.isArray(files) ? files : [];\r\n    },\r\n\r\n    /** 文件预览 */\r\n    handleFilePreview(file) {\r\n      if (file.url) {\r\n        window.open(file.url, '_blank');\r\n      }\r\n    },\r\n\r\n    /** 下载文件 */\r\n    downloadFile(url, fileName) {\r\n      // 创建一个临时的a标签来触发下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    /** 删除已上传的文件 */\r\n    removeUploadedFile(field, index) {\r\n      if (field.value && Array.isArray(field.value)) {\r\n        const newValue = [...field.value];\r\n        newValue.splice(index, 1);\r\n        this.handleFieldInput(field, newValue);\r\n      }\r\n    },\r\n\r\n    /** 删除文件URL */\r\n    removeFileUrl(field) {\r\n      this.handleFieldInput(field, '');\r\n    },\r\n\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      const fileName = parts[parts.length - 1];\r\n      // 如果文件名包含时间戳等，尝试提取原始文件名\r\n      const match = fileName.match(/.*_\\d+A\\d+\\.(.*)/);\r\n      if (match) {\r\n        return `文件.${match[1]}`;\r\n      }\r\n      return fileName || '未知文件';\r\n    },\r\n\r\n    /** 处理字段输入 */\r\n    handleFieldInput(field, value) {\r\n      // 更新字段的value\r\n      field.value = value;\r\n      // 同步到表单数据\r\n      this.$set(this.form.dynamicData, field.name, value);\r\n      console.log('handleFieldInput - field:', field.name, 'value:', value);\r\n    },\r\n\r\n    /** 更新字段值到表单数据 */\r\n    updateFieldValue(field) {\r\n      this.$set(this.form.dynamicData, field.name, field.value);\r\n    },\r\n\r\n    /** 获取分类名称 */\r\n    getCategoryName() {\r\n      if (this.categoryFieldsData && this.categoryFieldsData.length > 0) {\r\n        return this.categoryFieldsData[0].name || '专属字段';\r\n      }\r\n      return this.selectedCategoryName || '专属字段';\r\n    },\r\n\r\n    /** 处理分类字段数据 */\r\n    processCategoryFieldsData(data) {\r\n      if (typeof data === 'string') {\r\n        try {\r\n          this.categoryFieldsData = JSON.parse(data);\r\n        } catch (e) {\r\n          console.error('解析分类字段数据失败:', e);\r\n          this.categoryFieldsData = [];\r\n        }\r\n      } else if (Array.isArray(data)) {\r\n        this.categoryFieldsData = data;\r\n      } else {\r\n        this.categoryFieldsData = [];\r\n      }\r\n\r\n      // 初始化字段值到表单数据\r\n      this.categoryFieldsData.forEach(categoryData => {\r\n        if (categoryData.fields) {\r\n          categoryData.fields.forEach(field => {\r\n            // 确保字段有初始值\r\n            if (field.value === undefined || field.value === null) {\r\n              if (field.type === 'file') {\r\n                field.value = [];\r\n              } else if (field.type === 'checkbox') {\r\n                field.value = [];\r\n              } else {\r\n                field.value = '';\r\n              }\r\n            }\r\n\r\n            // 从表单数据中恢复字段值（如果存在）\r\n            if (this.form.dynamicData && this.form.dynamicData[field.name] !== undefined) {\r\n              field.value = this.form.dynamicData[field.name];\r\n            } else {\r\n              // 设置到表单数据\r\n              this.$set(this.form.dynamicData, field.name, field.value);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 测试新的数据格式 */\r\n    testNewDataFormat() {\r\n      // 使用您提供的实际JSON数据格式进行测试\r\n      const testData = [\r\n        {\r\n          \"name\": \"基础信息\",\r\n          \"description\": \"\",\r\n          \"fields\": [\r\n            {\r\n              \"label\": \"企业全称\",\r\n              \"name\": \"field_652408\",\r\n              \"type\": \"input\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"测试企业有限公司\"\r\n            },\r\n            {\r\n              \"label\": \"行业标签\",\r\n              \"name\": \"field_720944\",\r\n              \"type\": \"select\",\r\n              \"required\": true,\r\n              \"options\": \"新能源,硬科技\",\r\n              \"placeholder\": \"请选择\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"新能源\"\r\n            },\r\n            {\r\n              \"label\": \"联系人\",\r\n              \"name\": \"contact_name\",\r\n              \"type\": \"input\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"张三\"\r\n            },\r\n            {\r\n              \"label\": \"电话\",\r\n              \"name\": \"phone\",\r\n              \"type\": \"tel\",\r\n              \"required\": true,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"13800138000\"\r\n            }\r\n          ],\r\n          \"icon\": \"http://************:8080/profile/upload/2025/07/22/20250722-100228_20250722100403A004.png\"\r\n        },\r\n        {\r\n          \"name\": \"其他材料补充\",\r\n          \"description\": \"\",\r\n          \"fields\": [\r\n            {\r\n              \"label\": \"上传附件\",\r\n              \"name\": \"field_989222\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"未选择任何文件\",\r\n              \"staticContent\": \"\",\r\n              \"value\": \"http://************:8080/profile/upload/2025/07/23/xhuFwa0qulPS03911c35329f695848fb659a24f6f159_20250723183220A001.png\"\r\n            },\r\n            {\r\n              \"label\": \"邮件提交至\",\r\n              \"name\": \"field_227969\",\r\n              \"type\": \"static\",\r\n              \"required\": false,\r\n              \"options\": \"\",\r\n              \"placeholder\": \"请输入\",\r\n              \"staticContent\": \"<EMAIL>(文件名：【企业曝光申请】-企业/项目名）\",\r\n              \"value\": \"\"\r\n            }\r\n          ],\r\n          \"icon\": \"http://************:8080/profile/upload/2025/07/22/20250722-100247_20250722100459A009.png\"\r\n        }\r\n      ];\r\n\r\n      // 当点击修改按钮时，可以调用这个方法来设置测试数据\r\n      // this.processCategoryFieldsData(testData);\r\n    },\r\n\r\n\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n/* 详情弹窗样式 - 参考简洁布局 */\r\n.detail-content {\r\n  padding: 0;\r\n  background-color: #f5f5f5;\r\n  min-height: 400px;\r\n}\r\n\r\n.info-section {\r\n  background-color: white;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  background-color: #f8f9fa;\r\n  padding: 12px 20px;\r\n  margin: 0;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  color: #666;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  min-width: 100px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n  margin-right: 20px;\r\n}\r\n\r\n.info-value {\r\n  color: #333;\r\n  font-size: 14px;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.description-text {\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.docking-section {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.docking-list {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.docking-item {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.docking-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-main {\r\n  flex: 1;\r\n}\r\n\r\n.item-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  gap: 10px;\r\n}\r\n\r\n.user-name {\r\n  font-weight: 500;\r\n  color: #333;\r\n  font-size: 14px;\r\n}\r\n\r\n.user-phone {\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.item-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #666;\r\n  font-size: 13px;\r\n}\r\n\r\n.detail-item i {\r\n  margin-right: 4px;\r\n  color: #999;\r\n}\r\n\r\n.item-notes {\r\n  color: #666;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.item-notes i {\r\n  margin-right: 4px;\r\n  margin-top: 2px;\r\n  color: #999;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.item-actions {\r\n  margin-left: 15px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.no-data-simple {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.dynamic-fields-section {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border: 1px solid #e6e6e6;\r\n}\r\n\r\n.module-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 4px solid #409eff;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.module-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 分类组样式 */\r\n.category-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 4px solid #409eff;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.category-title i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.category-description {\r\n  margin-bottom: 15px;\r\n  padding: 8px 12px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 动态字段垂直布局样式 */\r\n.dynamic-field-item {\r\n  margin-bottom: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.dynamic-field-item:last-child {\r\n  margin-bottom: 20px; /* 保持底部间距，避免与下方元素重合 */\r\n}\r\n\r\n/* 字段标签样式 */\r\n.dynamic-field-item .field-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 必填字段标识 */\r\n.dynamic-field-item .required-mark {\r\n  color: #f56c6c;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 字段内容区域 */\r\n.dynamic-field-item .field-content {\r\n  width: 100%;\r\n}\r\n\r\n/* 表单控件样式 */\r\n.dynamic-field-item .field-content .el-input,\r\n.dynamic-field-item .field-content .el-textarea,\r\n.dynamic-field-item .field-content .el-select,\r\n.dynamic-field-item .field-content .el-input-number,\r\n.dynamic-field-item .field-content .el-date-editor,\r\n.dynamic-field-item .field-content .el-time-picker {\r\n  width: 100%;\r\n}\r\n\r\n/* 单选框和多选框布局 */\r\n.dynamic-field-item .field-content .el-radio-group,\r\n.dynamic-field-item .field-content .el-checkbox-group {\r\n  width: 100%;\r\n  line-height: 1.8;\r\n}\r\n\r\n.dynamic-field-item .field-content .el-radio,\r\n.dynamic-field-item .field-content .el-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 文件上传组件 */\r\n.dynamic-field-item .field-content .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.el-divider {\r\n  margin: 10px 0 20px 0;\r\n}\r\n\r\n/* 响应式布局优化 */\r\n@media (max-width: 768px) {\r\n  .dynamic-field-item .el-form-item__label {\r\n    width: 100px !important;\r\n    text-align: left;\r\n  }\r\n\r\n  .dynamic-field-item .el-radio,\r\n  .dynamic-field-item .el-checkbox {\r\n    display: block;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 上传组件样式优化 */\r\n.el-upload__tip {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.dynamic-field-item .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.dynamic-field-item .el-upload-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 文件上传相关样式 */\r\n.uploaded-files-list {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 4px;\r\n}\r\n\r\n.uploaded-files-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.uploaded-file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.uploaded-file-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.uploaded-file-item i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-link {\r\n  flex: 1;\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-link:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.remove-file-btn {\r\n  margin-left: 10px;\r\n  color: #f56c6c;\r\n}\r\n\r\n.remove-file-btn:hover {\r\n  color: #f78989;\r\n}\r\n\r\n/* 静态内容样式 */\r\n.static-content {\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n/* 已存在文件显示样式 */\r\n.existing-file {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-display {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 4px;\r\n  gap: 8px;\r\n}\r\n\r\n.file-display .el-icon-document {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-display .file-link {\r\n  flex: 1;\r\n  color: #409eff;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-display .file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.file-display .remove-file-btn {\r\n  color: #f56c6c;\r\n  padding: 0;\r\n}\r\n\r\n/* 动态字段整体布局优化 */\r\n.dynamic-fields-section .el-row {\r\n  margin-left: -10px;\r\n  margin-right: -10px;\r\n}\r\n\r\n.dynamic-fields-section .el-col {\r\n  padding-left: 10px;\r\n  padding-right: 10px;\r\n}\r\n\r\n/* 优化表单验证错误提示的显示 */\r\n.dynamic-field-item .el-form-item__error {\r\n  position: static;\r\n  margin-top: 2px;\r\n  padding-top: 2px;\r\n}\r\n\r\n/* 对接情况显示样式 */\r\n.docking-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.contact-stats {\r\n  display: flex;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.contacted {\r\n  color: #67c23a;\r\n}\r\n\r\n.uncontacted {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 展开内容样式 */\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.expand-content h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.no-docking {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n/* 对接详情表格样式 */\r\n.expand-content .el-table {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.expand-content .el-table th {\r\n  background-color: #fafafa;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAogCA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,oBAAA;MACA;MACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,aAAA;QACAC,YAAA;QACAC,WAAA;MACA;MACA;MACAC,YAAA;QACAJ,WAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,mBAAA;MACA;MACAC,UAAA;QACAC,WAAA;QACAC,YAAA;MACA;MACA;MACAC,eAAA;MACA;MACAC,YAAA;MACA;MACAC,aAAA;MACA;MACAC,oBAAA;MACA;MACAC,kBAAA;MACA;MACAC,aAAA;QACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;MACA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;QACAC,YAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAL,UAAA,GACA;UAAAvB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAoB,WAAA,GACA;UAAAtB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA2B,UAAA,GACA;UAAA7B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA4B,WAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6B,YAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA8B,OAAA;UAAA/B,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsB,YAAA,GACA;UAAAxB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACA+B,QAAA;IACA,iBACAC,oBAAA,WAAAA,qBAAA;MACA,IAAAC,OAAA;MACA,KAAA1B,aAAA,CAAA2B,OAAA,WAAAC,KAAA;QACA,IAAAC,WAAA,GAAAD,KAAA,CAAAC,WAAA;QACA,KAAAH,OAAA,CAAAG,WAAA;UACAH,OAAA,CAAAG,WAAA;QACA;QACAH,OAAA,CAAAG,WAAA,EAAAC,IAAA,CAAAF,KAAA;MACA;MACA,OAAAF,OAAA;IACA;IAEA,iBACAK,eAAA,WAAAA,gBAAA;MACA,IAAAC,QAAA,OAAAC,cAAA,CAAAC,OAAA,WAAAhB,IAAA,CAAAiB,WAAA;MACA,KAAAnC,aAAA,CAAA2B,OAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,CAAAzD,IAAA;UACA,IAAAyD,KAAA,CAAAQ,IAAA,oBAAAC,KAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAJ,KAAA,CAAAzD,IAAA;YACA6D,QAAA,CAAAJ,KAAA,CAAAzD,IAAA;UACA,WAAAyD,KAAA,CAAAQ,IAAA,gBAAAC,KAAA,CAAAC,OAAA,CAAAN,QAAA,CAAAJ,KAAA,CAAAzD,IAAA;YACA6D,QAAA,CAAAJ,KAAA,CAAAzD,IAAA;UACA;QACA;MACA;MACA,OAAA6D,QAAA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,eAAA;IACA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA,MAAAC,gBAAA,CAAAV,OAAA,MAAAU,gBAAA,CAAAV,OAAA,MAAAU,gBAAA,CAAAV,OAAA,MAAAU,gBAAA,CAAAV,OAAA,MAAAU,gBAAA,CAAAV,OAAA,MAAAU,gBAAA,CAAAV,OAAA;IACA,aACAM,OAAA,WAAAA,QAAA;MAAA,IAAAK,KAAA;MACA,KAAAvE,OAAA;MACA,IAAAwE,kBAAA,OAAApC,WAAA,EAAAqC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAjE,UAAA,GAAAoE,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAlE,KAAA,GAAAqE,QAAA,CAAArE,KAAA;QACAkE,KAAA,CAAAvE,OAAA;MACA,GAAA4E,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAN,KAAA,CAAAvE,OAAA;QACAuE,KAAA,CAAAQ,MAAA,CAAAC,QAAA;MACA;IACA;IACA,eACAb,eAAA,WAAAA,gBAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,4CAAA,IAAAT,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAAxD,YAAA,GAAAiD,QAAA,CAAA3E,IAAA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;QACAI,MAAA,CAAAF,MAAA,CAAAC,QAAA;MACA;IACA;IAGAG,MAAA,WAAAA,OAAA;MACA,KAAAhD,IAAA;MACA,KAAAiD,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,IAAA;QACA0C,QAAA;QACA9C,UAAA;QACAD,WAAA;QACAO,UAAA;QACAC,WAAA;QACAC,YAAA;QACAP,YAAA;QACA8C,KAAA;QACAC,MAAA;QACA3B,WAAA;MACA;;MAEA;MACA4B,MAAA,CAAAC,IAAA,MAAA7C,KAAA,EAAAQ,OAAA,WAAAsC,GAAA;QACA,IAAAA,GAAA,CAAAC,UAAA;UACAP,MAAA,CAAAQ,OAAA,CAAAR,MAAA,CAAAxC,KAAA,EAAA8C,GAAA;QACA;MACA;;MAEA;MACA,KAAAjE,aAAA;MACA,KAAAC,oBAAA;MAEA,KAAAmE,SAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA3D,WAAA,CAAAC,OAAA;MACA,KAAA6B,OAAA;IACA;IACA8B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjG,GAAA,GAAAiG,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAd,QAAA;MAAA;MACA,KAAApF,MAAA,GAAAgG,SAAA,CAAAG,MAAA;MACA,KAAAlG,QAAA,IAAA+F,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAlB,KAAA;MACA,KAAAjD,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAqE,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA/E,aAAA;MACA,KAAAC,oBAAA;MAEA,IAAA2D,QAAA,GAAAkB,GAAA,CAAAlB,QAAA,SAAArF,GAAA;MACA,IAAAyG,iBAAA,EAAApB,QAAA,EAAAb,IAAA,WAAAC,QAAA;QACA;QACA,IAAA3E,IAAA,GAAA2E,QAAA,CAAA3E,IAAA;QACA0G,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,cAAA7C,IAAA,CAAAuF,QAAA;QACAmB,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,gBAAA7C,IAAA,CAAAyC,UAAA;QACAiE,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,iBAAA7C,IAAA,CAAAwC,WAAA;QACAkE,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,gBAAA7C,IAAA,CAAA+C,UAAA;QACA2D,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,iBAAA7C,IAAA,CAAAgD,WAAA;QACA0D,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,kBAAA7C,IAAA,CAAAiD,YAAA;QACAyD,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,kBAAA7C,IAAA,CAAA0C,YAAA;QACAgE,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,WAAA7C,IAAA,CAAAwF,KAAA;QACAkB,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,YAAA7C,IAAA,CAAAyF,MAAA;;QAEA;QACA,IAAAzF,IAAA,CAAA6G,QAAA;UACA;YACA,IAAAA,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAA/G,IAAA,CAAA6G,QAAA;;YAEA;YACA,IAAA7C,KAAA,CAAAC,OAAA,CAAA4C,QAAA,KAAAA,QAAA,CAAAP,MAAA,QAAAO,QAAA,IAAAG,MAAA;cACA;cACAN,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA;;cAEA;cACAgE,QAAA,CAAAvD,OAAA,WAAA2D,YAAA;gBACA,IAAAA,YAAA,CAAAD,MAAA;kBACAC,YAAA,CAAAD,MAAA,CAAA1D,OAAA,WAAAC,KAAA;oBACA,IAAAA,KAAA,CAAA2D,KAAA,KAAAC,SAAA,IAAA5D,KAAA,CAAA2D,KAAA,aAAA3D,KAAA,CAAA2D,KAAA;sBACAR,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EAAAyD,KAAA,CAAA2D,KAAA;oBACA;kBACA;gBACA;cACA;;cAEA;cACAR,MAAA,CAAAU,yBAAA,CAAAP,QAAA;YACA;cACA;cACAH,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA,iBAAAgE,QAAA;cACAH,MAAA,CAAAW,iBAAA,CAAAX,MAAA,CAAA7D,IAAA,CAAAJ,UAAA;YACA;UACA,SAAA6E,CAAA;YACAvC,OAAA,CAAAD,KAAA,gBAAAwC,CAAA;YACAZ,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA;YACA6D,MAAA,CAAAW,iBAAA,CAAAX,MAAA,CAAA7D,IAAA,CAAAJ,UAAA;UACA;QACA;UACAiE,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAA7D,IAAA;UACA6D,MAAA,CAAAW,iBAAA,CAAAX,MAAA,CAAA7D,IAAA,CAAAJ,UAAA;QACA;;QAEA;QACAiE,MAAA,CAAAa,SAAA;UACA,IAAAb,MAAA,CAAAc,KAAA,CAAA3E,IAAA;YACA6D,MAAA,CAAAc,KAAA,CAAA3E,IAAA,CAAA4E,aAAA;UACA;QACA;QAEAf,MAAA,CAAAtE,IAAA;QACAsE,MAAA,CAAAvE,KAAA;MACA;IACA;IACA,WACAuF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,kBAAA;MACA,IAAAC,eAAA;;MAEA;MACA,SAAAhG,kBAAA,SAAAA,kBAAA,CAAAyE,MAAA;QACA,KAAAzE,kBAAA,CAAAyB,OAAA,WAAA2D,YAAA;UACA,IAAAA,YAAA,CAAAD,MAAA;YACAC,YAAA,CAAAD,MAAA,CAAA1D,OAAA,WAAAC,KAAA;cACA,IAAAA,KAAA,CAAArC,QAAA,IAAAqC,KAAA,CAAAzD,IAAA,IAAAyD,KAAA,CAAAQ,IAAA;gBACA,IAAAmD,KAAA,GAAAS,MAAA,CAAA9E,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;gBACA,IAAAgI,OAAA;gBAEA,IAAAvE,KAAA,CAAAQ,IAAA,mBAAAR,KAAA,CAAAQ,IAAA;kBACA+D,OAAA,IAAA9D,KAAA,CAAAC,OAAA,CAAAiD,KAAA,KAAAA,KAAA,CAAAZ,MAAA;gBACA;kBACAwB,OAAA,GAAAZ,KAAA,aAAAA,KAAA,KAAAC,SAAA,IAAAD,KAAA;gBACA;gBAEA,IAAAY,OAAA;kBACAF,kBAAA;kBACA,KAAAC,eAAA;oBACAA,eAAA,GAAAtE,KAAA,CAAAwE,KAAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;QACA;QACA,KAAApG,aAAA,CAAA2B,OAAA,WAAAC,KAAA;UACA,IAAAA,KAAA,CAAArC,QAAA,IAAAqC,KAAA,CAAAzD,IAAA;YACA,IAAAoH,KAAA,GAAAS,MAAA,CAAA9E,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;YACA,IAAAgI,OAAA;YAEA,IAAAvE,KAAA,CAAAQ,IAAA,mBAAAR,KAAA,CAAAQ,IAAA;cACA+D,OAAA,IAAA9D,KAAA,CAAAC,OAAA,CAAAiD,KAAA,KAAAA,KAAA,CAAAZ,MAAA;YACA;cACAwB,OAAA,GAAAZ,KAAA,aAAAA,KAAA,KAAAC,SAAA,IAAAD,KAAA;YACA;YAEA,IAAAY,OAAA;cACAF,kBAAA;cACA,KAAAC,eAAA;gBACAA,eAAA,GAAAtE,KAAA,CAAAwE,KAAA;cACA;YACA;UACA;QACA;MACA;MAEA,KAAAH,kBAAA;QACA,KAAA5C,MAAA,CAAAC,QAAA,IAAA+C,MAAA,CAAAH,eAAA;QACA;MACA;MAEA,KAAAL,KAAA,SAAAS,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAArB,QAAA,OAAAjD,cAAA,CAAAC,OAAA,MAAA8D,MAAA,CAAA9E,IAAA;;UAEA;UACA,IAAA8E,MAAA,CAAA9F,kBAAA,IAAA8F,MAAA,CAAA9F,kBAAA,CAAAyE,MAAA;YACA;YACA,IAAA6B,sBAAA,GAAAR,MAAA,CAAA9F,kBAAA,CAAAuE,GAAA,WAAAa,YAAA;cAAA,WAAArD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAoD,YAAA;gBACAD,MAAA,EAAAC,YAAA,CAAAD,MAAA,CAAAZ,GAAA,WAAA7C,KAAA;kBAAA,WAAAK,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAN,KAAA;oBACA2D,KAAA,EAAAS,MAAA,CAAA9E,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA,KAAAyD,KAAA,CAAA2D,KAAA,KAAA3D,KAAA,CAAAQ,IAAA,mBAAAR,KAAA,CAAAQ,IAAA;kBAAA;gBAAA,CACA;cAAA;YAAA,CACA;YACA8C,QAAA,CAAAA,QAAA,GAAAC,IAAA,CAAAsB,SAAA,CAAAD,sBAAA;UACA,WAAAtB,QAAA,CAAA/C,WAAA,IAAA4B,MAAA,CAAAC,IAAA,CAAAkB,QAAA,CAAA/C,WAAA,EAAAwC,MAAA;YACA;YACAO,QAAA,CAAAA,QAAA,GAAAC,IAAA,CAAAsB,SAAA,CAAAvB,QAAA,CAAA/C,WAAA;UACA;UAEA,OAAA+C,QAAA,CAAA/C,WAAA;;UAEAiB,OAAA,CAAAsD,GAAA,oCAAAxB,QAAA,CAAAA,QAAA;UAEA,IAAAc,MAAA,CAAA9E,IAAA,CAAA0C,QAAA;YACA,IAAA+C,oBAAA,EAAAzB,QAAA,EAAAnC,IAAA;cACAiD,MAAA,CAAA3C,MAAA,CAAAuD,UAAA;cACAZ,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAAxD,OAAA;YACA;UACA;YACA,IAAAqE,iBAAA,EAAA3B,QAAA,EAAAnC,IAAA;cACAiD,MAAA,CAAA3C,MAAA,CAAAuD,UAAA;cACAZ,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAAxD,OAAA;YACA;UACA;QACA;MACA;IACA;IAEA,WACAsE,YAAA,WAAAA,aAAAhC,GAAA;MAAA,IAAAiC,MAAA;MACA3D,OAAA,CAAAsD,GAAA,iBAAA5B,GAAA;;MAEA;MACA,KAAAG,IAAA,yBAAAhD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA4C,GAAA;QACAkC,cAAA;QACApH,WAAA;QACAC,YAAA;MAAA,EACA;;MAEA;MACA,IAAAiF,GAAA,CAAAI,QAAA;QACA;UACA,IAAAA,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAN,GAAA,CAAAI,QAAA;UACA,KAAAD,IAAA,MAAAtF,UAAA,uBAAAsH,uBAAA,CAAA/B,QAAA;QACA,SAAAS,CAAA;UACAvC,OAAA,CAAAD,KAAA,cAAAwC,CAAA;UACA,KAAAV,IAAA,MAAAtF,UAAA;QACA;MACA;MAEAyD,OAAA,CAAAsD,GAAA,iBAAA/G,UAAA;;MAEA;MACA,KAAAD,mBAAA;;MAEA;MACA,IAAAwH,gBAAA;QACAC,GAAA,qBAAAd,MAAA,CAAAvB,GAAA,CAAAlB,QAAA;QACAwD,MAAA;MACA,GAAArE,IAAA,WAAAC,QAAA;QACAI,OAAA,CAAAsD,GAAA,YAAA1D,QAAA;QACA,IAAAA,QAAA,CAAAqE,IAAA;UACAN,MAAA,CAAA9B,IAAA,CAAA8B,MAAA,CAAApH,UAAA,iBAAAqD,QAAA,CAAA3E,IAAA;QACA;UACA0I,MAAA,CAAA9B,IAAA,CAAA8B,MAAA,CAAApH,UAAA;UACAyD,OAAA,CAAAD,KAAA,cAAAH,QAAA,CAAAsE,GAAA;QACA;MACA,GAAApE,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA4D,MAAA,CAAA9B,IAAA,CAAA8B,MAAA,CAAApH,UAAA;MACA,GAAA4H,OAAA;QACAR,MAAA,CAAA9B,IAAA,CAAA8B,MAAA,CAAApH,UAAA;MACA;IACA;IAEA,aACA6H,mBAAA,WAAAA,oBAAAC,UAAA;MACArE,OAAA,CAAAsD,GAAA,qBAAAe,UAAA;MACA,KAAA3I,WAAA;QACAC,SAAA,EAAA0I,UAAA,CAAA1I,SAAA;QACAC,QAAA,EAAAyI,UAAA,CAAAzI,QAAA;QACAC,SAAA,EAAAwI,UAAA,CAAAxI,SAAA;QACAC,WAAA,EAAAuI,UAAA,CAAAvI,WAAA;QACAC,aAAA,EAAAsI,UAAA,CAAAtI,aAAA;QACAC,YAAA,EAAAqI,UAAA,CAAArI,YAAA;QACAC,WAAA,EAAAoI,UAAA,CAAApI,WAAA;MACA;MACA+D,OAAA,CAAAsD,GAAA,iBAAA5H,WAAA;MACA,KAAAD,oBAAA;IACA;IAEA,eACA6I,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAA9B,KAAA,gBAAAS,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAoB,MAAA,CAAA7I,WAAA,CAAAI,WAAA,aAAAyI,MAAA,CAAA7I,WAAA,CAAAO,WAAA;YACAsI,MAAA,CAAA7I,WAAA,CAAAO,WAAA,GAAAsI,MAAA,CAAAC,SAAA,KAAAC,IAAA;UACA;;UAEA;UACA,IAAA9I,SAAA,GAAA4I,MAAA,CAAA7I,WAAA,CAAAC,SAAA;UACA,IAAA+I,cAAA,GAAAH,MAAA,CAAA7I,WAAA,CAAAI,WAAA;UACA,IAAA6I,gBAAA,GAAAJ,MAAA,CAAA7I,WAAA,CAAAK,aAAA;UACA,IAAA6I,eAAA,GAAAL,MAAA,CAAA7I,WAAA,CAAAM,YAAA;UACA,IAAA6I,cAAA,GAAAN,MAAA,CAAA7I,WAAA,CAAAO,WAAA;UAEA,IAAA6I,2BAAA,EAAAP,MAAA,CAAA7I,WAAA,EAAAiE,IAAA,WAAAC,QAAA;YACAI,OAAA,CAAAsD,GAAA,cAAA1D,QAAA;YACA2E,MAAA,CAAAtE,MAAA,CAAAuD,UAAA;YACAe,MAAA,CAAA9I,oBAAA;;YAEA;YACA,IAAA8I,MAAA,CAAAjI,mBAAA,IAAAiI,MAAA,CAAAhI,UAAA,CAAAiE,QAAA,IAAA+D,MAAA,CAAAhI,UAAA,CAAAC,WAAA;cACAwD,OAAA,CAAAsD,GAAA;;cAEA;cACA,IAAAyB,WAAA,GAAAR,MAAA,CAAAhI,UAAA,CAAAC,WAAA,CAAAwI,IAAA,WAAA1D,IAAA;gBAAA,OAAAA,IAAA,CAAA3F,SAAA,KAAAA,SAAA;cAAA;cACAqE,OAAA,CAAAsD,GAAA,aAAAyB,WAAA;cACA/E,OAAA,CAAAsD,GAAA,cAAAoB,cAAA;cACA1E,OAAA,CAAAsD,GAAA,mBAAAiB,MAAA,CAAAhI,UAAA,CAAAC,WAAA;cAEA,IAAAuI,WAAA;gBACA/E,OAAA,CAAAsD,GAAA,cAAAyB,WAAA,CAAAjJ,WAAA;gBACAyI,MAAA,CAAA1C,IAAA,CAAAkD,WAAA,iBAAAL,cAAA;gBACAH,MAAA,CAAA1C,IAAA,CAAAkD,WAAA,mBAAAJ,gBAAA;gBACAJ,MAAA,CAAA1C,IAAA,CAAAkD,WAAA,kBAAAH,eAAA;gBACAL,MAAA,CAAA1C,IAAA,CAAAkD,WAAA,iBAAAF,cAAA;gBACA7E,OAAA,CAAAsD,GAAA,cAAAyB,WAAA,CAAAjJ,WAAA;;gBAEA;gBACAyI,MAAA,CAAA7H,eAAA;gBACAsD,OAAA,CAAAsD,GAAA,iBAAAiB,MAAA,CAAA7H,eAAA;cACA;gBACAsD,OAAA,CAAAD,KAAA,0BAAApE,SAAA;gBACAqE,OAAA,CAAAD,KAAA,eAAAwE,MAAA,CAAAhI,UAAA,CAAAC,WAAA,CAAA6E,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAA3F,SAAA;gBAAA;cACA;;cAEA;cACAqE,OAAA,CAAAsD,GAAA;cACAiB,MAAA,CAAAU,wBAAA;YACA;;YAEA;YACAV,MAAA,CAAAnF,OAAA;UACA,GAAAU,KAAA,WAAAC,KAAA;YACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;YACAwE,MAAA,CAAAtE,MAAA,CAAAC,QAAA;UACA;QACA;MACA;IACA;IAEA,iBACA+E,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA,KAAArD,IAAA,MAAAtF,UAAA;MACA,IAAAuH,gBAAA;QACAC,GAAA,qBAAAd,MAAA,MAAA1G,UAAA,CAAAiE,QAAA;QACAwD,MAAA;MACA,GAAArE,IAAA,WAAAC,QAAA;QACAI,OAAA,CAAAsD,GAAA,cAAA1D,QAAA;QACA,IAAAA,QAAA,CAAAqE,IAAA;UACAiB,MAAA,CAAArD,IAAA,CAAAqD,MAAA,CAAA3I,UAAA,iBAAAqD,QAAA,CAAA3E,IAAA;UACA+E,OAAA,CAAAsD,GAAA,cAAA4B,MAAA,CAAA3I,UAAA,CAAAC,WAAA;UACA;UACA0I,MAAA,CAAAxI,eAAA;UACAsD,OAAA,CAAAsD,GAAA,0BAAA4B,MAAA,CAAAxI,eAAA;UACA;UACAwI,MAAA,CAAAC,gBAAA;QACA;UACAD,MAAA,CAAArD,IAAA,CAAAqD,MAAA,CAAA3I,UAAA;UACAyD,OAAA,CAAAD,KAAA,cAAAH,QAAA,CAAAsE,GAAA;QACA;MACA,GAAApE,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAmF,MAAA,CAAArD,IAAA,CAAAqD,MAAA,CAAA3I,UAAA;MACA,GAAA4H,OAAA;QACAe,MAAA,CAAArD,IAAA,CAAAqD,MAAA,CAAA3I,UAAA;MACA;IACA;IAEA,kBACAsH,uBAAA,WAAAA,wBAAA/B,QAAA;MACA,IAAAsD,WAAA;MAEA;QACA;QACA,IAAAnG,KAAA,CAAAC,OAAA,CAAA4C,QAAA,KAAAA,QAAA,CAAAP,MAAA,QAAAO,QAAA,IAAAG,MAAA;UACA;UACAH,QAAA,CAAAvD,OAAA,WAAA2D,YAAA;YACA,IAAAA,YAAA,CAAAD,MAAA,IAAAhD,KAAA,CAAAC,OAAA,CAAAgD,YAAA,CAAAD,MAAA;cACAC,YAAA,CAAAD,MAAA,CAAA1D,OAAA,WAAAC,KAAA;gBACA;gBACA,IAAAA,KAAA,CAAAQ,IAAA,iBAAAR,KAAA,CAAAzD,IAAA,IAAAyD,KAAA,CAAA2D,KAAA,KAAAC,SAAA,IAAA5D,KAAA,CAAA2D,KAAA,aAAA3D,KAAA,CAAA2D,KAAA;kBACAiD,WAAA,CAAA1G,IAAA;oBACAsE,KAAA,EAAAxE,KAAA,CAAAwE,KAAA,IAAAxE,KAAA,CAAAzD,IAAA;oBACAoH,KAAA,EAAA3D,KAAA,CAAA2D,KAAA;oBACAnD,IAAA,EAAAR,KAAA,CAAAQ,IAAA;kBACA;gBACA;cACA;YACA;UACA;QACA,eAAAqG,QAAA,CAAAvG,OAAA,EAAAgD,QAAA,kBAAAA,QAAA;UACA;UACAnB,MAAA,CAAAC,IAAA,CAAAkB,QAAA,EAAAvD,OAAA,WAAAsC,GAAA;YACA,IAAAsB,KAAA,GAAAL,QAAA,CAAAjB,GAAA;YACA,IAAAsB,KAAA,KAAAC,SAAA,IAAAD,KAAA,aAAAA,KAAA;cACAiD,WAAA,CAAA1G,IAAA;gBACAsE,KAAA,EAAAnC,GAAA;gBACAsB,KAAA,EAAAA,KAAA;gBACAnD,IAAA;cACA;YACA;UACA;QACA;MACA,SAAAuD,CAAA;QACAvC,OAAA,CAAAD,KAAA,cAAAwC,CAAA;MACA;MAEA,OAAA6C,WAAA;IACA;IAEA,iBACAE,kBAAA,WAAAA,mBAAAvB,GAAA;MACA,KAAAA,GAAA;MACA,IAAAwB,KAAA,GAAAxB,GAAA,CAAAyB,KAAA;MACA,OAAAD,KAAA,CAAAA,KAAA,CAAAhE,MAAA;IACA;IAEA,iBACAkE,oBAAA,WAAAA,qBAAAC,MAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,MAAA;IACA;IAEA,oBACAP,gBAAA,WAAAA,iBAAA;MACAnF,OAAA,CAAAsD,GAAA;MACAtD,OAAA,CAAAsD,GAAA,iCAAA/G,UAAA,CAAAC,WAAA;MACA,SAAAD,UAAA,CAAAC,WAAA,SAAAD,UAAA,CAAAC,WAAA,CAAA+E,MAAA;QACA,KAAAhF,UAAA,CAAAC,WAAA,CAAA+B,OAAA,WAAA+C,IAAA,EAAAsE,KAAA;UACA5F,OAAA,CAAAsD,GAAA,gBAAAL,MAAA,CAAA2C,KAAA;YACAjK,SAAA,EAAA2F,IAAA,CAAA3F,SAAA;YACAC,QAAA,EAAA0F,IAAA,CAAA1F,QAAA;YACAE,WAAA,EAAAwF,IAAA,CAAAxF,WAAA;YACA+J,eAAA,MAAAR,QAAA,CAAAvG,OAAA,EAAAwC,IAAA,CAAAxF,WAAA;UACA;QACA;MACA;MACAkE,OAAA,CAAAsD,GAAA,0BAAA5G,eAAA;MACAsD,OAAA,CAAAsD,GAAA;IACA;IAEA,aACAwC,YAAA,WAAAA,aAAApE,GAAA;MAAA,IAAAqE,MAAA;MACA,IAAAC,SAAA,GAAAtE,GAAA,IAAAA,GAAA,CAAAlB,QAAA,SAAArF,GAAA;MACA,IAAA8K,WAAA,GAAAvE,GAAA,0EAAAuB,MAAA,CACAvB,GAAA,CAAAlB,QAAA,iGAAAyC,MAAA,CACA,KAAA9H,GAAA,CAAAoG,MAAA;MAEA,KAAAtB,MAAA,CAAAiG,OAAA,CAAAD,WAAA,EAAAtG,IAAA;QACA,WAAAwG,iBAAA,EAAAH,SAAA;MACA,GAAArG,IAAA;QACAoG,MAAA,CAAA3G,OAAA;QACA2G,MAAA,CAAA9F,MAAA,CAAAuD,UAAA;MACA,GAAA1D,KAAA;IACA;IACA,aACAsG,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAAxH,cAAA,CAAAC,OAAA,MACA,KAAAxB,WAAA,+BAAA2F,MAAA,CACA,IAAAwB,IAAA,GAAA6B,OAAA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA7E,GAAA;MAAA,IAAA8E,MAAA;MACA,IAAAC,IAAA,GAAA/E,GAAA,CAAAjB,KAAA;MACA,IAAAA,KAAA,GAAAiB,GAAA,CAAAjB,KAAA;MACA,KAAAR,MAAA,CAAAiG,OAAA,UAAAO,IAAA,YAAA/E,GAAA,CAAAjE,WAAA,UAAAkC,IAAA;QACA,IAAA+G,UAAA;UACAlG,QAAA,EAAAkB,GAAA,CAAAlB,QAAA;UACAC,KAAA,EAAAA;QACA;QACA,WAAA8C,oBAAA,EAAAmD,UAAA;MACA,GAAA/G,IAAA;QACA6G,MAAA,CAAApH,OAAA;QACAoH,MAAA,CAAAvG,MAAA,CAAAuD,UAAA,CAAAiD,IAAA;MACA,GAAA3G,KAAA;IACA;IAEA,WACA6G,cAAA,WAAAA,eAAAjF,GAAA;MAAA,IAAAkF,MAAA;MACA,KAAA3G,MAAA,CAAAiG,OAAA,cAAAxE,GAAA,CAAAjE,WAAA,UAAAkC,IAAA;QACA,WAAAkH,sBAAA,EAAAnF,GAAA,CAAAlB,QAAA;MACA,GAAAb,IAAA;QACAiH,MAAA,CAAAxH,OAAA;QACAwH,MAAA,CAAA3G,MAAA,CAAAuD,UAAA;MACA,GAAA1D,KAAA;IACA;IAEA,WACAgH,aAAA,WAAAA,cAAApF,GAAA;MAAA,IAAAqF,OAAA;MACA,KAAA9G,MAAA,CAAAiG,OAAA,cAAAxE,GAAA,CAAAjE,WAAA,UAAAkC,IAAA;QACA,WAAAqH,qBAAA,EAAAtF,GAAA,CAAAlB,QAAA;MACA,GAAAb,IAAA;QACAoH,OAAA,CAAA3H,OAAA;QACA2H,OAAA,CAAA9G,MAAA,CAAAuD,UAAA;MACA,GAAA1D,KAAA;IACA;IAEA,eACAmH,gBAAA,WAAAA,iBAAAvJ,UAAA;MACAsC,OAAA,CAAAsD,GAAA,mCAAA5F,UAAA;;MAEA;MACA,KAAAI,IAAA,CAAAiB,WAAA;MACA;MACA,KAAAjC,kBAAA;MAEA,KAAAY,UAAA;QACA,KAAAd,aAAA;QACA,KAAAC,oBAAA;QACA;MACA;MAEA,IAAAqK,QAAA,QAAAvK,YAAA,CAAAqI,IAAA,WAAAmC,GAAA;QAAA,OAAAA,GAAA,CAAAzJ,UAAA,KAAAA,UAAA;MAAA;MACAsC,OAAA,CAAAsD,GAAA,uCAAA4D,QAAA;MAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAE,UAAA;QACA;UACA,IAAAC,UAAA,GAAAtF,IAAA,CAAAC,KAAA,CAAAkF,QAAA,CAAAE,UAAA;UACApH,OAAA,CAAAsD,GAAA,mCAAA+D,UAAA;;UAEA;UACA,IAAApI,KAAA,CAAAC,OAAA,CAAAmI,UAAA,KAAAA,UAAA,CAAA9F,MAAA,QAAA8F,UAAA,IAAApF,MAAA;YACA;YACA,KAAAI,yBAAA,CAAAgF,UAAA;YACArH,OAAA,CAAAsD,GAAA,kEAAAxG,kBAAA;UACA;YACA;YACA,KAAAwF,iBAAA,CAAA5E,UAAA;YACAsC,OAAA,CAAAsD,GAAA,6DAAA1G,aAAA;UACA;QACA,SAAA2F,CAAA;UACAvC,OAAA,CAAAD,KAAA,cAAAwC,CAAA;UACA,KAAAD,iBAAA,CAAA5E,UAAA;QACA;MACA;QACAsC,OAAA,CAAAsD,GAAA;QACA,KAAA1G,aAAA;QACA,KAAAC,oBAAA;MACA;IACA;IAEA,eACAyF,iBAAA,WAAAA,kBAAA5E,UAAA;MAAA,IAAA4J,OAAA;MACA,KAAA5J,UAAA;QACA,KAAAd,aAAA;QACA,KAAAC,oBAAA;QACA;MACA;MAEA,IAAAqK,QAAA,QAAAvK,YAAA,CAAAqI,IAAA,WAAAmC,GAAA;QAAA,OAAAA,GAAA,CAAAzJ,UAAA,KAAAA,UAAA;MAAA;MACA,IAAAwJ,QAAA;QACA,KAAArK,oBAAA,GAAAqK,QAAA,CAAAK,YAAA;QAEA,IAAAL,QAAA,CAAAE,UAAA;UACA;YACA,IAAAC,UAAA,GAAAtF,IAAA,CAAAC,KAAA,CAAAkF,QAAA,CAAAE,UAAA;YACA,KAAAxK,aAAA;;YAEA;YACA,IAAAqC,KAAA,CAAAC,OAAA,CAAAmI,UAAA,KAAAA,UAAA,CAAA9F,MAAA;cACA,IAAA8F,UAAA,IAAApF,MAAA;gBACA;gBACAoF,UAAA,CAAA9I,OAAA,WAAAiJ,MAAA;kBACA,IAAAA,MAAA,CAAAvF,MAAA,IAAAhD,KAAA,CAAAC,OAAA,CAAAsI,MAAA,CAAAvF,MAAA;oBACAuF,MAAA,CAAAvF,MAAA,CAAA1D,OAAA,WAAAC,KAAA;sBACA;sBACA,IAAAA,KAAA,CAAAQ,IAAA,iBAAAR,KAAA,CAAAzD,IAAA;wBACAuM,OAAA,CAAA1K,aAAA,CAAA8B,IAAA,KAAAG,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAN,KAAA;0BACAC,WAAA,EAAA+I,MAAA,CAAAzM,IAAA;wBAAA,EACA;sBACA;oBACA;kBACA;gBACA;cACA;gBACA;gBACA,KAAA6B,aAAA,GAAAyK,UAAA;cACA;YACA;;YAEA;YACA,KAAAzK,aAAA,CAAA2B,OAAA,WAAAC,KAAA;cACA,IAAAA,KAAA,CAAAzD,IAAA;gBACA;gBACA,IAAAyD,KAAA,CAAAQ,IAAA;kBACAsI,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EACAkE,KAAA,CAAAC,OAAA,CAAAoI,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA,KAAAuM,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;gBACA,WAAAyD,KAAA,CAAAQ,IAAA;kBACA;kBACA,IAAAyI,QAAA,GAAAH,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;kBACA,WAAA0M,QAAA,iBAAAA,QAAA,CAAAC,IAAA;oBACA;oBACA,IAAAC,QAAA,GAAAF,QAAA,CAAAjC,KAAA,MAAAoC,GAAA;oBACAN,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA;sBACAA,IAAA,EAAA4M,QAAA;sBACA5D,GAAA,EAAA0D;oBACA;kBACA,WAAAxI,KAAA,CAAAC,OAAA,CAAAuI,QAAA;oBACA;oBACAH,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EAAA0M,QAAA;kBACA;oBACA;oBACAH,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA;kBACA;gBACA,WAAAyD,KAAA,CAAAQ,IAAA;kBACAsI,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EACAuM,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA,MAAAqH,SAAA,GAAAkF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;gBACA,WAAAyD,KAAA,CAAAQ,IAAA,eAAAR,KAAA,CAAAQ,IAAA;kBACAsI,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EACAuM,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA,MAAAqH,SAAA,GAAAkF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;gBACA;kBACAuM,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EACAuM,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA,MAAAqH,SAAA,GAAAkF,OAAA,CAAAxJ,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;gBACA;;gBAEA;gBACA,IAAAyD,KAAA,CAAArC,QAAA;kBACA,IAAA0L,QAAA,kBAAA5E,MAAA,CAAAzE,KAAA,CAAAzD,IAAA;kBACAuM,OAAA,CAAAzF,IAAA,CAAAyF,OAAA,CAAAvJ,KAAA,EAAA8J,QAAA,GACA;oBACA1L,QAAA;oBACAC,OAAA,KAAA6G,MAAA,CAAAzE,KAAA,CAAAwE,KAAA;oBACA3G,OAAA,EAAAmC,KAAA,CAAAQ,IAAA;kBACA,EACA;gBACA;cACA;YACA;UACA,SAAAuD,CAAA;YACAvC,OAAA,CAAAD,KAAA,gBAAAwC,CAAA;YACA,KAAA3F,aAAA;UACA;QACA;UACA,KAAAA,aAAA;QACA;MACA;IACA;IAEA,aACAkL,eAAA,WAAAA,gBAAAtJ,KAAA;MACA,KAAAA,KAAA,CAAAuJ,OAAA;MACA,OAAAvJ,KAAA,CAAAuJ,OAAA,CAAAvC,KAAA,MAAAnE,GAAA,WAAA2G,MAAA;QAAA,OAAAA,MAAA,CAAAN,IAAA;MAAA,GAAAO,MAAA,WAAAD,MAAA;QAAA,OAAAA,MAAA;MAAA;IACA;IAEA,eACAE,iBAAA,WAAAA,kBAAAtI,QAAA,EAAAuI,IAAA,EAAAC,QAAA,EAAA5J,KAAA;MACAwB,OAAA,CAAAsD,GAAA,kCAAA1D,QAAA,WAAAuI,IAAA,YAAA3J,KAAA,CAAAzD,IAAA;MAEA,IAAA6E,QAAA,CAAAqE,IAAA;QACA,IAAAoE,OAAA,GAAAzI,QAAA,CAAAmE,GAAA,IAAAnE,QAAA,CAAA+H,QAAA,IAAA/H,QAAA,CAAA3E,IAAA;;QAEA;QACA,KAAAqN,gBAAA,CAAA9J,KAAA,EAAA6J,OAAA;QAEArI,OAAA,CAAAsD,GAAA,sCAAA+E,OAAA;QACArI,OAAA,CAAAsD,GAAA,kDAAA9E,KAAA,CAAA2D,KAAA;MACA;QACA,KAAAlC,MAAA,CAAAC,QAAA,CAAAN,QAAA,CAAAsE,GAAA;MACA;IACA;IAEA,aACAqE,gBAAA,WAAAA,iBAAAJ,IAAA,EAAAC,QAAA,EAAA5J,KAAA;MACA;MACA,KAAA8J,gBAAA,CAAA9J,KAAA;MACAwB,OAAA,CAAAsD,GAAA;IACA;IAEA,gBACAkF,gBAAA,WAAAA,iBAAAC,SAAA;MACA,IAAAtG,KAAA,QAAArE,IAAA,CAAAiB,WAAA,CAAA0J,SAAA;MACA,OAAAxJ,KAAA,CAAAC,OAAA,CAAAiD,KAAA,IAAAA,KAAA;IACA;IAEA,cACAuG,mBAAA,WAAAA,oBAAAD,SAAA,EAAAtG,KAAA;MACA,KAAAN,IAAA,MAAA/D,IAAA,CAAAiB,WAAA,EAAA0J,SAAA,EAAAxJ,KAAA,CAAAC,OAAA,CAAAiD,KAAA,IAAAA,KAAA;IACA;IAEA,4BACAwG,WAAA,WAAAA,YAAAnK,KAAA;MAAA,IAAAoK,OAAA;MACA,IAAAC,KAAA,GAAArK,KAAA,CAAA2D,KAAA;MACAnC,OAAA,CAAAsD,GAAA,yBAAA9E,KAAA,CAAAzD,IAAA,YAAA8N,KAAA;;MAEA;MACA,WAAAA,KAAA,iBAAAA,KAAA,CAAAnB,IAAA;QACA;UACA3M,IAAA,OAAAuK,kBAAA,CAAAuD,KAAA;UACA9E,GAAA,EAAA8E,KAAA;UACAC,GAAA,KAAA7F,MAAA,CAAAzE,KAAA,CAAAzD,IAAA;UACAgO,MAAA;QACA;MACA;;MAEA;MACA,IAAA9J,KAAA,CAAAC,OAAA,CAAA2J,KAAA;QACA,OAAAA,KAAA,CAAAxH,GAAA,WAAA8G,IAAA,EAAAvC,KAAA;UAAA;YACA7K,IAAA,EAAAoN,IAAA,CAAApN,IAAA,IAAA6N,OAAA,CAAAtD,kBAAA,CAAA6C,IAAA,CAAApE,GAAA,IAAAoE,IAAA;YACApE,GAAA,EAAAoE,IAAA,CAAApE,GAAA,IAAAoE,IAAA;YACAW,GAAA,KAAA7F,MAAA,CAAAzE,KAAA,CAAAzD,IAAA,OAAAkI,MAAA,CAAA2C,KAAA;YACAmD,MAAA;UACA;QAAA;MACA;;MAEA;MACA/I,OAAA,CAAAsD,GAAA;MACA;IACA;IAEA,uBACA0F,gBAAA,WAAAA,iBAAAxK,KAAA;MACA,IAAAqK,KAAA,GAAArK,KAAA,CAAA2D,KAAA;MACA,OAAAlD,KAAA,CAAAC,OAAA,CAAA2J,KAAA,IAAAA,KAAA;IACA;IAEA,WACAI,iBAAA,WAAAA,kBAAAd,IAAA;MACA,IAAAA,IAAA,CAAApE,GAAA;QACAmF,MAAA,CAAA7L,IAAA,CAAA8K,IAAA,CAAApE,GAAA;MACA;IACA;IAEA,WACAoF,YAAA,WAAAA,aAAApF,GAAA,EAAA4D,QAAA;MACA;MACA,IAAAyB,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAxF,GAAA;MACAqF,IAAA,CAAA/C,QAAA,GAAAsB,QAAA;MACAyB,IAAA,CAAAI,MAAA;MACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;MACAA,IAAA,CAAAO,KAAA;MACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;IACA;IAEA,eACAS,kBAAA,WAAAA,mBAAArL,KAAA,EAAAoH,KAAA;MACA,IAAApH,KAAA,CAAA2D,KAAA,IAAAlD,KAAA,CAAAC,OAAA,CAAAV,KAAA,CAAA2D,KAAA;QACA,IAAA2H,QAAA,OAAAC,mBAAA,CAAAjL,OAAA,EAAAN,KAAA,CAAA2D,KAAA;QACA2H,QAAA,CAAAE,MAAA,CAAApE,KAAA;QACA,KAAA0C,gBAAA,CAAA9J,KAAA,EAAAsL,QAAA;MACA;IACA;IAEA,cACAG,aAAA,WAAAA,cAAAzL,KAAA;MACA,KAAA8J,gBAAA,CAAA9J,KAAA;IACA;EAAA,kCAAA8G,mBAGAvB,GAAA;IACA,KAAAA,GAAA;IACA,IAAAwB,KAAA,GAAAxB,GAAA,CAAAyB,KAAA;IACA,IAAAmC,QAAA,GAAApC,KAAA,CAAAA,KAAA,CAAAhE,MAAA;IACA;IACA,IAAA2I,KAAA,GAAAvC,QAAA,CAAAuC,KAAA;IACA,IAAAA,KAAA;MACA,uBAAAjH,MAAA,CAAAiH,KAAA;IACA;IACA,OAAAvC,QAAA;EACA,iCAGAW,iBAAA9J,KAAA,EAAA2D,KAAA;IACA;IACA3D,KAAA,CAAA2D,KAAA,GAAAA,KAAA;IACA;IACA,KAAAN,IAAA,MAAA/D,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EAAAoH,KAAA;IACAnC,OAAA,CAAAsD,GAAA,8BAAA9E,KAAA,CAAAzD,IAAA,YAAAoH,KAAA;EACA,iCAGAgI,iBAAA3L,KAAA;IACA,KAAAqD,IAAA,MAAA/D,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EAAAyD,KAAA,CAAA2D,KAAA;EACA,gCAGAiI,gBAAA;IACA,SAAAtN,kBAAA,SAAAA,kBAAA,CAAAyE,MAAA;MACA,YAAAzE,kBAAA,IAAA/B,IAAA;IACA;IACA,YAAA8B,oBAAA;EACA,0CAGAwF,0BAAApH,IAAA;IAAA,IAAAoP,OAAA;IACA,WAAApP,IAAA;MACA;QACA,KAAA6B,kBAAA,GAAAiF,IAAA,CAAAC,KAAA,CAAA/G,IAAA;MACA,SAAAsH,CAAA;QACAvC,OAAA,CAAAD,KAAA,gBAAAwC,CAAA;QACA,KAAAzF,kBAAA;MACA;IACA,WAAAmC,KAAA,CAAAC,OAAA,CAAAjE,IAAA;MACA,KAAA6B,kBAAA,GAAA7B,IAAA;IACA;MACA,KAAA6B,kBAAA;IACA;;IAEA;IACA,KAAAA,kBAAA,CAAAyB,OAAA,WAAA2D,YAAA;MACA,IAAAA,YAAA,CAAAD,MAAA;QACAC,YAAA,CAAAD,MAAA,CAAA1D,OAAA,WAAAC,KAAA;UACA;UACA,IAAAA,KAAA,CAAA2D,KAAA,KAAAC,SAAA,IAAA5D,KAAA,CAAA2D,KAAA;YACA,IAAA3D,KAAA,CAAAQ,IAAA;cACAR,KAAA,CAAA2D,KAAA;YACA,WAAA3D,KAAA,CAAAQ,IAAA;cACAR,KAAA,CAAA2D,KAAA;YACA;cACA3D,KAAA,CAAA2D,KAAA;YACA;UACA;;UAEA;UACA,IAAAkI,OAAA,CAAAvM,IAAA,CAAAiB,WAAA,IAAAsL,OAAA,CAAAvM,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA,MAAAqH,SAAA;YACA5D,KAAA,CAAA2D,KAAA,GAAAkI,OAAA,CAAAvM,IAAA,CAAAiB,WAAA,CAAAP,KAAA,CAAAzD,IAAA;UACA;YACA;YACAsP,OAAA,CAAAxI,IAAA,CAAAwI,OAAA,CAAAvM,IAAA,CAAAiB,WAAA,EAAAP,KAAA,CAAAzD,IAAA,EAAAyD,KAAA,CAAA2D,KAAA;UACA;QACA;MACA;IACA;EACA,kCAGA7C,kBAAA;IACA;IACA,IAAAgL,QAAA,IACA;MACA;MACA;MACA,WACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,EACA;MACA;IACA,GACA;MACA;MACA;MACA,WACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,EACA;MACA;IACA,EACA;;IAEA;IACA;EACA;AAMA", "ignoreList": []}]}