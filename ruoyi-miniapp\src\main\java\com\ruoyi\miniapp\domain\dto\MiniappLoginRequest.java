package com.ruoyi.miniapp.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 小程序登录请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@ApiModel("小程序登录请求")
public class MiniappLoginRequest
{
    @ApiModelProperty("微信登录凭证code")
    private String code;

    @ApiModelProperty("微信OpenID（可选，通过code获取）")
    private String openid;

    @ApiModelProperty("微信昵称")
    private String weixinNickname;

    @ApiModelProperty("微信头像")
    private String weixinAvatar;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("用户头像")
    private String avatar;

    @ApiModelProperty("微信UnionID")
    private String unionid;

    @ApiModelProperty("微信会话密钥")
    private String sessionKey;

    @ApiModelProperty("手机号码")
    private String phonenumber;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("性别（0男 1女 2未知）")
    private String sex;

    @ApiModelProperty("出生日期")
    private String birthDate;

    @ApiModelProperty("地区")
    private String region;

    @ApiModelProperty("毕业院校")
    private String graduateSchool;

    @ApiModelProperty("毕业年份")
    private String graduationYear;

    @ApiModelProperty("专业")
    private String major;

    @ApiModelProperty("学院")
    private String college;

    @ApiModelProperty("当前公司")
    private String currentCompany;

    @ApiModelProperty("行业领域（多个用逗号分隔）")
    private String industryField;

    @ApiModelProperty("职位标题")
    private String positionTitle;

    @ApiModelProperty("加密数据（已废弃，使用phoneCode代替）")
    private String encryptedData;

    @ApiModelProperty("初始向量（已废弃，使用phoneCode代替）")
    private String iv;

    @ApiModelProperty("手机号获取凭证（新版本使用）")
    private String phoneCode;

    public String getCode()
    {
        return code;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getOpenid()
    {
        return openid;
    }

    public void setOpenid(String openid)
    {
        this.openid = openid;
    }

    public String getWeixinNickname()
    {
        return weixinNickname;
    }

    public void setWeixinNickname(String weixinNickname)
    {
        this.weixinNickname = weixinNickname;
    }

    public String getWeixinAvatar()
    {
        return weixinAvatar;
    }

    public void setWeixinAvatar(String weixinAvatar)
    {
        this.weixinAvatar = weixinAvatar;
    }

    public String getUnionid()
    {
        return unionid;
    }

    public void setUnionid(String unionid)
    {
        this.unionid = unionid;
    }

    public String getSessionKey()
    {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey)
    {
        this.sessionKey = sessionKey;
    }

    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getRealName()
    {
        return realName;
    }

    public void setRealName(String realName)
    {
        this.realName = realName;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getEncryptedData()
    {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData)
    {
        this.encryptedData = encryptedData;
    }

    public String getIv()
    {
        return iv;
    }

    public void setIv(String iv)
    {
        this.iv = iv;
    }

    public String getPhoneCode()
    {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode)
    {
        this.phoneCode = phoneCode;
    }

    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getBirthDate()
    {
        return birthDate;
    }

    public void setBirthDate(String birthDate)
    {
        this.birthDate = birthDate;
    }

    public String getRegion()
    {
        return region;
    }

    public void setRegion(String region)
    {
        this.region = region;
    }

    public String getGraduateSchool()
    {
        return graduateSchool;
    }

    public void setGraduateSchool(String graduateSchool)
    {
        this.graduateSchool = graduateSchool;
    }

    public String getGraduationYear()
    {
        return graduationYear;
    }

    public void setGraduationYear(String graduationYear)
    {
        this.graduationYear = graduationYear;
    }

    public String getMajor()
    {
        return major;
    }

    public void setMajor(String major)
    {
        this.major = major;
    }

    public String getCollege()
    {
        return college;
    }

    public void setCollege(String college)
    {
        this.college = college;
    }

    public String getCurrentCompany()
    {
        return currentCompany;
    }

    public void setCurrentCompany(String currentCompany)
    {
        this.currentCompany = currentCompany;
    }

    public String getIndustryField()
    {
        return industryField;
    }

    public void setIndustryField(String industryField)
    {
        this.industryField = industryField;
    }

    public String getPositionTitle()
    {
        return positionTitle;
    }

    public void setPositionTitle(String positionTitle)
    {
        this.positionTitle = positionTitle;
    }

    @Override
    public String toString()
    {
        return "MiniappLoginRequest{" +
                "code='" + code + '\'' +
                ", openid='" + openid + '\'' +
                ", weixinNickname='" + weixinNickname + '\'' +
                ", weixinAvatar='" + weixinAvatar + '\'' +
                ", nickName='" + nickName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", unionid='" + unionid + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", phonenumber='" + phonenumber + '\'' +
                ", realName='" + realName + '\'' +
                ", sex='" + sex + '\'' +
                ", birthDate='" + birthDate + '\'' +
                ", region='" + region + '\'' +
                ", graduateSchool='" + graduateSchool + '\'' +
                ", graduationYear='" + graduationYear + '\'' +
                ", major='" + major + '\'' +
                ", college='" + college + '\'' +
                ", currentCompany='" + currentCompany + '\'' +
                ", industryField='" + industryField + '\'' +
                ", positionTitle='" + positionTitle + '\'' +
                ", encryptedData='" + encryptedData + '\'' +
                ", iv='" + iv + '\'' +
                '}';
    }
}
