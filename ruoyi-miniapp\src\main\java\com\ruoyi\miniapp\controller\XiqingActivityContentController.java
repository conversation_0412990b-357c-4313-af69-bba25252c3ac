package com.ruoyi.miniapp.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.XiqingActivityContent;
import com.ruoyi.miniapp.service.IXiqingActivityContentService;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 西青金种子专区活动内容管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "西青金种子-专区活动管理")
@RestController
@RequestMapping("/miniapp/xiqing/activity")
public class XiqingActivityContentController extends BaseController
{
    @Autowired
    private IXiqingActivityContentService xiqingActivityContentService;

    /**
     * 查询西青金种子专区活动内容管理列表
     */
    @ApiOperation("查询专区活动内容列表")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") XiqingActivityContent xiqingActivityContent)
    {
        startPage();
        List<XiqingActivityContent> list = xiqingActivityContentService.selectXiqingActivityContentList(xiqingActivityContent);
        return getDataTable(list);
    }

    /**
     * 获取西青金种子专区活动内容管理详细信息
     */
    @ApiOperation("获取专区活动内容详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:query')")
    @GetMapping(value = "/{contentId}")
    public AjaxResult getInfo(@ApiParam("内容ID") @PathVariable("contentId") Long contentId)
    {
        return AjaxResult.success(xiqingActivityContentService.selectXiqingActivityContentByContentId(contentId));
    }

    /**
     * 修改西青金种子专区活动内容管理
     */
    @ApiOperation("修改专区活动内容")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:activity:edit')")
    @Log(title = "西青金种子专区活动内容管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("专区活动内容信息") @RequestBody XiqingActivityContent xiqingActivityContent)
    {
        return toAjax(xiqingActivityContentService.updateXiqingActivityContent(xiqingActivityContent));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取启用的专区活动内容（小程序端）
     */
    @ApiOperation("获取启用的专区活动内容")
    @GetMapping("/app/getEnabledContent")
    public AjaxResult getEnabledContent()
    {
        XiqingActivityContent content = xiqingActivityContentService.selectEnabledXiqingActivityContent();
        return AjaxResult.success(content);
    }
}
