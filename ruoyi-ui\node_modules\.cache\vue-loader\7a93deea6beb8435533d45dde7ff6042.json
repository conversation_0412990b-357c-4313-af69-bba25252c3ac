{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\docking\\index.vue?vue&type=template&id=0f13cfaf&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\docking\\index.vue", "mtime": 1753633472180}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}