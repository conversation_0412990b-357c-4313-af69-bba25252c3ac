{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=style&index=0&id=733e5256&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753944331124}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZm9ybS1jb25maWctY29udGFpbmVyIHsNCiAgbWF4LXdpZHRoOiA4MDBweDsNCiAgbWFyZ2luOiAyMHB4IGF1dG87DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmNvbmZpZy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHggMjRweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5jb25maWctaGVhZGVyIGgzIHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouY29uZmlnLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyNHB4Ow0KICBtaW4taGVpZ2h0OiAzMDBweDsNCn0NCg0KLmZvcm0tcHJldmlldyBoNCB7DQogIG1hcmdpbjogMCAwIDIwcHggMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmZpZWxkLWxpc3Qgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5maWVsZC1pdGVtIHsNCiAgcGFkZGluZzogMTJweCAxNnB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCiAgYmFja2dyb3VuZDogI2ZhZmJmYzsNCn0NCg0KLmZpZWxkLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5maWVsZC1pbmZvIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouZmllbGQtaWNvbiB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5maWVsZC1sYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBtaW4td2lkdGg6IDEwMHB4Ow0KfQ0KDQouZmllbGQtdHlwZSB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEycHg7DQogIG1hcmdpbi1sZWZ0OiBhdXRvOw0KfQ0KDQouZW1wdHktZm9ybSB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNjBweCAyMHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLmVtcHR5LWZvcm0gaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi5mb3JtLWZpZWxkcy1jb25maWcgew0KICBtaW4taGVpZ2h0OiA0MDBweDsNCn0NCg0KLmZvcm0tZmllbGRzLXRvb2xiYXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLnRvb2xiYXItbGVmdCwgLnRvb2xiYXItcmlnaHQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEwcHg7DQp9DQoNCi5mb3JtLWZpZWxkcy1saXN0IHsNCiAgbWF4LWhlaWdodDogNTAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5mb3JtLWZpZWxkLWl0ZW0gew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzOw0KfQ0KDQouZm9ybS1maWVsZC1pdGVtOmhvdmVyIHsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSk7DQp9DQoNCi5maWVsZC1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDEycHggMTZweDsNCiAgYmFja2dyb3VuZDogI2ZhZmJmYzsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIGJvcmRlci1yYWRpdXM6IDZweCA2cHggMCAwOw0KfQ0KDQouZmllbGQtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogNXB4Ow0KfQ0KDQouZGFuZ2VyLWJ0biB7DQogIGNvbG9yOiAjZjU2YzZjOw0KfQ0KDQouZmllbGQtY29udGVudCB7DQogIHBhZGRpbmc6IDE2cHg7DQp9DQoNCi5maWVsZC1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmZpZWxkLWl0ZW0gbGFiZWwgew0KICBkaXNwbGF5OiBibG9jazsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQoub3B0aW9ucy1wcmV2aWV3IHsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KfQ0KDQouZW1wdHktc3RhdGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDYwcHggMjBweDsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5lbXB0eS1zdGF0ZSBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBkaXNwbGF5OiBibG9jazsNCn0NCg0KLmZvcm0tcHJldmlldyB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5wcmV2aWV3LWhlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCiAgcGFkZGluZy1ib3R0b206IDIwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOw0KfQ0KDQoucHJldmlldy1oZWFkZXIgaDMgew0KICBtYXJnaW46IDAgMCAxMHB4IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoucHJldmlldy1oZWFkZXIgcCB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnByZXZpZXctZmllbGQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoucHJldmlldy1sYWJlbCB7DQogIGRpc3BsYXk6IGJsb2NrOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQoucmVxdWlyZWQgew0KICBjb2xvcjogI2Y1NmM2YzsNCiAgbWFyZ2luLWxlZnQ6IDJweDsNCn0NCg0KLnByZXZpZXctaW5wdXQgew0KICB3aWR0aDogMTAwJTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+lBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/activity-config", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-config-container\">\r\n      <div class=\"config-header\">\r\n        <h3>路演活动报名表单配置</h3>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-setting\"\r\n          @click=\"handleFormConfig\"\r\n          v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\r\n        >配置表单</el-button>\r\n      </div>\r\n\r\n      <div class=\"config-content\" v-loading=\"loading\">\r\n        <div v-if=\"formFields.length > 0\" class=\"form-preview\">\r\n          <h4>当前表单字段预览：</h4>\r\n          <div class=\"field-list\">\r\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-item\">\r\n              <div class=\"field-info\">\r\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                <span class=\"field-label\">{{ field.label }}</span>\r\n                <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\r\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n                <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-else class=\"empty-form\">\r\n          <i class=\"el-icon-document-add\"></i>\r\n          <p>暂未配置表单字段，点击\"配置表单\"开始设置</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 表单配置对话框 -->\r\n    <el-dialog title=\"报名表单配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\r\n      <div class=\"form-fields-config\">\r\n        <!-- 工具栏 -->\r\n        <div class=\"form-fields-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\r\n              添加字段\r\n            </el-button>\r\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\r\n              <el-button size=\"small\">\r\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\r\n              预览表单\r\n            </el-button>\r\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\r\n              保存配置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 字段配置区域 -->\r\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\r\n          <div\r\n            v-for=\"(field, index) in formFields\"\r\n            :key=\"index\"\r\n            class=\"form-field-item\"\r\n          >\r\n            <div class=\"field-header\">\r\n              <div class=\"field-info\">\r\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                <span class=\"field-label\">{{ field.label || '未命名字段' }}</span>\r\n                <el-tag size=\"mini\" type=\"success\">{{ field.name || 'unnamed' }}</el-tag>\r\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n              </div>\r\n              <div class=\"field-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"moveField(index, -1)\"\r\n                  :disabled=\"index === 0\"\r\n                  icon=\"el-icon-arrow-up\"\r\n                ></el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"moveField(index, 1)\"\r\n                  :disabled=\"index === formFields.length - 1\"\r\n                  icon=\"el-icon-arrow-down\"\r\n                ></el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"removeFormField(index)\"\r\n                  icon=\"el-icon-delete\"\r\n                  class=\"danger-btn\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"field-content\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段标签</label>\r\n                    <el-input\r\n                      v-model=\"field.label\"\r\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\r\n                      size=\"small\"\r\n                      @input=\"updateFieldName(field, $event)\"\r\n                    />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段名称 (唯一标识)</label>\r\n                    <el-input\r\n                      v-model=\"field.name\"\r\n                      placeholder=\"字段的唯一标识符\"\r\n                      size=\"small\"\r\n                      readonly\r\n                      style=\"background-color: #f5f7fa;\"\r\n                    />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段类型</label>\r\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\r\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\r\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\r\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\r\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\r\n                      <el-option label=\"📞 电话\" value=\"tel\" />\r\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\r\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\r\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\r\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\r\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\r\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\r\n                      <el-option label=\"📅 日期\" value=\"date\" />\r\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\r\n                    </el-select>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n                <el-col :span=\"4\">\r\n                  <div class=\"field-item\">\r\n                    <label>是否必填</label>\r\n                    <el-switch v-model=\"field.required\" />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\r\n                  <div class=\"field-item\">\r\n                    <label>选项配置</label>\r\n                    <el-input\r\n                      v-model=\"field.options\"\r\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\r\n                      size=\"small\"\r\n                    />\r\n                    <div class=\"options-preview\" v-if=\"field.options\">\r\n                      <el-tag\r\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\r\n                        :key=\"optIndex\"\r\n                        size=\"mini\"\r\n                        style=\"margin-right: 5px; margin-top: 5px;\"\r\n                      >\r\n                        {{ option.trim() }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div class=\"empty-state\" v-else>\r\n          <i class=\"el-icon-document-add\"></i>\r\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"form-preview\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\r\n          <p>请填写以下信息完成报名</p>\r\n        </div>\r\n        <div class=\"preview-form\">\r\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\r\n            <label class=\"preview-label\">\r\n              {{ field.label }}\r\n              <span v-if=\"field.required\" class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"preview-input\">\r\n              <el-input\r\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input\r\n                v-else-if=\"field.type === 'textarea'\"\r\n                type=\"textarea\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input-number\r\n                v-else-if=\"field.type === 'number'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n                style=\"width: 100%\"\r\n              />\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\r\n                <el-radio\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                >\r\n                  {{ option.trim() }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\r\n                <el-checkbox\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                >\r\n                  {{ option.trim() }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                  :value=\"option.trim()\"\r\n                />\r\n              </el-select>\r\n              <el-date-picker\r\n                v-else-if=\"field.type === 'date'\"\r\n                type=\"date\"\r\n                :placeholder=\"'请选择' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n                style=\"width: 100%\"\r\n              />\r\n              <el-upload\r\n                v-else-if=\"field.type === 'file'\"\r\n                class=\"upload-demo\"\r\n                action=\"#\"\r\n                :disabled=\"true\"\r\n                :show-file-list=\"false\"\r\n              >\r\n                <el-button size=\"small\" type=\"primary\" disabled>\r\n                  <i class=\"el-icon-upload\"></i> 选择文件\r\n                </el-button>\r\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\r\n              </el-upload>\r\n              <!-- 单选+其他 -->\r\n              <div v-else-if=\"field.type === 'radio_other'\">\r\n                <el-radio-group disabled>\r\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\r\n                  </div>\r\n                  <div style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-radio label=\"其他\">\r\n                      其他\r\n                      <el-input\r\n                        placeholder=\"请输入其他内容\"\r\n                        size=\"small\"\r\n                        disabled\r\n                        style=\"width: 200px; margin-left: 10px;\"\r\n                      />\r\n                    </el-radio>\r\n                  </div>\r\n                </el-radio-group>\r\n              </div>\r\n              <!-- 多选+其他 -->\r\n              <div v-else-if=\"field.type === 'checkbox_other'\">\r\n                <el-checkbox-group disabled>\r\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\r\n                  </div>\r\n                  <div style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-checkbox label=\"其他\">\r\n                      其他\r\n                      <el-input\r\n                        placeholder=\"请输入其他内容\"\r\n                        size=\"small\"\r\n                        disabled\r\n                        style=\"width: 200px; margin-left: 10px;\"\r\n                      />\r\n                    </el-checkbox>\r\n                  </div>\r\n                </el-checkbox-group>\r\n              </div>\r\n              <!-- 下拉+其他 -->\r\n              <div v-else-if=\"field.type === 'select_other'\">\r\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\r\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n                <el-input\r\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\r\n                  size=\"small\"\r\n                  disabled\r\n                  style=\"width: 100%;\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\r\n\r\nexport default {\r\n  name: \"XiqingActivityConfig\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 是否显示表单配置弹出层\r\n      formConfigOpen: false,\r\n      // 是否显示表单预览弹出层\r\n      previewDialogVisible: false,\r\n      // 表单字段配置\r\n      formFields: [],\r\n      // 活动ID（固定为1，因为只有一个路演活动配置）\r\n      activityId: 1\r\n    };\r\n  },\r\n  created() {\r\n    this.loadFormConfig();\r\n  },\r\n  methods: {\r\n    /** 加载表单配置 */\r\n    loadFormConfig() {\r\n      this.loading = true;\r\n      getActivityConfig(this.activityId).then(response => {\r\n        if (response.data && response.data.formConfig) {\r\n          try {\r\n            this.formFields = JSON.parse(response.data.formConfig);\r\n          } catch (e) {\r\n            this.formFields = [];\r\n          }\r\n        } else {\r\n          this.formFields = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.formFields = [];\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig() {\r\n      this.formConfigOpen = true;\r\n    },\r\n    /** 添加表单字段 */\r\n    addFormField() {\r\n      // 生成默认的唯一字段名\r\n      const defaultName = this.generateUniqueFieldName('field');\r\n\r\n      this.formFields.push({\r\n        name: defaultName,\r\n        label: '',\r\n        type: 'input',\r\n        required: false,\r\n        options: ''\r\n      });\r\n    },\r\n    /** 删除表单字段 */\r\n    removeFormField(index) {\r\n      this.formFields.splice(index, 1);\r\n    },\r\n    /** 移动字段位置 */\r\n    moveField(index, direction) {\r\n      const newIndex = index + direction;\r\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\r\n        const temp = this.formFields[index];\r\n        this.$set(this.formFields, index, this.formFields[newIndex]);\r\n        this.$set(this.formFields, newIndex, temp);\r\n      }\r\n    },\r\n    /** 更新字段名称 */\r\n    updateFieldName(field, label) {\r\n      if (!field.name || field.name === '') {\r\n        field.name = this.generateUniqueFieldName(label);\r\n      }\r\n    },\r\n    /** 生成唯一字段名称 */\r\n    generateUniqueFieldName(label) {\r\n      const pinyin = {\r\n        '姓名': 'name',\r\n        '联系电话': 'phone',\r\n        '电话': 'phone',\r\n        '邮箱': 'email',\r\n        '邮箱地址': 'email',\r\n        '公司': 'company',\r\n        '项目名称': 'project_name',\r\n        '项目描述': 'project_description',\r\n        '团队规模': 'team_size'\r\n      };\r\n\r\n      // 生成基础名称\r\n      let baseName = pinyin[label] || label.toLowerCase().replace(/[\\s\\u4e00-\\u9fa5]+/g, '_').replace(/[^\\w_]/g, '');\r\n\r\n      // 确保名称不为空\r\n      if (!baseName) {\r\n        baseName = 'field';\r\n      }\r\n\r\n      // 检查名称是否已存在，如果存在则添加数字后缀\r\n      let uniqueName = baseName;\r\n      let counter = 1;\r\n\r\n      while (this.isFieldNameExists(uniqueName)) {\r\n        uniqueName = `${baseName}_${counter}`;\r\n        counter++;\r\n      }\r\n\r\n      return uniqueName;\r\n    },\r\n    /** 检查字段名称是否已存在 */\r\n    isFieldNameExists(name) {\r\n      return this.formFields.some(field => field.name === name);\r\n    },\r\n    /** 获取字段图标 */\r\n    getFieldIcon(type) {\r\n      const icons = {\r\n        input: 'el-icon-edit',\r\n        textarea: 'el-icon-document',\r\n        number: 'el-icon-s-data',\r\n        email: 'el-icon-message',\r\n        tel: 'el-icon-phone',\r\n        radio: 'el-icon-success',\r\n        checkbox: 'el-icon-check',\r\n        select: 'el-icon-arrow-down',\r\n        radio_other: 'el-icon-circle-plus',\r\n        checkbox_other: 'el-icon-square-plus',\r\n        select_other: 'el-icon-plus',\r\n        date: 'el-icon-date',\r\n        file: 'el-icon-upload'\r\n      };\r\n      return icons[type] || 'el-icon-edit';\r\n    },\r\n    /** 处理模板命令 */\r\n    handleTemplateCommand(command) {\r\n      if (command === 'clear') {\r\n        this.$confirm('确定要清空所有字段吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.formFields = [];\r\n          this.$message.success('已清空所有字段');\r\n        });\r\n        return;\r\n      }\r\n\r\n      const templates = {\r\n        basic: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' }\r\n        ],\r\n        roadshow: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\r\n          { label: '公司/团队', name: '', type: 'input', required: true, options: '' },\r\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\r\n          { label: '项目来源', name: '', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },\r\n          { label: '项目阶段', name: '', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\r\n          { label: '团队规模', name: '', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\r\n          { label: '融资需求', name: '', type: 'input', required: false, options: '' },\r\n          { label: '商业计划书', name: '', type: 'file', required: true, options: '' },\r\n          { label: '项目描述', name: '', type: 'textarea', required: true, options: '' }\r\n        ]\r\n      };\r\n\r\n      if (templates[command]) {\r\n        // 清空现有字段\r\n        this.formFields = [];\r\n\r\n        // 为每个模板字段生成唯一的name\r\n        const templateFields = templates[command].map(field => ({\r\n          ...field,\r\n          name: this.generateUniqueFieldName(field.label)\r\n        }));\r\n\r\n        this.formFields = templateFields;\r\n        this.$message.success('模板应用成功');\r\n      }\r\n    },\r\n    /** 预览表单 */\r\n    previewForm() {\r\n      if (this.formFields.length === 0) {\r\n        this.$message.warning('请先添加表单字段');\r\n        return;\r\n      }\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (this.formFields.length === 0) {\r\n        this.$message.warning('请至少添加一个表单字段');\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置\r\n      const fieldNames = [];\r\n      for (let i = 0; i < this.formFields.length; i++) {\r\n        const field = this.formFields[i];\r\n\r\n        // 验证标签不能为空\r\n        if (!field.label) {\r\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\r\n          return;\r\n        }\r\n\r\n        // 验证字段名称不能为空\r\n        if (!field.name) {\r\n          this.$message.error(`第${i + 1}个字段的名称不能为空`);\r\n          return;\r\n        }\r\n\r\n        // 验证字段名称唯一性\r\n        if (fieldNames.includes(field.name)) {\r\n          this.$message.error(`字段名称\"${field.name}\"重复，请确保每个字段名称唯一`);\r\n          return;\r\n        }\r\n        fieldNames.push(field.name);\r\n\r\n        // 验证选项配置\r\n        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {\r\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const configData = JSON.stringify(this.formFields);\r\n      const updateData = {\r\n        activityId: this.activityId,\r\n        formConfig: configData\r\n      };\r\n\r\n      updateActivityConfig(updateData).then(() => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.loadFormConfig();\r\n      });\r\n    },\r\n    /** 获取字段类型名称 */\r\n    getFieldTypeName(type) {\r\n      const typeNames = {\r\n        input: '文本输入',\r\n        textarea: '多行文本',\r\n        number: '数字输入',\r\n        email: '邮箱',\r\n        tel: '电话',\r\n        radio: '单选',\r\n        checkbox: '多选',\r\n        select: '下拉选择',\r\n        radio_other: '单选+其他',\r\n        checkbox_other: '多选+其他',\r\n        select_other: '下拉+其他',\r\n        date: '日期',\r\n        file: '文件上传'\r\n      };\r\n      return typeNames[type] || '未知类型';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  max-width: 800px;\r\n  margin: 20px auto;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 300px;\r\n}\r\n\r\n.form-preview h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  min-width: 100px;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n}\r\n\r\n.empty-form {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.form-fields-config {\r\n  min-height: 400px;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.toolbar-left, .toolbar-right {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.form-fields-list {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-field-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 15px;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.form-field-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.field-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.field-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.danger-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.field-content {\r\n  padding: 16px;\r\n}\r\n\r\n.field-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.field-item label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.options-preview {\r\n  margin-top: 5px;\r\n}\r\n\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.form-preview {\r\n  padding: 20px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #303133;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 2px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}