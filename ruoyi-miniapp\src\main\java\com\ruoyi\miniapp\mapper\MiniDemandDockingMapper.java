package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniDemandDocking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 需求对接Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Mapper
public interface MiniDemandDockingMapper 
{
    /**
     * 查询需求对接
     * 
     * @param dockingId 需求对接主键
     * @return 需求对接
     */
    public MiniDemandDocking selectMiniDemandDockingByDockingId(Long dockingId);

    /**
     * 查询需求对接列表
     * 
     * @param miniDemandDocking 需求对接
     * @return 需求对接集合
     */
    public List<MiniDemandDocking> selectMiniDemandDockingList(MiniDemandDocking miniDemandDocking);

    /**
     * 新增需求对接
     * 
     * @param miniDemandDocking 需求对接
     * @return 结果
     */
    public int insertMiniDemandDocking(MiniDemandDocking miniDemandDocking);

    /**
     * 修改需求对接
     * 
     * @param miniDemandDocking 需求对接
     * @return 结果
     */
    public int updateMiniDemandDocking(MiniDemandDocking miniDemandDocking);

    /**
     * 删除需求对接
     * 
     * @param dockingId 需求对接主键
     * @return 结果
     */
    public int deleteMiniDemandDockingByDockingId(Long dockingId);

    /**
     * 批量删除需求对接
     * 
     * @param dockingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniDemandDockingByDockingIds(Long[] dockingIds);

    /**
     * 查询用户是否已对接某需求
     * 
     * @param demandId 需求ID
     * @param userId 用户ID
     * @return 对接记录
     */
    public MiniDemandDocking selectDockingRecord(@Param("demandId") Long demandId, @Param("userId") Long userId);

    /**
     * 查询需求的对接列表（后台管理用）
     * 
     * @param demandId 需求ID
     * @return 对接列表
     */
    public List<MiniDemandDocking> selectDockingListByDemandId(@Param("demandId") Long demandId);

    /**
     * 查询用户的对接列表（我的对接）
     * 
     * @param userId 用户ID
     * @return 对接列表
     */
    public List<MiniDemandDocking> selectMyDockingList(@Param("userId") Long userId);

    /**
     * 统计需求的对接次数
     * 
     * @param demandId 需求ID
     * @return 对接次数
     */
    public int countDockingByDemandId(@Param("demandId") Long demandId);

    /**
     * 统计用户的对接次数
     * 
     * @param userId 用户ID
     * @return 对接次数
     */
    public int countDockingByUserId(@Param("userId") Long userId);

    /**
     * 批量查询用户对接状态
     *
     * @param userId 用户ID
     * @param demandIds 需求ID列表
     * @return 对接状态列表
     */
    public List<MiniDemandDocking> batchSelectDockingStatus(@Param("userId") Long userId,
                                                            @Param("demandIds") List<Long> demandIds);

    /**
     * 查询需求对接详情（包含需求和用户信息）
     * 
     * @param dockingId 对接ID
     * @return 对接详情
     */
    public MiniDemandDocking selectDockingDetailById(@Param("dockingId") Long dockingId);

    /**
     * 查询需求的对接详情列表（包含用户信息）
     * 
     * @param demandId 需求ID
     * @return 对接详情列表
     */
    public List<MiniDemandDocking> selectDockingDetailListByDemandId(@Param("demandId") Long demandId);
}
