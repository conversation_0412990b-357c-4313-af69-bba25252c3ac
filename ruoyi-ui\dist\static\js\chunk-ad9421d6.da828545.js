(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ad9421d6"],{"0c5d":function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"app-container"},[s("el-row",{staticClass:"mb20",attrs:{gutter:20}},[s("el-col",{attrs:{span:6}},[s("el-card",{staticClass:"stat-card"},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.statistics.totalUsers))]),s("div",{staticClass:"stat-label"},[t._v("总用户数")]),s("div",{staticClass:"stat-trend"},[s("i",{staticClass:"el-icon-arrow-up",staticStyle:{color:"#67c23a"}}),s("span",{staticClass:"trend-text"},[t._v("+"+t._s(t.statistics.newUsersToday))])])])])],1),s("el-col",{attrs:{span:6}},[s("el-card",{staticClass:"stat-card"},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.statistics.totalBarrages))]),s("div",{staticClass:"stat-label"},[t._v("弹幕总数")]),s("div",{staticClass:"stat-trend"},[s("i",{staticClass:"el-icon-arrow-up",staticStyle:{color:"#67c23a"}}),s("span",{staticClass:"trend-text"},[t._v("+"+t._s(t.statistics.newBarragesToday))])])])])],1),s("el-col",{attrs:{span:6}},[s("el-card",{staticClass:"stat-card"},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.statistics.pendingAuditCount))]),s("div",{staticClass:"stat-label"},[t._v("待审核弹幕")]),s("div",{staticClass:"stat-trend"},[t.statistics.pendingAuditCount>0?s("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.goToAudit}},[t._v(" 去审核 ")]):t._e()],1)])])],1),s("el-col",{attrs:{span:6}},[s("el-card",{staticClass:"stat-card"},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-value"},[t._v(t._s(t.statistics.totalActivities))]),s("div",{staticClass:"stat-label"},[t._v("活动总数")]),s("div",{staticClass:"stat-trend"},[s("i",{staticClass:"el-icon-arrow-up",staticStyle:{color:"#67c23a"}}),s("span",{staticClass:"trend-text"},[t._v("+"+t._s(t.statistics.newActivitiesToday))])])])])],1)],1),s("el-row",{staticClass:"mb20",attrs:{gutter:20}},[s("el-col",{attrs:{span:12}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("用户注册趋势")]),s("el-button-group",{staticStyle:{float:"right"}},[s("el-button",{attrs:{size:"mini",type:"week"===t.chartPeriod?"primary":"default"},on:{click:function(a){return t.changeChartPeriod("week")}}},[t._v("近7天")]),s("el-button",{attrs:{size:"mini",type:"month"===t.chartPeriod?"primary":"default"},on:{click:function(a){return t.changeChartPeriod("month")}}},[t._v("近30天")])],1)],1),s("div",{ref:"userChart",staticStyle:{height:"300px"}})])],1),s("el-col",{attrs:{span:12}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("弹幕审核状态")])]),s("div",{ref:"auditChart",staticStyle:{height:"300px"}})])],1)],1),s("el-row",{staticClass:"mb20",attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("快速操作")])]),s("div",{staticClass:"quick-actions"},[s("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(a){return t.$router.push("/miniapp/content/banner")}}},[t._v(" 新增轮播图 ")]),s("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:function(a){return t.$router.push("/miniapp/content/notice")}}},[t._v(" 发布通知 ")]),s("el-button",{attrs:{type:"warning",icon:"el-icon-check"},on:{click:function(a){return t.$router.push("/miniapp/content/barrage")}}},[t._v(" 审核弹幕 ")]),s("el-button",{attrs:{type:"info",icon:"el-icon-plus"},on:{click:function(a){return t.$router.push("/miniapp/business/event")}}},[t._v(" 创建活动 ")])],1)])],1),s("el-col",{attrs:{span:8}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("用户积分排行")])]),s("div",{staticClass:"ranking-list"},t._l(t.topUsers,(function(a,e){return s("div",{key:a.userId,staticClass:"ranking-item"},[s("div",{staticClass:"ranking-number"},[t._v(t._s(e+1))]),s("el-avatar",{attrs:{size:30,src:a.avatarUrl}}),s("div",{staticClass:"user-info"},[s("div",{staticClass:"user-name"},[t._v(t._s(a.nickname))]),s("div",{staticClass:"user-points"},[t._v(t._s(a.totalPoints)+" 积分")])])],1)})),0)])],1),s("el-col",{attrs:{span:8}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("最新弹幕")]),s("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:function(a){return t.$router.push("/miniapp/content/barrage")}}},[t._v(" 查看全部 ")])],1),s("div",{staticClass:"latest-barrages"},t._l(t.latestBarrages,(function(a){return s("div",{key:a.barrageId,staticClass:"barrage-item"},[s("div",{staticClass:"barrage-user"},[s("el-avatar",{attrs:{size:20,src:a.userAvatarUrl}}),s("span",{staticClass:"user-name"},[t._v(t._s(a.userNickname))])],1),s("div",{staticClass:"barrage-content"},[t._v(t._s(a.content))]),s("div",{staticClass:"barrage-status"},[s("el-tag",{attrs:{type:t.getAuditStatusType(a.auditStatus),size:"mini"}},[t._v(" "+t._s(t.getAuditStatusText(a.auditStatus))+" ")])],1)])})),0)])],1)],1),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:24}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("今日数据概览")]),s("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[t._v(" 刷新数据 ")])],1),s("el-table",{staticStyle:{width:"100%"},attrs:{data:t.todayData,stripe:""}},[s("el-table-column",{attrs:{prop:"type",label:"数据类型",width:"120"}}),s("el-table-column",{attrs:{prop:"count",label:"数量",width:"100"}}),s("el-table-column",{attrs:{prop:"percentage",label:"相比昨日",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[0!==a.row.percentage?s("span",{class:a.row.percentage>0?"text-success":"text-danger"},[t._v(" "+t._s(a.row.percentage>0?"+":"")+t._s(a.row.percentage)+"% ")]):s("span",[t._v("-")])]}}])}),s("el-table-column",{attrs:{prop:"remark",label:"备注"}})],1)],1)],1)],1)],1)},i=[],r=s("c14f"),n=s("3835"),c=s("1da1"),l=(s("14d9"),s("d3b7"),s("3ca3"),s("ddb0"),s("b775"));function o(){return Object(l["a"])({url:"/miniapp/statistics/getStatistics",method:"post"})}function u(){return Object(l["a"])({url:"/miniapp/statistics/getUserRanking",method:"post"})}function d(){return Object(l["a"])({url:"/miniapp/statistics/getLatestBarrages",method:"post"})}function p(){return Object(l["a"])({url:"/miniapp/statistics/getTodayData",method:"post"})}var h=s("313e"),v={name:"MiniappStatistics",data:function(){return{loading:!1,chartPeriod:"week",statistics:{totalUsers:0,newUsersToday:0,totalBarrages:0,newBarragesToday:0,pendingAuditCount:0,totalActivities:0,newActivitiesToday:0},topUsers:[],latestBarrages:[],todayData:[],userChart:null,auditChart:null}},created:function(){this.loadData()},mounted:function(){this.initCharts()},methods:{loadData:function(){var t=this;return Object(c["a"])(Object(r["a"])().m((function a(){var s,e,i,c,l,h,v;return Object(r["a"])().w((function(a){while(1)switch(a.n){case 0:return t.loading=!0,a.p=1,a.n=2,Promise.all([o(),u(),d(),p()]);case 2:s=a.v,e=Object(n["a"])(s,4),i=e[0],c=e[1],l=e[2],h=e[3],t.statistics=i.data,t.topUsers=c.data||[],t.latestBarrages=l.data||[],t.todayData=h.data||[],t.updateCharts(),a.n=4;break;case 3:a.p=3,v=a.v,console.error("加载数据失败:",v);case 4:return a.p=4,t.loading=!1,a.f(4);case 5:return a.a(2)}}),a,null,[[1,3,4,5]])})))()},initCharts:function(){var t=this;this.userChart=h["init"](this.$refs.userChart),this.auditChart=h["init"](this.$refs.auditChart),window.addEventListener("resize",(function(){t.userChart.resize(),t.auditChart.resize()}))},updateCharts:function(){var t={tooltip:{trigger:"axis"},xAxis:{type:"category",data:this.statistics.userTrendDates||[]},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:this.statistics.userTrendCounts||[],smooth:!0,itemStyle:{color:"#409EFF"}}]};this.userChart.setOption(t);var a={tooltip:{trigger:"item"},series:[{name:"弹幕审核状态",type:"pie",radius:"60%",data:[{value:this.statistics.pendingAuditCount,name:"待审核"},{value:this.statistics.approvedCount,name:"已通过"},{value:this.statistics.rejectedCount,name:"已拒绝"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};this.auditChart.setOption(a)},changeChartPeriod:function(t){this.chartPeriod=t,this.loadData()},refreshData:function(){this.loadData(),this.$message.success("数据刷新成功")},goToAudit:function(){this.$router.push("/miniapp/content/barrage")},getAuditStatusType:function(t){var a={0:"warning",1:"success",2:"danger"};return a[t]||"info"},getAuditStatusText:function(t){var a={0:"待审核",1:"已通过",2:"已拒绝"};return a[t]||"未知"}},beforeDestroy:function(){this.userChart&&this.userChart.dispose(),this.auditChart&&this.auditChart.dispose()}},C=v,g=(s("29e1"),s("2877")),f=Object(g["a"])(C,e,i,!1,null,"b9bde7a6",null);a["default"]=f.exports},"29e1":function(t,a,s){"use strict";s("5c1d")},"5c1d":function(t,a,s){}}]);