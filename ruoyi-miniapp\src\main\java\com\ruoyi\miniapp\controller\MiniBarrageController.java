package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniBarrage;
import com.ruoyi.miniapp.service.IMiniBarrageService;
import com.ruoyi.miniapp.annotation.SensitiveWordFilter;
import com.ruoyi.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 弹幕Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "弹幕管理")
@RestController
@RequestMapping("/miniapp/barrage")
public class MiniBarrageController extends BaseController
{
    @Autowired
    private IMiniBarrageService miniBarrageService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询弹幕列表
     */
    @ApiOperation("查询弹幕列表")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniBarrage miniBarrage)
    {
        startPage();
        List<MiniBarrage> list = miniBarrageService.selectMiniBarrageList(miniBarrage);
        return getDataTable(list);
    }

    /**
     * 导出弹幕列表
     */
    @ApiOperation("导出弹幕列表")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:export')")
    @Log(title = "弹幕", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniBarrage miniBarrage)
    {
        List<MiniBarrage> list = miniBarrageService.selectMiniBarrageList(miniBarrage);
        ExcelUtil<MiniBarrage> util = new ExcelUtil<MiniBarrage>(MiniBarrage.class);
        util.exportExcel(response, list, "弹幕数据");
    }

    /**
     * 获取弹幕详细信息
     */
    @ApiOperation("获取弹幕详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("弹幕ID") @RequestBody Long barrageId)
    {
        return AjaxResult.success(miniBarrageService.selectMiniBarrageByBarrageId(barrageId));
    }

    /**
     * 新增弹幕
     */
    @ApiOperation("新增弹幕")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:add')")
    @Log(title = "弹幕", businessType = BusinessType.INSERT)
    @SensitiveWordFilter(moduleName = "弹幕管理", strategy = SensitiveWordFilter.FilterStrategy.REPLACE, fields = {"content"})
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("弹幕信息") @RequestBody MiniBarrage miniBarrage)
    {
        return toAjax(miniBarrageService.insertMiniBarrage(miniBarrage));
    }

    /**
     * 修改弹幕
     */
    @ApiOperation("修改弹幕")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:edit')")
    @Log(title = "弹幕", businessType = BusinessType.UPDATE)
    @SensitiveWordFilter(moduleName = "弹幕管理", strategy = SensitiveWordFilter.FilterStrategy.REPLACE, fields = {"content"})
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("弹幕信息") @RequestBody MiniBarrage miniBarrage)
    {
        return toAjax(miniBarrageService.updateMiniBarrage(miniBarrage));
    }

    /**
     * 删除弹幕
     */
    @ApiOperation("删除弹幕")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:remove')")
    @Log(title = "弹幕", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("弹幕ID数组") @RequestBody Long[] barrageIds)
    {
        return toAjax(miniBarrageService.deleteMiniBarrageByBarrageIds(barrageIds));
    }

    /**
     * 审核弹幕
     */
    @ApiOperation("审核弹幕")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:audit')")
    @Log(title = "弹幕审核", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult audit(@ApiParam("审核信息") @RequestBody AuditRequest request)
    {
        return toAjax(miniBarrageService.auditBarrage(
            request.getBarrageId(),
            request.getAuditStatus(),
            request.getAuditRemark()
        ));
    }

    /**
     * 批量审核通过
     */
    @ApiOperation("批量审核通过")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:audit')")
    @Log(title = "批量审核通过", businessType = BusinessType.UPDATE)
    @PostMapping("/batchApprove")
    public AjaxResult batchApprove(@ApiParam("弹幕ID数组") @RequestBody Long[] barrageIds)
    {
        int successCount = 0;
        for (Long barrageId : barrageIds) {
            int result = miniBarrageService.auditBarrage(barrageId, "1", "批量审核通过");
            if (result > 0) {
                successCount++;
            }
        }
        return AjaxResult.success("成功审核通过 " + successCount + " 条弹幕");
    }

    /**
     * 批量审核拒绝
     */
    @ApiOperation("批量审核拒绝")
    @PreAuthorize("@ss.hasPermi('miniapp:barrage:audit')")
    @Log(title = "批量审核拒绝", businessType = BusinessType.UPDATE)
    @PostMapping("/batchReject")
    public AjaxResult batchReject(@ApiParam("批量拒绝信息") @RequestBody BatchRejectRequest request)
    {
        int successCount = 0;
        for (Long barrageId : request.getBarrageIds()) {
            int result = miniBarrageService.auditBarrage(barrageId, "2", request.getRejectReason());
            if (result > 0) {
                successCount++;
            }
        }
        return AjaxResult.success("成功拒绝 " + successCount + " 条弹幕");
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取通过审核的弹幕列表
     */
    @ApiOperation("获取通过审核的弹幕列表")
    @PostMapping("/app/getApprovedList")
    public AjaxResult getApprovedList()
    {
        List<MiniBarrage> list = miniBarrageService.selectApprovedMiniBarrageList();
        return AjaxResult.success(list);
    }

    /**
     * 发布弹幕
     */
    @ApiOperation("发布弹幕")
    @PostMapping("/app/publish")
    @SensitiveWordFilter(moduleName = "弹幕管理", strategy = SensitiveWordFilter.FilterStrategy.REJECT, ignoreError = false, fields = {"content"})
    public AjaxResult publish(@ApiParam("弹幕信息") @RequestBody MiniBarrage miniBarrage)
    {
        return toAjax(miniBarrageService.publishBarrage(miniBarrage));
    }

    /**
     * 获取弹幕配置
     */
    @ApiOperation("获取弹幕配置")
    @PostMapping("/app/getConfig")
    public AjaxResult getConfig()
    {
        BarrageConfig config = new BarrageConfig();

        // 获取弹幕行数配置，最大值为3，不能为负数
        String rowsStr = configService.selectConfigByKey("danmaku.display.rows");
        int rows = parsePositiveInt(rowsStr, 1);
        config.setRows(Math.min(rows, 3));

        // 获取弹幕滚动速度配置，不能为负数
        String speedStr = configService.selectConfigByKey("danmaku.scroll.speed");
        int speed = parsePositiveInt(speedStr, 10);
        config.setSpeed(speed);

        // 获取弹幕发送间隔配置，不能为负数
        String intervalStr = configService.selectConfigByKey("danmaku.send.interval");
        int interval = parsePositiveInt(intervalStr, 2);
        config.setInterval(interval);

        return AjaxResult.success(config);
    }

    /**
     * 解析正整数，如果解析失败或为负数则返回默认值
     */
    private int parsePositiveInt(String str, int defaultValue) {
        try {
            int value = Integer.parseInt(str);
            return Math.max(value, 0); // 确保不返回负数
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 审核请求参数
     */
    public static class AuditRequest {
        private Long barrageId;
        private String auditStatus;  // 1-通过 2-拒绝
        private String auditRemark;

        public Long getBarrageId() { return barrageId; }
        public void setBarrageId(Long barrageId) { this.barrageId = barrageId; }
        public String getAuditStatus() { return auditStatus; }
        public void setAuditStatus(String auditStatus) { this.auditStatus = auditStatus; }
        public String getAuditRemark() { return auditRemark; }
        public void setAuditRemark(String auditRemark) { this.auditRemark = auditRemark; }
    }

    /**
     * 批量拒绝请求参数
     */
    public static class BatchRejectRequest {
        private Long[] barrageIds;
        private String rejectReason;

        public Long[] getBarrageIds() { return barrageIds; }
        public void setBarrageIds(Long[] barrageIds) { this.barrageIds = barrageIds; }
        public String getRejectReason() { return rejectReason; }
        public void setRejectReason(String rejectReason) { this.rejectReason = rejectReason; }
    }

    /**
     * 弹幕配置信息
     */
    public static class BarrageConfig {
        private int rows;      // 弹幕行数
        private int speed;     // 弹幕滚动速度
        private int interval;  // 弹幕发送间隔

        public int getRows() { return rows; }
        public void setRows(int rows) { this.rows = rows; }
        public int getSpeed() { return speed; }
        public void setSpeed(int speed) { this.speed = speed; }
        public int getInterval() { return interval; }
        public void setInterval(int interval) { this.interval = interval; }
    }
}