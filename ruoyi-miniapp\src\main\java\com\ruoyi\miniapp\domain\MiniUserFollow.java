package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户关注对象 mini_user_follow
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@ApiModel("用户关注")
public class MiniUserFollow extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关注ID */
    @ApiModelProperty("关注ID")
    private Long followId;

    /** 关注者用户ID */
    @ApiModelProperty("关注者用户ID")
    @Excel(name = "关注者用户ID")
    private Long followerId;

    /** 被关注者用户ID */
    @ApiModelProperty("被关注者用户ID")
    @Excel(name = "被关注者用户ID")
    private Long followedId;

    /** 关注时间 */
    @ApiModelProperty("关注时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "关注时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date followTime;

    /** 状态（0正常 1取消关注） */
    @ApiModelProperty("状态（0正常 1取消关注）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=取消关注")
    private String status;

    // 关联查询字段
    /** 关注者信息 */
    @ApiModelProperty("关注者信息")
    private FollowerInfo followerInfo;

    /** 被关注者信息 */
    @ApiModelProperty("被关注者信息")
    private FollowedInfo followedInfo;

    /** 毕业院校 */
    @Excel(name = "毕业院校")
    private String graduateSchool;

    /** 毕业年份 */
    @Excel(name = "毕业年份")
    private String graduationYear;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    public String getCollege() {
        return college;
    }

    public void setCollege(String college) {
        this.college = college;
    }

    public String getMajor() {
        return major;
    }

    @Override
    public String toString() {
        return "MiniUserFollow{" +
                "followId=" + followId +
                ", followerId=" + followerId +
                ", followedId=" + followedId +
                ", followTime=" + followTime +
                ", status='" + status + '\'' +
                ", followerInfo=" + followerInfo +
                ", followedInfo=" + followedInfo +
                ", graduateSchool='" + graduateSchool + '\'' +
                ", graduationYear='" + graduationYear + '\'' +
                ", major='" + major + '\'' +
                ", college='" + college + '\'' +
                '}';
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getGraduationYear() {
        return graduationYear;
    }

    public void setGraduationYear(String graduationYear) {
        this.graduationYear = graduationYear;
    }

    public String getGraduateSchool() {
        return graduateSchool;
    }

    public void setGraduateSchool(String graduateSchool) {
        this.graduateSchool = graduateSchool;
    }

    /** 学院 */
    @Excel(name = "学院")
    private String college;

    public void setFollowId(Long followId) 
    {
        this.followId = followId;
    }

    public Long getFollowId() 
    {
        return followId;
    }
    public void setFollowerId(Long followerId) 
    {
        this.followerId = followerId;
    }

    public Long getFollowerId() 
    {
        return followerId;
    }
    public void setFollowedId(Long followedId) 
    {
        this.followedId = followedId;
    }

    public Long getFollowedId() 
    {
        return followedId;
    }
    public void setFollowTime(Date followTime) 
    {
        this.followTime = followTime;
    }

    public Date getFollowTime() 
    {
        return followTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public FollowerInfo getFollowerInfo() {
        return followerInfo;
    }

    public void setFollowerInfo(FollowerInfo followerInfo) {
        this.followerInfo = followerInfo;
    }

    public FollowedInfo getFollowedInfo() {
        return followedInfo;
    }

    public void setFollowedInfo(FollowedInfo followedInfo) {
        this.followedInfo = followedInfo;
    }

    /**
     * 关注者信息
     */
    @ApiModel("关注者信息")
    public static class FollowerInfo {
        @ApiModelProperty("用户ID")
        private Long userId;
        
        @ApiModelProperty("用户名")
        private String userName;
        
        @ApiModelProperty("昵称")
        private String nickName;
        
        @ApiModelProperty("头像")
        private String avatar;

        /** 形象照 */
        private String portraitUrl;
        
        @ApiModelProperty("真实姓名")
        private String realName;
        
        @ApiModelProperty("当前公司")
        private String currentCompany;
        
        @ApiModelProperty("职位")
        private String positionTitle;


        /** 毕业院校 */
        @Excel(name = "毕业院校")
        private String graduateSchool;

        /** 毕业年份 */
        @Excel(name = "毕业年份")
        private String graduationYear;

        /** 专业 */
        @Excel(name = "专业")
        private String major;

        /** 学院 */
        @Excel(name = "学院")
        private String college;

        // getter and setter methods
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getNickName() { return nickName; }
        public void setNickName(String nickName) { this.nickName = nickName; }
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getCurrentCompany() { return currentCompany; }
        public void setCurrentCompany(String currentCompany) { this.currentCompany = currentCompany; }
        public String getPositionTitle() { return positionTitle; }
        public void setPositionTitle(String positionTitle) { this.positionTitle = positionTitle; }

        public String getGraduateSchool() {
            return graduateSchool;
        }

        public void setGraduateSchool(String graduateSchool) {
            this.graduateSchool = graduateSchool;
        }

        public String getGraduationYear() {
            return graduationYear;
        }

        public void setGraduationYear(String graduationYear) {
            this.graduationYear = graduationYear;
        }

        public String getMajor() {
            return major;
        }

        public void setMajor(String major) {
            this.major = major;
        }

        public String getCollege() {
            return college;
        }

        public void setCollege(String college) {
            this.college = college;
        }

        public String getPortraitUrl() {
            return portraitUrl;
        }

        public void setPortraitUrl(String portraitUrl) {
            this.portraitUrl = portraitUrl;
        }
    }

    /**
     * 被关注者信息
     */
    @ApiModel("被关注者信息")
    public static class FollowedInfo {
        @ApiModelProperty("用户ID")
        private Long userId;
        
        @ApiModelProperty("用户名")
        private String userName;
        
        @ApiModelProperty("昵称")
        private String nickName;
        
        @ApiModelProperty("头像")
        private String avatar;

        /** 形象照 */
        private String portraitUrl;
        
        @ApiModelProperty("真实姓名")
        private String realName;
        
        @ApiModelProperty("当前公司")
        private String currentCompany;
        
        @ApiModelProperty("职位")
        private String positionTitle;
        
        @ApiModelProperty("粉丝数量")
        private Integer followerCount;
        
        @ApiModelProperty("是否互相关注")
        private Boolean isMutualFollow;

        /** 毕业院校 */
        @Excel(name = "毕业院校")
        private String graduateSchool;

        /** 毕业年份 */
        @Excel(name = "毕业年份")
        private String graduationYear;

        /** 专业 */
        @Excel(name = "专业")
        private String major;

        /** 学院 */
        @Excel(name = "学院")
        private String college;

        // getter and setter methods
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getNickName() { return nickName; }
        public void setNickName(String nickName) { this.nickName = nickName; }
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getCurrentCompany() { return currentCompany; }
        public void setCurrentCompany(String currentCompany) { this.currentCompany = currentCompany; }
        public String getPositionTitle() { return positionTitle; }
        public void setPositionTitle(String positionTitle) { this.positionTitle = positionTitle; }
        public Integer getFollowerCount() { return followerCount; }
        public void setFollowerCount(Integer followerCount) { this.followerCount = followerCount; }
        public Boolean getIsMutualFollow() { return isMutualFollow; }
        public void setIsMutualFollow(Boolean isMutualFollow) { this.isMutualFollow = isMutualFollow; }

        public String getGraduateSchool() {
            return graduateSchool;
        }

        public void setGraduateSchool(String graduateSchool) {
            this.graduateSchool = graduateSchool;
        }

        public String getGraduationYear() {
            return graduationYear;
        }

        public void setGraduationYear(String graduationYear) {
            this.graduationYear = graduationYear;
        }

        public String getMajor() {
            return major;
        }

        public void setMajor(String major) {
            this.major = major;
        }

        public String getCollege() {
            return college;
        }

        public void setCollege(String college) {
            this.college = college;
        }

        public String getPortraitUrl() {
            return portraitUrl;
        }

        public void setPortraitUrl(String portraitUrl) {
            this.portraitUrl = portraitUrl;
        }

        public Boolean getMutualFollow() {
            return isMutualFollow;
        }

        public void setMutualFollow(Boolean mutualFollow) {
            isMutualFollow = mutualFollow;
        }
    }
}
