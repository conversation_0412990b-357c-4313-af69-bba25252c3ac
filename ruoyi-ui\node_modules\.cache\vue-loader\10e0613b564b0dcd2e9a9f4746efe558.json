{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955748790}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkluZGV4IiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g54mI5pys5Y+3DQogICAgICB2ZXJzaW9uOiAiMy45LjAiLA0KICAgICAgLy8g57uf6K6h5pWw5o2uDQogICAgICB0b3RhbFVzZXJzOiAwLA0KICAgICAgYWN0aXZlVXNlcnM6IDAsDQogICAgICB0b3RhbEFjdGl2aXRpZXM6IDAsDQogICAgICB0b3RhbERlbWFuZHM6IDAsDQogICAgICB0b3RhbEV2ZW50czogMCwNCiAgICAgIHRvdGFsRW50ZXJwcmlzZXM6IDAsDQogICAgICB0b3RhbEpvYnM6IDAsDQogICAgICB0b3RhbEV4cGVydHM6IDAsDQogICAgICB0b3RhbE5ld3M6IDAsDQogICAgICB0b2RheU9wZXJhdGlvbnM6IDAsDQogICAgICAvLyDmnIDmlrDmlbDmja4NCiAgICAgIHJlY2VudEFjdGl2aXRpZXM6IFtdLA0KICAgICAgcmVjZW50RGVtYW5kczogW10sDQogICAgICAvLyDlm77ooajlrp7kvosNCiAgICAgIGJ1c2luZXNzQ2hhcnQ6IG51bGwsDQogICAgICB1c2VyVHJlbmRDaGFydDogbnVsbA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmxvYWREYXNoYm9hcmREYXRhKCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICB0aGlzLmluaXRDaGFydHMoKQ0KICAgICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyWDQogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpDQogICAgfSkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDnp7vpmaTkuovku7bnm5HlkKwNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpDQogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LDQogICAgaWYgKHRoaXMuYnVzaW5lc3NDaGFydCkgew0KICAgICAgdGhpcy5idXNpbmVzc0NoYXJ0LmRpc3Bvc2UoKQ0KICAgIH0NCiAgICBpZiAodGhpcy51c2VyVHJlbmRDaGFydCkgew0KICAgICAgdGhpcy51c2VyVHJlbmRDaGFydC5kaXNwb3NlKCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliqDovb3ku6rooajnm5jmlbDmja4NCiAgICBhc3luYyBsb2FkRGFzaGJvYXJkRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPlue7n+iuoeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRTdGF0aXN0aWNzKCkNCiAgICAgICAgLy8g6I635Y+W5pyA5paw5rS75YqoDQogICAgICAgIGF3YWl0IHRoaXMubG9hZFJlY2VudEFjdGl2aXRpZXMoKQ0KICAgICAgICAvLyDojrflj5bmnIDmlrDpnIDmsYINCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkUmVjZW50RGVtYW5kcygpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3ku6rooajnm5jmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIC8vIOS9v+eUqOm7mOiupOaVsOaNrg0KICAgICAgICB0aGlzLnNldERlZmF1bHREYXRhKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L2957uf6K6h5pWw5o2uDQogICAgYXN5bmMgbG9hZFN0YXRpc3RpY3MoKSB7DQogICAgICAvLyDov5nph4zlj6/ku6XosIPnlKjlkI7nq69BUEnojrflj5bnu5/orqHmlbDmja4NCiAgICAgIC8vIOaaguaXtuS9v+eUqOS7juaVsOaNruW6k+afpeivouWIsOeahOecn+WunuaVsOaNrg0KICAgICAgdGhpcy50b3RhbFVzZXJzID0gNg0KICAgICAgdGhpcy5hY3RpdmVVc2VycyA9IDQgLy8g5rS76LeD55So5oi35pWw77yI5pyA6L+RN+WkqeeZu+W9leeahOeUqOaIt++8iQ0KICAgICAgdGhpcy50b3RhbEFjdGl2aXRpZXMgPSAyDQogICAgICB0aGlzLnRvdGFsRGVtYW5kcyA9IDINCiAgICAgIHRoaXMudG90YWxFdmVudHMgPSA0DQogICAgICB0aGlzLnRvdGFsRW50ZXJwcmlzZXMgPSAyDQogICAgICB0aGlzLnRvdGFsSm9icyA9IDgNCiAgICAgIHRoaXMudG90YWxFeHBlcnRzID0gNA0KICAgICAgdGhpcy50b3RhbE5ld3MgPSAyDQogICAgICB0aGlzLnRvZGF5T3BlcmF0aW9ucyA9IDM2DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veacgOaWsOa0u+WKqA0KICAgIGFzeW5jIGxvYWRSZWNlbnRBY3Rpdml0aWVzKCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55So5ZCO56uvQVBJ6I635Y+W5pyA5paw5rS75YqoDQogICAgICB0aGlzLnJlY2VudEFjdGl2aXRpZXMgPSBbDQogICAgICAgIHsgaWQ6IDEsIHRpdGxlOiAi5rWL6K+V6aG1IiwgY3JlYXRlX3RpbWU6ICIyMDI1LTA3LTE4VDAxOjM5OjU4LjAwMFoiIH0sDQogICAgICAgIHsgaWQ6IDIsIHRpdGxlOiAi5rGf6KW/5a6H5oKm56eR5oqA5pyJ6ZmQ5YWs5Y+4IiwgY3JlYXRlX3RpbWU6ICIyMDI1LTA3LTE2VDAzOjE0OjI1LjAwMFoiIH0NCiAgICAgIF0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L295pyA5paw6ZyA5rGCDQogICAgYXN5bmMgbG9hZFJlY2VudERlbWFuZHMoKSB7DQogICAgICAvLyDov5nph4zlj6/ku6XosIPnlKjlkI7nq69BUEnojrflj5bmnIDmlrDpnIDmsYINCiAgICAgIHRoaXMucmVjZW50RGVtYW5kcyA9IFsNCiAgICAgICAgeyBpZDogMSwgZGVtYW5kX3RpdGxlOiAi5a+755CD5a+75rGC5a+75rGCIiwgY3JlYXRlX3RpbWU6ICIyMDI1LTA3LTMwVDA3OjQzOjAwLjAwMFoiIH0sDQogICAgICAgIHsgaWQ6IDIsIGRlbWFuZF90aXRsZTogIuaIkeaYr+Wkj+Wuh+adsCIsIGNyZWF0ZV90aW1lOiAiMjAyNS0wNy0zMFQwNjo1NTo0NS4wMDBaIiB9DQogICAgICBdDQogICAgfSwNCg0KICAgIC8vIOiuvue9rum7mOiupOaVsOaNrg0KICAgIHNldERlZmF1bHREYXRhKCkgew0KICAgICAgdGhpcy50b3RhbFVzZXJzID0gMA0KICAgICAgdGhpcy5hY3RpdmVVc2VycyA9IDANCiAgICAgIHRoaXMudG90YWxBY3Rpdml0aWVzID0gMA0KICAgICAgdGhpcy50b3RhbERlbWFuZHMgPSAwDQogICAgICB0aGlzLnRvdGFsRXZlbnRzID0gMA0KICAgICAgdGhpcy50b3RhbEVudGVycHJpc2VzID0gMA0KICAgICAgdGhpcy50b3RhbEpvYnMgPSAwDQogICAgICB0aGlzLnRvdGFsRXhwZXJ0cyA9IDANCiAgICAgIHRoaXMudG90YWxOZXdzID0gMA0KICAgICAgdGhpcy50b2RheU9wZXJhdGlvbnMgPSAwDQogICAgICB0aGlzLnJlY2VudEFjdGl2aXRpZXMgPSBbXQ0KICAgICAgdGhpcy5yZWNlbnREZW1hbmRzID0gW10NCiAgICB9LA0KDQogICAgLy8g5Yid5aeL5YyW5Zu+6KGoDQogICAgaW5pdENoYXJ0cygpIHsNCiAgICAgIHRoaXMuaW5pdEJ1c2luZXNzQ2hhcnQoKQ0KICAgICAgdGhpcy5pbml0VXNlclRyZW5kQ2hhcnQoKQ0KICAgIH0sDQoNCiAgICAvLyDliJ3lp4vljJbkuJrliqHmlbDmja7liIbluIPppbzlm74NCiAgICBpbml0QnVzaW5lc3NDaGFydCgpIHsNCiAgICAgIGlmICghdGhpcy4kcmVmcy5idXNpbmVzc0NoYXJ0KSByZXR1cm4NCg0KICAgICAgdGhpcy5idXNpbmVzc0NoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuYnVzaW5lc3NDaGFydCkNCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6ICfkuJrliqHmqKHlnZfliIbluIMnLA0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgZm9udFNpemU6IDE0LA0KICAgICAgICAgICAgY29sb3I6ICcjMzMzJw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdpdGVtJywNCiAgICAgICAgICBmb3JtYXR0ZXI6ICd7YX0gPGJyLz57Yn06IHtjfSAoe2R9JSknDQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGJvdHRvbTogJzUlJywNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+S4muWKoeaVsOaNricsDQogICAgICAgICAgICB0eXBlOiAncGllJywNCiAgICAgICAgICAgIHJhZGl1czogWyc0MCUnLCAnNzAlJ10sDQogICAgICAgICAgICBjZW50ZXI6IFsnNTAlJywgJzQ1JSddLA0KICAgICAgICAgICAgYXZvaWRMYWJlbE92ZXJsYXA6IGZhbHNlLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMTAsDQogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgICAgIHBvc2l0aW9uOiAnY2VudGVyJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGVtcGhhc2lzOiB7DQogICAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2JywNCiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IFsNCiAgICAgICAgICAgICAgeyB2YWx1ZTogdGhpcy50b3RhbEFjdGl2aXRpZXMsIG5hbWU6ICfnsr7lvanmtLvliqgnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjNDA5ZWZmJyB9IH0sDQogICAgICAgICAgICAgIHsgdmFsdWU6IHRoaXMudG90YWxEZW1hbmRzLCBuYW1lOiAn6ZyA5rGC5a+55o6lJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzY3YzIzYScgfSB9LA0KICAgICAgICAgICAgICB7IHZhbHVlOiB0aGlzLnRvdGFsRXZlbnRzLCBuYW1lOiAn5rS75Yqo5oql5ZCNJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI2U2YTIzYycgfSB9LA0KICAgICAgICAgICAgICB7IHZhbHVlOiB0aGlzLnRvdGFsRXhwZXJ0cywgbmFtZTogJ+S4k+WutuW6kycsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNmNTZjNmMnIH0gfSwNCiAgICAgICAgICAgICAgeyB2YWx1ZTogdGhpcy50b3RhbEpvYnMsIG5hbWU6ICfmi5vogZjogYzkvY0nLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTA5Mzk5JyB9IH0sDQogICAgICAgICAgICAgIHsgdmFsdWU6IHRoaXMudG90YWxOZXdzLCBuYW1lOiAn5paw6Ze76LWE6K6vJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzYwNjI2NicgfSB9DQogICAgICAgICAgICBdDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQoNCiAgICAgIHRoaXMuYnVzaW5lc3NDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDliJ3lp4vljJbnlKjmiLfmtLvot4Pluqbotovlir/lm74NCiAgICBpbml0VXNlclRyZW5kQ2hhcnQoKSB7DQogICAgICBpZiAoIXRoaXMuJHJlZnMudXNlclRyZW5kQ2hhcnQpIHJldHVybg0KDQogICAgICB0aGlzLnVzZXJUcmVuZENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMudXNlclRyZW5kQ2hhcnQpDQoNCiAgICAgIC8vIOaooeaLn+acgOi/kTflpKnnmoTmlbDmja4NCiAgICAgIGNvbnN0IGRhdGVzID0gW10NCiAgICAgIGNvbnN0IGFjdGl2ZURhdGEgPSBbXQ0KICAgICAgY29uc3QgdG90YWxEYXRhID0gW10NCg0KICAgICAgZm9yIChsZXQgaSA9IDY7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpDQogICAgICAgIGRhdGVzLnB1c2goZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJywgeyBtb250aDogJzItZGlnaXQnLCBkYXk6ICcyLWRpZ2l0JyB9KSkNCiAgICAgICAgYWN0aXZlRGF0YS5wdXNoKE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUpICsgMikgLy8gMi025LmL6Ze055qE6ZqP5py65pWwDQogICAgICAgIHRvdGFsRGF0YS5wdXNoKE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDMpICsgNikgLy8gNi045LmL6Ze055qE6ZqP5py65pWwDQogICAgICB9DQoNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgdGl0bGU6IHsNCiAgICAgICAgICB0ZXh0OiAn55So5oi35rS76LeD5bqm77yI5pyA6L+RN+Wkqe+8iScsDQogICAgICAgICAgbGVmdDogJ2NlbnRlcicsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTQsDQogICAgICAgICAgICBjb2xvcjogJyMzMzMnDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnDQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGJvdHRvbTogJzUlJywNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMTUlJywNCiAgICAgICAgICB0b3A6ICcyMCUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgIGRhdGE6IGRhdGVzLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgZm9udFNpemU6IDExDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTENCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfmtLvot4PnlKjmiLcnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjNDA5ZWZmJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM0MDllZmYnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiB7DQogICAgICAgICAgICAgICAgdHlwZTogJ2xpbmVhcicsDQogICAgICAgICAgICAgICAgeDogMCwNCiAgICAgICAgICAgICAgICB5OiAwLA0KICAgICAgICAgICAgICAgIHgyOiAwLA0KICAgICAgICAgICAgICAgIHkyOiAxLA0KICAgICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFt7DQogICAgICAgICAgICAgICAgICBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSg2NCwgMTU4LCAyNTUsIDAuMyknDQogICAgICAgICAgICAgICAgfSwgew0KICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjEpJw0KICAgICAgICAgICAgICAgIH1dDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBhY3RpdmVEYXRhDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5oC755So5oi3JywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM2N2MyM2EnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzY3YzIzYScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiB0b3RhbERhdGENCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCg0KICAgICAgdGhpcy51c2VyVHJlbmRDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbml6XmnJ8NCiAgICBmb3JtYXREYXRlKGRhdGVTdHJpbmcpIHsNCiAgICAgIGlmICghZGF0ZVN0cmluZykgcmV0dXJuICcnDQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZykNCiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIHllYXI6ICdudW1lcmljJywNCiAgICAgICAgbW9udGg6ICcyLWRpZ2l0JywNCiAgICAgICAgZGF5OiAnMi1kaWdpdCcNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluezu+e7n+i/kOihjOaXtumXtA0KICAgIGdldFVwdGltZSgpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCkNCiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IG5ldyBEYXRlKCcyMDI1LTAxLTAxJykgLy8g5YGH6K6+57O757uf5ZCv5Yqo5pe26Ze0DQogICAgICBjb25zdCBkaWZmID0gbm93IC0gc3RhcnRUaW1lDQogICAgICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcihkaWZmIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKQ0KICAgICAgcmV0dXJuIGAke2RheXN9IOWkqWANCiAgICB9LA0KDQogICAgLy8g6aG16Z2i6Lez6L2sDQogICAgZ29Ub1BhZ2UocGF0aCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2gocGF0aCkNCiAgICB9LA0KDQogICAgLy8g5aSW6YOo6ZO+5o6l6Lez6L2sDQogICAgZ29UYXJnZXQoaHJlZikgew0KICAgICAgd2luZG93Lm9wZW4oaHJlZiwgIl9ibGFuayIpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"app-container home\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <h1 class=\"banner-title\">\r\n          <i class=\"el-icon-star-on\"></i>\r\n          天津大学海棠小程序管理后台\r\n        </h1>\r\n        <p class=\"banner-subtitle\">智慧校园 · 创新服务 · 数据驱动</p>\r\n        <div class=\"banner-stats\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ totalUsers }}</div>\r\n            <div class=\"stat-label\">系统用户</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ activeUsers }}</div>\r\n            <div class=\"stat-label\">活跃用户</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ totalEnterprises }}</div>\r\n            <div class=\"stat-label\">入驻企业</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ todayOperations }}</div>\r\n            <div class=\"stat-label\">今日操作</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card activities\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-star-on\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalActivities }}</div>\r\n            <div class=\"stat-title\">精彩活动</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card demands\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-connection\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalDemands }}</div>\r\n            <div class=\"stat-title\">需求对接</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card events\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-tickets\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalEvents }}</div>\r\n            <div class=\"stat-title\">活动报名</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card experts\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-user\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalExperts }}</div>\r\n            <div class=\"stat-title\">专家库</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表展示区域 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <!-- 业务数据分布饼图 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-pie-chart\"></i> 业务数据分布</span>\r\n          </div>\r\n          <div class=\"chart-container\">\r\n            <div ref=\"businessChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 用户活跃度趋势图 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-data-line\"></i> 用户活跃度趋势</span>\r\n          </div>\r\n          <div class=\"chart-container\">\r\n            <div ref=\"userTrendChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 最新活动 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-star-on\"></i> 最新活动</span>\r\n            <el-button type=\"text\" size=\"small\" @click=\"goToPage('/miniapp/activity')\">查看更多</el-button>\r\n          </div>\r\n          <div class=\"activity-list\">\r\n            <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"activity-item\">\r\n              <div class=\"activity-title\">{{ activity.title }}</div>\r\n              <div class=\"activity-time\">{{ formatDate(activity.create_time) }}</div>\r\n            </div>\r\n            <div v-if=\"recentActivities.length === 0\" class=\"empty-data\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <p>暂无活动数据</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 最新需求 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-connection\"></i> 最新需求</span>\r\n            <el-button type=\"text\" size=\"small\" @click=\"goToPage('/miniapp/demand')\">查看更多</el-button>\r\n          </div>\r\n          <div class=\"demand-list\">\r\n            <div v-for=\"demand in recentDemands\" :key=\"demand.id\" class=\"demand-item\">\r\n              <div class=\"demand-title\">{{ demand.demand_title }}</div>\r\n              <div class=\"demand-time\">{{ formatDate(demand.create_time) }}</div>\r\n            </div>\r\n            <div v-if=\"recentDemands.length === 0\" class=\"empty-data\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <p>暂无需求数据</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 系统状态 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-monitor\"></i> 系统状态</span>\r\n          </div>\r\n          <div class=\"system-status\">\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon online\">\r\n                <i class=\"el-icon-success\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">系统状态</div>\r\n                <div class=\"status-value\">运行正常</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon\">\r\n                <i class=\"el-icon-time\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">运行时间</div>\r\n                <div class=\"status-value\">{{ getUptime() }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon\">\r\n                <i class=\"el-icon-view\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">活跃用户</div>\r\n                <div class=\"status-value\">{{ activeUsers }} 人</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 快捷操作 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-s-operation\"></i> 快捷操作</span>\r\n          </div>\r\n          <div class=\"quick-actions\">\r\n            <div class=\"action-group\">\r\n              <h4>内容管理</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/activity')\">\r\n                  新增活动\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/banner')\">\r\n                  新增轮播图\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/notice')\">\r\n                  新增通知\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/news')\">\r\n                  新增新闻\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"action-group\">\r\n              <h4>用户服务</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/demand')\">\r\n                  发布需求\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/event')\">\r\n                  创建活动\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/job')\">\r\n                  发布职位\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/expert')\">\r\n                  添加专家\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"action-group\">\r\n              <h4>系统管理</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-user\" size=\"small\" @click=\"goToPage('/system/user')\">\r\n                  用户管理\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-setting\" size=\"small\" @click=\"goToPage('/system/menu')\">\r\n                  菜单管理\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-view\" size=\"small\" @click=\"goToPage('/monitor/operlog')\">\r\n                  操作日志\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-monitor\" size=\"small\" @click=\"goToPage('/monitor/server')\">\r\n                  服务监控\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: \"Index\",\r\n  data() {\r\n    return {\r\n      // 版本号\r\n      version: \"3.9.0\",\r\n      // 统计数据\r\n      totalUsers: 0,\r\n      activeUsers: 0,\r\n      totalActivities: 0,\r\n      totalDemands: 0,\r\n      totalEvents: 0,\r\n      totalEnterprises: 0,\r\n      totalJobs: 0,\r\n      totalExperts: 0,\r\n      totalNews: 0,\r\n      todayOperations: 0,\r\n      // 最新数据\r\n      recentActivities: [],\r\n      recentDemands: [],\r\n      // 图表实例\r\n      businessChart: null,\r\n      userTrendChart: null\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDashboardData()\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initCharts()\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', this.handleResize)\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.handleResize)\r\n    // 销毁图表实例\r\n    if (this.businessChart) {\r\n      this.businessChart.dispose()\r\n    }\r\n    if (this.userTrendChart) {\r\n      this.userTrendChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载仪表盘数据\r\n    async loadDashboardData() {\r\n      try {\r\n        // 获取统计数据\r\n        await this.loadStatistics()\r\n        // 获取最新活动\r\n        await this.loadRecentActivities()\r\n        // 获取最新需求\r\n        await this.loadRecentDemands()\r\n      } catch (error) {\r\n        console.error('加载仪表盘数据失败:', error)\r\n        // 使用默认数据\r\n        this.setDefaultData()\r\n      }\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      // 这里可以调用后端API获取统计数据\r\n      // 暂时使用从数据库查询到的真实数据\r\n      this.totalUsers = 6\r\n      this.activeUsers = 4 // 活跃用户数（最近7天登录的用户）\r\n      this.totalActivities = 2\r\n      this.totalDemands = 2\r\n      this.totalEvents = 4\r\n      this.totalEnterprises = 2\r\n      this.totalJobs = 8\r\n      this.totalExperts = 4\r\n      this.totalNews = 2\r\n      this.todayOperations = 36\r\n    },\r\n\r\n    // 加载最新活动\r\n    async loadRecentActivities() {\r\n      // 这里可以调用后端API获取最新活动\r\n      this.recentActivities = [\r\n        { id: 1, title: \"测试页\", create_time: \"2025-07-18T01:39:58.000Z\" },\r\n        { id: 2, title: \"江西宇悦科技有限公司\", create_time: \"2025-07-16T03:14:25.000Z\" }\r\n      ]\r\n    },\r\n\r\n    // 加载最新需求\r\n    async loadRecentDemands() {\r\n      // 这里可以调用后端API获取最新需求\r\n      this.recentDemands = [\r\n        { id: 1, demand_title: \"寻球寻求寻求\", create_time: \"2025-07-30T07:43:00.000Z\" },\r\n        { id: 2, demand_title: \"我是夏宇杰\", create_time: \"2025-07-30T06:55:45.000Z\" }\r\n      ]\r\n    },\r\n\r\n    // 设置默认数据\r\n    setDefaultData() {\r\n      this.totalUsers = 0\r\n      this.activeUsers = 0\r\n      this.totalActivities = 0\r\n      this.totalDemands = 0\r\n      this.totalEvents = 0\r\n      this.totalEnterprises = 0\r\n      this.totalJobs = 0\r\n      this.totalExperts = 0\r\n      this.totalNews = 0\r\n      this.todayOperations = 0\r\n      this.recentActivities = []\r\n      this.recentDemands = []\r\n    },\r\n\r\n    // 初始化图表\r\n    initCharts() {\r\n      this.initBusinessChart()\r\n      this.initUserTrendChart()\r\n    },\r\n\r\n    // 初始化业务数据分布饼图\r\n    initBusinessChart() {\r\n      if (!this.$refs.businessChart) return\r\n\r\n      this.businessChart = echarts.init(this.$refs.businessChart)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '业务模块分布',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          bottom: '5%',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '业务数据',\r\n            type: 'pie',\r\n            radius: ['40%', '70%'],\r\n            center: ['50%', '45%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: 10,\r\n              borderColor: '#fff',\r\n              borderWidth: 2\r\n            },\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '16',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              { value: this.totalActivities, name: '精彩活动', itemStyle: { color: '#409eff' } },\r\n              { value: this.totalDemands, name: '需求对接', itemStyle: { color: '#67c23a' } },\r\n              { value: this.totalEvents, name: '活动报名', itemStyle: { color: '#e6a23c' } },\r\n              { value: this.totalExperts, name: '专家库', itemStyle: { color: '#f56c6c' } },\r\n              { value: this.totalJobs, name: '招聘职位', itemStyle: { color: '#909399' } },\r\n              { value: this.totalNews, name: '新闻资讯', itemStyle: { color: '#606266' } }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.businessChart.setOption(option)\r\n    },\r\n\r\n    // 初始化用户活跃度趋势图\r\n    initUserTrendChart() {\r\n      if (!this.$refs.userTrendChart) return\r\n\r\n      this.userTrendChart = echarts.init(this.$refs.userTrendChart)\r\n\r\n      // 模拟最近7天的数据\r\n      const dates = []\r\n      const activeData = []\r\n      const totalData = []\r\n\r\n      for (let i = 6; i >= 0; i--) {\r\n        const date = new Date()\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))\r\n        activeData.push(Math.floor(Math.random() * 5) + 2) // 2-6之间的随机数\r\n        totalData.push(Math.floor(Math.random() * 3) + 6) // 6-8之间的随机数\r\n      }\r\n\r\n      const option = {\r\n        title: {\r\n          text: '用户活跃度（最近7天）',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          bottom: '5%',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '15%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 11\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 11\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '活跃用户',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            smooth: true,\r\n            lineStyle: {\r\n              color: '#409eff'\r\n            },\r\n            itemStyle: {\r\n              color: '#409eff'\r\n            },\r\n            areaStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: 'rgba(64, 158, 255, 0.3)'\r\n                }, {\r\n                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'\r\n                }]\r\n              }\r\n            },\r\n            data: activeData\r\n          },\r\n          {\r\n            name: '总用户',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: {\r\n              color: '#67c23a'\r\n            },\r\n            itemStyle: {\r\n              color: '#67c23a'\r\n            },\r\n            data: totalData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.userTrendChart.setOption(option)\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 获取系统运行时间\r\n    getUptime() {\r\n      const now = new Date()\r\n      const startTime = new Date('2025-01-01') // 假设系统启动时间\r\n      const diff = now - startTime\r\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n      return `${days} 天`\r\n    },\r\n\r\n    // 页面跳转\r\n    goToPage(path) {\r\n      this.$router.push(path)\r\n    },\r\n\r\n    // 外部链接跳转\r\n    goTarget(href) {\r\n      window.open(href, \"_blank\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  // 欢迎横幅样式\r\n  .welcome-banner {\r\n    background: #409eff;\r\n    border-radius: 8px;\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n    color: white;\r\n    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);\r\n\r\n    .banner-title {\r\n      font-size: 28px;\r\n      font-weight: 600;\r\n      margin: 0 0 8px 0;\r\n\r\n      i {\r\n        color: #ffd700;\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .banner-subtitle {\r\n      font-size: 14px;\r\n      opacity: 0.9;\r\n      margin: 0 0 25px 0;\r\n    }\r\n\r\n    .banner-stats {\r\n      display: flex;\r\n      gap: 30px;\r\n      flex-wrap: wrap;\r\n\r\n      .stat-item {\r\n        text-align: center;\r\n\r\n        .stat-number {\r\n          font-size: 24px;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 12px;\r\n          opacity: 0.8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 统计卡片样式\r\n  .stats-row {\r\n    margin-bottom: 20px;\r\n\r\n    .stat-card {\r\n      background: white;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n      transition: all 0.3s ease;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n      }\r\n\r\n      .stat-icon {\r\n        width: 60px;\r\n        height: 60px;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 15px;\r\n\r\n        i {\r\n          font-size: 24px;\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-info {\r\n        flex: 1;\r\n\r\n        .stat-value {\r\n          font-size: 24px;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n          margin-bottom: 5px;\r\n        }\r\n\r\n        .stat-title {\r\n          font-size: 14px;\r\n          color: #666;\r\n        }\r\n      }\r\n\r\n      &.activities {\r\n        .stat-icon {\r\n          background: #409eff;\r\n        }\r\n        .stat-value {\r\n          color: #409eff;\r\n        }\r\n      }\r\n\r\n      &.demands {\r\n        .stat-icon {\r\n          background: #67c23a;\r\n        }\r\n        .stat-value {\r\n          color: #67c23a;\r\n        }\r\n      }\r\n\r\n      &.events {\r\n        .stat-icon {\r\n          background: #e6a23c;\r\n        }\r\n        .stat-value {\r\n          color: #e6a23c;\r\n        }\r\n      }\r\n\r\n      &.experts {\r\n        .stat-icon {\r\n          background: #f56c6c;\r\n        }\r\n        .stat-value {\r\n          color: #f56c6c;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 图表展示区域样式\r\n  .charts-row {\r\n    margin-bottom: 20px;\r\n\r\n    .chart-container {\r\n      padding: 10px 0;\r\n\r\n      .chart {\r\n        width: 100%;\r\n        height: 300px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域样式\r\n  .main-content {\r\n    margin-bottom: 20px;\r\n\r\n    .content-card {\r\n      height: 100%;\r\n      box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n      border-radius: 8px;\r\n\r\n      .card-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        font-weight: 600;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n          color: #409eff;\r\n        }\r\n      }\r\n\r\n      // 活动列表样式\r\n      .activity-list, .demand-list {\r\n        .activity-item, .demand-item {\r\n          padding: 12px 0;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .activity-title, .demand-title {\r\n            font-size: 14px;\r\n            color: #333;\r\n            margin-bottom: 5px;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n\r\n          .activity-time, .demand-time {\r\n            font-size: 12px;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 系统状态样式\r\n      .system-status {\r\n        .status-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 15px 0;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .status-icon {\r\n            width: 40px;\r\n            height: 40px;\r\n            border-radius: 8px;\r\n            background: #f5f7fa;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 15px;\r\n\r\n            i {\r\n              font-size: 18px;\r\n              color: #909399;\r\n            }\r\n\r\n            &.online i {\r\n              color: #67c23a;\r\n            }\r\n          }\r\n\r\n          .status-info {\r\n            flex: 1;\r\n\r\n            .status-label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 4px;\r\n            }\r\n\r\n            .status-value {\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 1;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 空数据样式\r\n      .empty-data {\r\n        text-align: center;\r\n        padding: 40px 0;\r\n        color: #999;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          margin-bottom: 10px;\r\n          display: block;\r\n        }\r\n\r\n        p {\r\n          margin: 0;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 快捷操作样式\r\n  .quick-actions {\r\n    .action-group {\r\n      margin-bottom: 30px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      h4 {\r\n        margin: 0 0 15px 0;\r\n        font-size: 16px;\r\n        color: #333;\r\n        font-weight: 600;\r\n        border-left: 4px solid #409eff;\r\n        padding-left: 10px;\r\n      }\r\n\r\n      .action-buttons {\r\n        display: flex;\r\n        gap: 10px;\r\n        flex-wrap: wrap;\r\n\r\n        .el-button {\r\n          border-radius: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 响应式设计\r\n  @media (max-width: 768px) {\r\n    padding: 10px;\r\n\r\n    .welcome-banner {\r\n      padding: 20px;\r\n\r\n      .banner-title {\r\n        font-size: 22px;\r\n      }\r\n\r\n      .banner-stats {\r\n        gap: 20px;\r\n\r\n        .stat-item {\r\n          .stat-number {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .stats-row {\r\n      .stat-card {\r\n        padding: 15px;\r\n\r\n        .stat-icon {\r\n          width: 50px;\r\n          height: 50px;\r\n\r\n          i {\r\n            font-size: 20px;\r\n          }\r\n        }\r\n\r\n        .stat-info {\r\n          .stat-value {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .charts-row {\r\n      .chart-container {\r\n        .chart {\r\n          height: 250px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .quick-actions {\r\n      .action-group {\r\n        .action-buttons {\r\n          .el-button {\r\n            font-size: 12px;\r\n            padding: 6px 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>"]}]}