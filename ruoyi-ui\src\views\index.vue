<template>
  <div class="app-container home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="banner-title">
          <i class="el-icon-star-on"></i>
          天津大学海棠小程序管理后台
        </h1>
        <p class="banner-subtitle">智慧校园 · 创新服务 · 数据驱动</p>
        <div class="banner-stats">
          <div class="stat-item">
            <div class="stat-number">{{ totalUsers }}</div>
            <div class="stat-label">系统用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ totalEnterprises }}</div>
            <div class="stat-label">入驻企业</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ todayOperations }}</div>
            <div class="stat-label">今日操作</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card activities">
          <div class="stat-icon">
            <i class="el-icon-star-on"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalActivities }}</div>
            <div class="stat-title">精彩活动</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card demands">
          <div class="stat-icon">
            <i class="el-icon-connection"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalDemands }}</div>
            <div class="stat-title">需求对接</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card events">
          <div class="stat-icon">
            <i class="el-icon-tickets"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalEvents }}</div>
            <div class="stat-title">活动报名</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card experts">
          <div class="stat-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalExperts }}</div>
            <div class="stat-title">专家库</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表展示区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 业务数据分布饼图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-pie-chart"></i> 业务数据分布</span>
          </div>
          <div class="chart-container">
            <div ref="businessChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>

      <!-- 用户活跃度趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-data-line"></i> 用户活跃度趋势</span>
          </div>
          <div class="chart-container">
            <div ref="userTrendChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 最新活动 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-star-on"></i> 最新活动</span>
            <el-button type="text" size="small" @click="goToPage('/miniapp/activity')">查看更多</el-button>
          </div>
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ formatDate(activity.create_time) }}</div>
            </div>
            <div v-if="recentActivities.length === 0" class="empty-data">
              <i class="el-icon-document"></i>
              <p>暂无活动数据</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最新需求 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-connection"></i> 最新需求</span>
            <el-button type="text" size="small" @click="goToPage('/miniapp/demand')">查看更多</el-button>
          </div>
          <div class="demand-list">
            <div v-for="demand in recentDemands" :key="demand.id" class="demand-item">
              <div class="demand-title">{{ demand.demand_title }}</div>
              <div class="demand-time">{{ formatDate(demand.create_time) }}</div>
            </div>
            <div v-if="recentDemands.length === 0" class="empty-data">
              <i class="el-icon-document"></i>
              <p>暂无需求数据</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 系统状态 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-monitor"></i> 系统状态</span>
          </div>
          <div class="system-status">
            <div class="status-item">
              <div class="status-icon online">
                <i class="el-icon-success"></i>
              </div>
              <div class="status-info">
                <div class="status-label">系统状态</div>
                <div class="status-value">运行正常</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="status-info">
                <div class="status-label">运行时间</div>
                <div class="status-value">{{ getUptime() }}</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon">
                <i class="el-icon-view"></i>
              </div>
              <div class="status-info">
                <div class="status-label">活跃用户</div>
                <div class="status-value">{{ activeUsers }} 人</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" class="main-content">
      <el-col :xs="24" :sm="24" :md="24" :lg="24">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-s-operation"></i> 快捷操作</span>
          </div>
          <div class="quick-actions">
            <div class="action-group">
              <h4>内容管理</h4>
              <div class="action-buttons">
                <el-button type="primary" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/activity')">
                  新增活动
                </el-button>
                <el-button type="success" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/banner')">
                  新增轮播图
                </el-button>
                <el-button type="info" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/notice')">
                  新增通知
                </el-button>
                <el-button type="warning" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/news')">
                  新增新闻
                </el-button>
              </div>
            </div>
            <div class="action-group">
              <h4>用户服务</h4>
              <div class="action-buttons">
                <el-button type="primary" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/demand')">
                  发布需求
                </el-button>
                <el-button type="success" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/event')">
                  创建活动
                </el-button>
                <el-button type="info" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/job')">
                  发布职位
                </el-button>
                <el-button type="warning" icon="el-icon-plus" size="small" @click="goToPage('/miniapp/expert')">
                  添加专家
                </el-button>
              </div>
            </div>
            <div class="action-group">
              <h4>系统管理</h4>
              <div class="action-buttons">
                <el-button type="primary" icon="el-icon-user" size="small" @click="goToPage('/system/user')">
                  用户管理
                </el-button>
                <el-button type="success" icon="el-icon-setting" size="small" @click="goToPage('/system/menu')">
                  菜单管理
                </el-button>
                <el-button type="info" icon="el-icon-view" size="small" @click="goToPage('/monitor/operlog')">
                  操作日志
                </el-button>
                <el-button type="warning" icon="el-icon-monitor" size="small" @click="goToPage('/monitor/server')">
                  服务监控
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.9.0",
      // 统计数据
      totalUsers: 0,
      activeUsers: 0,
      totalActivities: 0,
      totalDemands: 0,
      totalEvents: 0,
      totalEnterprises: 0,
      totalJobs: 0,
      totalExperts: 0,
      totalNews: 0,
      todayOperations: 0,
      // 最新数据
      recentActivities: [],
      recentDemands: [],
      // 图表实例
      businessChart: null,
      userTrendChart: null
    }
  },
  created() {
    this.loadDashboardData()
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    })
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
    // 销毁图表实例
    if (this.businessChart) {
      this.businessChart.dispose()
    }
    if (this.userTrendChart) {
      this.userTrendChart.dispose()
    }
  },
  methods: {
    // 加载仪表盘数据
    async loadDashboardData() {
      try {
        // 获取统计数据
        await this.loadStatistics()
        // 获取最新活动
        await this.loadRecentActivities()
        // 获取最新需求
        await this.loadRecentDemands()
      } catch (error) {
        console.error('加载仪表盘数据失败:', error)
        // 使用默认数据
        this.setDefaultData()
      }
    },

    // 加载统计数据
    async loadStatistics() {
      // 这里可以调用后端API获取统计数据
      // 暂时使用从数据库查询到的真实数据
      this.totalUsers = 6
      this.activeUsers = 4 // 活跃用户数（最近7天登录的用户）
      this.totalActivities = 2
      this.totalDemands = 2
      this.totalEvents = 4
      this.totalEnterprises = 2
      this.totalJobs = 8
      this.totalExperts = 4
      this.totalNews = 2
      this.todayOperations = 36
    },

    // 加载最新活动
    async loadRecentActivities() {
      // 这里可以调用后端API获取最新活动
      this.recentActivities = [
        { id: 1, title: "测试页", create_time: "2025-07-18T01:39:58.000Z" },
        { id: 2, title: "江西宇悦科技有限公司", create_time: "2025-07-16T03:14:25.000Z" }
      ]
    },

    // 加载最新需求
    async loadRecentDemands() {
      // 这里可以调用后端API获取最新需求
      this.recentDemands = [
        { id: 1, demand_title: "寻球寻求寻求", create_time: "2025-07-30T07:43:00.000Z" },
        { id: 2, demand_title: "我是夏宇杰", create_time: "2025-07-30T06:55:45.000Z" }
      ]
    },

    // 设置默认数据
    setDefaultData() {
      this.totalUsers = 0
      this.activeUsers = 0
      this.totalActivities = 0
      this.totalDemands = 0
      this.totalEvents = 0
      this.totalEnterprises = 0
      this.totalJobs = 0
      this.totalExperts = 0
      this.totalNews = 0
      this.todayOperations = 0
      this.recentActivities = []
      this.recentDemands = []
    },

    // 初始化图表
    initCharts() {
      this.initBusinessChart()
      this.initUserTrendChart()
    },

    // 初始化业务数据分布饼图
    initBusinessChart() {
      if (!this.$refs.businessChart) return

      this.businessChart = echarts.init(this.$refs.businessChart)

      const option = {
        title: {
          text: '业务模块分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '业务数据',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.totalActivities, name: '精彩活动', itemStyle: { color: '#409eff' } },
              { value: this.totalDemands, name: '需求对接', itemStyle: { color: '#67c23a' } },
              { value: this.totalEvents, name: '活动报名', itemStyle: { color: '#e6a23c' } },
              { value: this.totalExperts, name: '专家库', itemStyle: { color: '#f56c6c' } },
              { value: this.totalJobs, name: '招聘职位', itemStyle: { color: '#909399' } },
              { value: this.totalNews, name: '新闻资讯', itemStyle: { color: '#606266' } }
            ]
          }
        ]
      }

      this.businessChart.setOption(option)
    },

    // 初始化用户活跃度趋势图
    initUserTrendChart() {
      if (!this.$refs.userTrendChart) return

      this.userTrendChart = echarts.init(this.$refs.userTrendChart)

      // 模拟最近7天的数据
      const dates = []
      const activeData = []
      const totalData = []

      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
        activeData.push(Math.floor(Math.random() * 5) + 2) // 2-6之间的随机数
        totalData.push(Math.floor(Math.random() * 3) + 6) // 6-8之间的随机数
      }

      const option = {
        title: {
          text: '用户活跃度（最近7天）',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            fontSize: 11
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [
          {
            name: '活跃用户',
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              color: '#409eff'
            },
            itemStyle: {
              color: '#409eff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(64, 158, 255, 0.3)'
                }, {
                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'
                }]
              }
            },
            data: activeData
          },
          {
            name: '总用户',
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#67c23a'
            },
            itemStyle: {
              color: '#67c23a'
            },
            data: totalData
          }
        ]
      }

      this.userTrendChart.setOption(option)
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.businessChart) {
        this.businessChart.resize()
      }
      if (this.userTrendChart) {
        this.userTrendChart.resize()
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    },

    // 获取系统运行时间
    getUptime() {
      const now = new Date()
      const startTime = new Date('2025-01-01') // 假设系统启动时间
      const diff = now - startTime
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      return `${days} 天`
    },

    // 页面跳转
    goToPage(path) {
      this.$router.push(path)
    },

    // 外部链接跳转
    goTarget(href) {
      window.open(href, "_blank")
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);

  // 欢迎横幅样式
  .welcome-banner {
    background: #409eff;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);

    .banner-title {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 8px 0;

      i {
        color: #ffd700;
        margin-right: 8px;
      }
    }

    .banner-subtitle {
      font-size: 14px;
      opacity: 0.9;
      margin: 0 0 25px 0;
    }

    .banner-stats {
      display: flex;
      gap: 30px;
      flex-wrap: wrap;

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          opacity: 0.8;
        }
      }
    }
  }

  // 统计卡片样式
  .stats-row {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      }

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 5px;
        }

        .stat-title {
          font-size: 14px;
          color: #666;
        }
      }

      &.activities {
        .stat-icon {
          background: #409eff;
        }
        .stat-value {
          color: #409eff;
        }
      }

      &.demands {
        .stat-icon {
          background: #67c23a;
        }
        .stat-value {
          color: #67c23a;
        }
      }

      &.events {
        .stat-icon {
          background: #e6a23c;
        }
        .stat-value {
          color: #e6a23c;
        }
      }

      &.experts {
        .stat-icon {
          background: #f56c6c;
        }
        .stat-value {
          color: #f56c6c;
        }
      }
    }
  }

  // 图表展示区域样式
  .charts-row {
    margin-bottom: 20px;

    .chart-container {
      padding: 10px 0;

      .chart {
        width: 100%;
        height: 300px;
      }
    }
  }

  // 主要内容区域样式
  .main-content {
    margin-bottom: 20px;

    .content-card {
      height: 100%;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      border-radius: 8px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }

      // 活动列表样式
      .activity-list, .demand-list {
        .activity-item, .demand-item {
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .activity-title, .demand-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .activity-time, .demand-time {
            font-size: 12px;
            color: #999;
          }
        }
      }

      // 系统状态样式
      .system-status {
        .status-item {
          display: flex;
          align-items: center;
          padding: 15px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f5f7fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;

            i {
              font-size: 18px;
              color: #909399;
            }

            &.online i {
              color: #67c23a;
            }
          }

          .status-info {
            flex: 1;

            .status-label {
              font-size: 12px;
              color: #999;
              margin-bottom: 4px;
            }

            .status-value {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              line-height: 1;
            }
          }
        }
      }

      // 空数据样式
      .empty-data {
        text-align: center;
        padding: 40px 0;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 10px;
          display: block;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }

  // 快捷操作样式
  .quick-actions {
    .action-group {
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 15px 0;
        font-size: 16px;
        color: #333;
        font-weight: 600;
        border-left: 4px solid #409eff;
        padding-left: 10px;
      }

      .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;

        .el-button {
          border-radius: 6px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;

    .welcome-banner {
      padding: 20px;

      .banner-title {
        font-size: 22px;
      }

      .banner-stats {
        gap: 20px;

        .stat-item {
          .stat-number {
            font-size: 18px;
          }
        }
      }
    }

    .stats-row {
      .stat-card {
        padding: 15px;

        .stat-icon {
          width: 50px;
          height: 50px;

          i {
            font-size: 20px;
          }
        }

        .stat-info {
          .stat-value {
            font-size: 18px;
          }
        }
      }
    }

    .charts-row {
      .chart-container {
        .chart {
          height: 250px;
        }
      }
    }

    .quick-actions {
      .action-group {
        .action-buttons {
          .el-button {
            font-size: 12px;
            padding: 6px 12px;
          }
        }
      }
    }
  }
}
</style>