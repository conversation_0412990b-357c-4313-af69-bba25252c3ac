package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniPark;
import com.ruoyi.miniapp.service.IMiniParkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.domain.SysConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 园区管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "园区管理")
@RestController
@RequestMapping("/miniapp/park")
public class MiniParkController extends BaseController
{
    @Autowired
    private IMiniParkService miniParkService;

    @Autowired
    private ISysConfigService configService;

    // 园区简介图片配置键名
    private static final String PARK_INTRO_IMAGE_CONFIG_KEY = "miniapp.park.intro.image";

    /**
     * 查询园区管理列表
     */
    @ApiOperation("查询园区管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:park:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") MiniPark miniPark)
    {
        startPage();
        List<MiniPark> list = miniParkService.selectMiniParkList(miniPark);
        return getDataTable(list);
    }

    /**
     * 导出园区管理列表
     */
    @ApiOperation("导出园区管理列表")
    @PreAuthorize("@ss.hasPermi('miniapp:park:export')")
    @Log(title = "园区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") MiniPark miniPark)
    {
        List<MiniPark> list = miniParkService.selectMiniParkList(miniPark);
        ExcelUtil<MiniPark> util = new ExcelUtil<MiniPark>(MiniPark.class);
        util.exportExcel(response, list, "园区管理数据");
    }

    /**
     * 获取园区管理详细信息
     */
    @ApiOperation("获取园区管理详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:park:query')")
    @GetMapping(value = "/{parkId}")
    public AjaxResult getInfo(@ApiParam("园区ID") @PathVariable("parkId") Long parkId)
    {
        return success(miniParkService.selectMiniParkByParkId(parkId));
    }

    /**
     * 新增园区管理
     */
    @ApiOperation("新增园区管理")
    @PreAuthorize("@ss.hasPermi('miniapp:park:add')")
    @Log(title = "园区管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("园区信息") @RequestBody MiniPark miniPark)
    {
        return toAjax(miniParkService.insertMiniPark(miniPark));
    }

    /**
     * 修改园区管理
     */
    @ApiOperation("修改园区管理")
    @PreAuthorize("@ss.hasPermi('miniapp:park:edit')")
    @Log(title = "园区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("园区信息") @RequestBody MiniPark miniPark)
    {
        return toAjax(miniParkService.updateMiniPark(miniPark));
    }

    /**
     * 删除园区管理
     */
    @ApiOperation("删除园区管理")
    @PreAuthorize("@ss.hasPermi('miniapp:park:remove')")
    @Log(title = "园区管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{parkIds}")
    public AjaxResult remove(@ApiParam("园区ID数组") @PathVariable Long[] parkIds)
    {
        return toAjax(miniParkService.deleteMiniParkByParkIds(parkIds));
    }

    /**
     * 获取园区简介图片
     */
    @ApiOperation("获取园区简介图片")
    @PreAuthorize("@ss.hasPermi('miniapp:park:query')")
    @GetMapping("/config/intro")
    public AjaxResult getParkIntroImage()
    {
        String introImageUrl = configService.selectConfigByKey(PARK_INTRO_IMAGE_CONFIG_KEY);
        // 确保返回的是字符串，如果为null则返回空字符串
        String imageUrl = introImageUrl != null ? introImageUrl : "";
        // 使用success(String msg, Object data)确保数据在data字段中
        return AjaxResult.success("获取园区简介图片成功", imageUrl);
    }

    /**
     * 更新园区简介图片
     */
    @ApiOperation("更新园区简介图片")
    @PreAuthorize("@ss.hasPermi('miniapp:park:edit')")
    @Log(title = "园区简介图片", businessType = BusinessType.UPDATE)
    @PutMapping("/config/intro")
    public AjaxResult updateParkIntroImage(@RequestBody java.util.Map<String, String> params)
    {
        String introImageUrl = params.get("introImageUrl");

        // 查找是否已存在该配置
        SysConfig existingConfig = new SysConfig();
        existingConfig.setConfigKey(PARK_INTRO_IMAGE_CONFIG_KEY);
        List<SysConfig> configList = configService.selectConfigList(existingConfig);

        if (configList != null && !configList.isEmpty()) {
            // 更新现有配置
            SysConfig config = configList.get(0);
            config.setConfigValue(introImageUrl);
            configService.updateConfig(config);
        } else {
            // 创建新配置
            SysConfig newConfig = new SysConfig();
            newConfig.setConfigName("园区简介图片");
            newConfig.setConfigKey(PARK_INTRO_IMAGE_CONFIG_KEY);
            newConfig.setConfigValue(introImageUrl);
            newConfig.setConfigType("N"); // N表示非系统内置
            newConfig.setRemark("园区管理模块的简介图片URL");
            configService.insertConfig(newConfig);
        }

        return AjaxResult.success("园区简介图片更新成功");
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取启用的园区列表（小程序端）
     */
    @ApiOperation("获取启用的园区列表")
    @GetMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniPark> list = miniParkService.selectEnabledMiniParkList();
        return AjaxResult.success(list);
    }

    /**
     * 获取推荐的园区列表（小程序端）
     */
    @ApiOperation("获取推荐的园区列表")
    @GetMapping("/app/getRecommendedList")
    public AjaxResult getRecommendedList()
    {
        List<MiniPark> list = miniParkService.selectRecommendedMiniParkList();
        return AjaxResult.success(list);
    }

    /**
     * 获取园区详情（小程序端）
     */
    @ApiOperation("获取园区详情")
    @GetMapping("/app/getDetail/{parkId}")
    public AjaxResult getDetail(@ApiParam("园区ID") @PathVariable("parkId") Long parkId)
    {
        MiniPark park = miniParkService.selectMiniParkByParkId(parkId);
        if (park == null || !"0".equals(park.getStatus())) {
            return AjaxResult.error("园区不存在或已停用");
        }
        return AjaxResult.success(park);
    }

    /**
     * 小程序端获取园区简介图片
     */
    @ApiOperation("小程序端获取园区简介图片")
    @GetMapping("/app/config/intro")
    public AjaxResult getParkIntroImageForApp()
    {
        String introImageUrl = configService.selectConfigByKey(PARK_INTRO_IMAGE_CONFIG_KEY);
        // 确保返回的是字符串，如果为null则返回空字符串
        return AjaxResult.success(introImageUrl != null ? introImageUrl : "");
    }
}
