(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6d46c58d"],{"30e2":function(e,t,i){},3741:function(e,t,i){"use strict";i("30e2")},"9c94":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[i("el-form-item",{attrs:{label:"活动标题",prop:"title"}},[i("el-input",{attrs:{placeholder:"请输入活动标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),i("el-form-item",{attrs:{label:"活动地点",prop:"location"}},[i("el-input",{attrs:{placeholder:"请输入活动地点",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.location,callback:function(t){e.$set(e.queryParams,"location",t)},expression:"queryParams.location"}})],1),i("el-form-item",{attrs:{label:"状态",prop:"status"}},[i("el-select",{attrs:{placeholder:"活动状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[i("el-option",{attrs:{label:"正常",value:"0"}}),i("el-option",{attrs:{label:"停用",value:"1"}})],1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:event:add"],expression:"['miniapp:event:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:event:remove"],expression:"['miniapp:event:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:event:export"],expression:"['miniapp:event:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),i("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.eventList},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),i("el-table-column",{attrs:{label:"活动ID",align:"center",prop:"eventId",width:"80"}}),i("el-table-column",{attrs:{label:"活动标题",align:"center",prop:"title","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{label:"活动描述",align:"center",prop:"description","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{label:"封面图片",align:"center",prop:"coverImage",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.coverImage?i("image-preview",{attrs:{src:t.row.coverImage,width:50,height:50}}):e._e()]}}])}),i("el-table-column",{attrs:{label:"活动地点",align:"center",prop:"location","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{label:"开始时间",align:"center",prop:"startTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.startTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),i("el-table-column",{attrs:{label:"结束时间",align:"center",prop:"endTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.endTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),i("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"80"}}),i("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),i("el-table-column",{attrs:{label:"操作",align:"center",width:"180","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:event:edit"],expression:"['miniapp:event:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v("修改")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:event:remove"],expression:"['miniapp:event:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),i("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[i("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"活动标题",prop:"title"}},[i("el-input",{attrs:{placeholder:"请输入活动标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"活动地点",prop:"location"}},[i("el-input",{attrs:{placeholder:"请输入活动地点"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}})],1)],1)],1),i("el-form-item",{attrs:{label:"活动描述",prop:"description"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入活动描述",rows:3},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),i("el-form-item",{attrs:{label:"封面图片",prop:"coverImage"}},[i("image-upload",{model:{value:e.form.coverImage,callback:function(t){e.$set(e.form,"coverImage",t)},expression:"form.coverImage"}})],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"开始时间",prop:"startTime"}},[i("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.startTime,callback:function(t){e.$set(e.form,"startTime",t)},expression:"form.startTime"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"结束时间",prop:"endTime"}},[i("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.endTime,callback:function(t){e.$set(e.form,"endTime",t)},expression:"form.endTime"}})],1)],1)],1),i("el-form-item",{attrs:{label:"报名截止时间",prop:"registrationDeadline"}},[i("el-date-picker",{attrs:{type:"datetime",placeholder:"选择报名截止时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.registrationDeadline,callback:function(t){e.$set(e.form,"registrationDeadline",t)},expression:"form.registrationDeadline"}})],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[i("el-input-number",{attrs:{min:0,max:9999},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"状态",prop:"status"}},[i("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[i("el-radio",{attrs:{label:"0"}},[e._v("正常")]),i("el-radio",{attrs:{label:"1"}},[e._v("停用")])],1)],1)],1)],1),i("el-form-item",{attrs:{label:"报名表单配置",prop:"formFields"}},[i("div",{staticClass:"form-fields-config"},[i("div",{staticClass:"form-fields-toolbar"},[i("div",{staticClass:"toolbar-left"},[i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addFormField}},[e._v(" 添加字段 ")]),i("el-dropdown",{attrs:{size:"small"},on:{command:e.handleTemplateCommand}},[i("el-button",{attrs:{size:"small"}},[e._v(" 预设模板"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{command:"basic"}},[e._v("基础信息模板")]),i("el-dropdown-item",{attrs:{command:"tech"}},[e._v("技术分享模板")]),i("el-dropdown-item",{attrs:{command:"sport"}},[e._v("体育活动模板")]),i("el-dropdown-item",{attrs:{command:"clear"}},[e._v("清空所有字段")])],1)],1)],1),i("div",{staticClass:"toolbar-right"},[i("el-button",{attrs:{size:"small",icon:"el-icon-view"},on:{click:e.previewForm}},[e._v(" 预览表单 ")])],1)]),e.formFieldsList.length>0?i("div",{staticClass:"form-fields-list"},e._l(e.formFieldsList,(function(t,a){return i("div",{key:a,staticClass:"form-field-item"},[i("div",{staticClass:"field-header"},[i("div",{staticClass:"field-info"},[i("i",{staticClass:"field-icon",class:e.getFieldIcon(t.type)}),i("span",{staticClass:"field-label"},[e._v(e._s(t.label||"未命名字段"))]),t.required?i("el-tag",{attrs:{size:"mini",type:"danger"}},[e._v("必填")]):i("el-tag",{attrs:{size:"mini",type:"info"}},[e._v("选填")])],1),i("div",{staticClass:"field-actions"},[i("el-button",{attrs:{type:"text",size:"mini",disabled:0===a,icon:"el-icon-arrow-up"},on:{click:function(t){return e.moveField(a,-1)}}}),i("el-button",{attrs:{type:"text",size:"mini",disabled:a===e.formFieldsList.length-1,icon:"el-icon-arrow-down"},on:{click:function(t){return e.moveField(a,1)}}}),i("el-button",{staticClass:"danger-btn",attrs:{type:"text",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.removeFormField(a)}}})],1)]),i("div",{staticClass:"field-content"},[i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:12}},[i("div",{staticClass:"field-item"},[i("label",[e._v("字段标签")]),i("el-input",{attrs:{placeholder:"显示给用户的标签，如：姓名、电话等",size:"small"},on:{input:function(i){return e.updateFieldName(t,i)}},model:{value:t.label,callback:function(i){e.$set(t,"label",i)},expression:"field.label"}})],1)]),i("el-col",{attrs:{span:12}},[i("div",{staticClass:"field-item"},[i("label",[e._v("字段类型")]),i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择类型",size:"small"},model:{value:t.type,callback:function(i){e.$set(t,"type",i)},expression:"field.type"}},[i("el-option",{attrs:{label:"📝 文本输入",value:"input"}}),i("el-option",{attrs:{label:"📄 多行文本",value:"textarea"}}),i("el-option",{attrs:{label:"🔢 数字输入",value:"number"}}),i("el-option",{attrs:{label:"📧 邮箱",value:"email"}}),i("el-option",{attrs:{label:"📞 电话",value:"tel"}}),i("el-option",{attrs:{label:"🔘 单选",value:"radio"}}),i("el-option",{attrs:{label:"☑️ 多选",value:"checkbox"}}),i("el-option",{attrs:{label:"📋 下拉选择",value:"select"}}),i("el-option",{attrs:{label:"📅 日期",value:"date"}})],1)],1)])],1),i("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:4}},[i("div",{staticClass:"field-item"},[i("label",[e._v("是否必填")]),i("el-switch",{model:{value:t.required,callback:function(i){e.$set(t,"required",i)},expression:"field.required"}})],1)]),["radio","checkbox","select"].includes(t.type)?i("el-col",{attrs:{span:20}},[i("div",{staticClass:"field-item"},[i("label",[e._v("选项配置")]),i("el-input",{attrs:{placeholder:"用逗号分隔选项，如：选项1,选项2,选项3",size:"small"},model:{value:t.options,callback:function(i){e.$set(t,"options",i)},expression:"field.options"}}),t.options?i("div",{staticClass:"options-preview"},e._l(t.options.split(","),(function(t,a){return i("el-tag",{key:a,staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{size:"mini"}},[e._v(" "+e._s(t.trim())+" ")])})),1):e._e()],1)]):e._e()],1)],1)])})),0):i("div",{staticClass:"empty-state"},[i("i",{staticClass:"el-icon-document-add"}),i("p",[e._v('暂无表单字段，点击"添加字段"开始配置')])])])]),i("el-dialog",{attrs:{title:"表单预览",visible:e.previewDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.previewDialogVisible=t}}},[i("div",{staticClass:"form-preview"},[i("div",{staticClass:"preview-header"},[i("h3",[e._v(e._s(e.form.title||"活动报名表"))]),i("p",[e._v(e._s(e.form.description||"请填写以下信息完成报名"))])]),i("div",{staticClass:"preview-form"},e._l(e.formFieldsList,(function(t,a){return i("div",{key:a,staticClass:"preview-field"},[i("label",{staticClass:"preview-label"},[e._v(" "+e._s(t.label)+" "),t.required?i("span",{staticClass:"required"},[e._v("*")]):e._e()]),i("div",{staticClass:"preview-input"},["input"===t.type||"email"===t.type||"tel"===t.type?i("el-input",{attrs:{placeholder:"请输入"+t.label,size:"small",disabled:""}}):"textarea"===t.type?i("el-input",{attrs:{type:"textarea",placeholder:"请输入"+t.label,size:"small",disabled:""}}):"number"===t.type?i("el-input-number",{attrs:{placeholder:"请输入"+t.label,size:"small",disabled:""}}):"radio"===t.type?i("el-radio-group",{attrs:{disabled:""}},e._l(t.options.split(","),(function(t,a){return i("el-radio",{key:a,attrs:{label:t.trim()}},[e._v(" "+e._s(t.trim())+" ")])})),1):"checkbox"===t.type?i("el-checkbox-group",{attrs:{disabled:""}},e._l(t.options.split(","),(function(t,a){return i("el-checkbox",{key:a,attrs:{label:t.trim()}},[e._v(" "+e._s(t.trim())+" ")])})),1):"select"===t.type?i("el-select",{attrs:{placeholder:"请选择"+t.label,size:"small",disabled:""}},e._l(t.options.split(","),(function(e,t){return i("el-option",{key:t,attrs:{label:e.trim(),value:e.trim()}})})),1):"date"===t.type?i("el-date-picker",{attrs:{type:"date",placeholder:"请选择"+t.label,size:"small",disabled:""}}):e._e()],1)])})),0)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.previewDialogVisible=!1}}},[e._v("关闭")])],1)]),i("el-form-item",{attrs:{label:"备注",prop:"remark"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),i("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],r=i("5530"),o=(i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("e9c4"),i("b64b"),i("d3b7"),i("ac1f"),i("5319"),i("0643"),i("a573"),i("b775"));function n(e){var t=Object(r["a"])(Object(r["a"])({},e),{},{eventType:"activity"});return Object(o["a"])({url:"/miniapp/event/list",method:"post",data:t})}function s(e){return Object(o["a"])({url:"/miniapp/event/getInfo",method:"post",data:e})}function c(e){var t=Object(r["a"])(Object(r["a"])({},e),{},{eventType:"activity"});return Object(o["a"])({url:"/miniapp/event/add",method:"post",data:t})}function m(e){var t=Object(r["a"])(Object(r["a"])({},e),{},{eventType:"activity"});return Object(o["a"])({url:"/miniapp/event/edit",method:"post",data:t})}function d(e){return Object(o["a"])({url:"/miniapp/event/remove",method:"post",data:e})}var p={name:"MiniEvent",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,eventList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,location:null,status:null},form:{},formFieldsList:[],previewDialogVisible:!1,rules:{title:[{required:!0,message:"活动标题不能为空",trigger:"blur"}],coverImage:[{required:!0,message:"封面图片不能为空",trigger:"blur"}],startTime:[{required:!0,message:"开始时间不能为空",trigger:"change"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.eventList=t.rows,e.total=t.total,e.loading=!1})).catch((function(t){console.error("获取活动列表失败:",t),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={eventId:null,title:null,coverImage:null,description:null,location:null,startTime:null,endTime:null,registrationDeadline:null,formFields:null,sortOrder:0,status:"0",remark:null},this.formFieldsList=[],this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.eventId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加活动报名"},handleUpdate:function(e){var t=this;this.reset();var i=e.eventId||this.ids;s(i).then((function(e){if(t.form=e.data,t.form.formFields)try{t.formFieldsList=JSON.parse(t.form.formFields)}catch(i){t.formFieldsList=[]}t.open=!0,t.title="修改活动报名"})).catch((function(e){console.error("获取活动详情失败:",e),t.$modal.msgError("获取活动详情失败")}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){t&&(e.form.formFields=JSON.stringify(e.formFieldsList),null!=e.form.eventId?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})).catch((function(t){console.error("修改活动失败:",t),e.$modal.msgError("修改活动失败")})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})).catch((function(t){console.error("新增活动失败:",t),e.$modal.msgError("新增活动失败")})))}))},handleDelete:function(e){var t=this,i=e.eventId||this.ids;this.$modal.confirm('是否确认删除活动报名编号为"'+i+'"的数据项？').then((function(){return d(i)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/event/export",Object(r["a"])({},this.queryParams),"event_".concat((new Date).getTime(),".xlsx"))},addFormField:function(){this.formFieldsList.push({label:"",name:"",type:"input",required:!1,options:""})},removeFormField:function(e){this.formFieldsList.splice(e,1)},getFieldIcon:function(e){var t={input:"el-icon-edit",textarea:"el-icon-document",number:"el-icon-s-data",email:"el-icon-message",tel:"el-icon-phone",radio:"el-icon-circle-check",checkbox:"el-icon-check",select:"el-icon-arrow-down",date:"el-icon-date"};return t[e]||"el-icon-edit"},updateFieldName:function(e,t){var i={"姓名":"name","真实姓名":"real_name","联系电话":"phone","手机号":"mobile","电话":"phone","邮箱":"email","邮箱地址":"email","年龄":"age","性别":"gender","地址":"address","详细地址":"address","公司":"company","所在公司":"company","职位":"position","工作岗位":"position","备注":"remark","说明":"remark","身份证":"id_card","身份证号":"id_card","学校":"school","专业":"major","班级":"class","学号":"student_id","工作经验":"experience","工作年限":"work_years","紧急联系人":"emergency_contact","紧急联系人电话":"emergency_phone","个人简介":"introduction","自我介绍":"introduction","期望收获":"expectations","参与原因":"reason","特长技能":"skills","兴趣爱好":"hobbies","技术方向":"tech_direction","出生日期":"birthday","参赛项目":"events","是否有运动损伤史":"injury_history","特殊说明":"special_notes"};i[t]?e.name=i[t]:e.name=t.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g,"_").replace(/_{2,}/g,"_").replace(/^_|_$/g,"").toLowerCase()||"field_"+Date.now()},moveField:function(e,t){var i=e+t;if(i>=0&&i<this.formFieldsList.length){var a=this.formFieldsList.splice(e,1)[0];this.formFieldsList.splice(i,0,a)}},handleTemplateCommand:function(e){var t=this;if("clear"!==e){var i={basic:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!1,options:""}],tech:[{label:"姓名",name:"name",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"邮箱地址",name:"email",type:"email",required:!0,options:""},{label:"所在公司",name:"company",type:"input",required:!1,options:""},{label:"技术方向",name:"tech_direction",type:"select",required:!0,options:"前端开发,后端开发,移动开发,人工智能,数据分析"},{label:"工作年限",name:"experience",type:"radio",required:!0,options:"1年以内,1-3年,3-5年,5年以上"},{label:"期望收获",name:"expectations",type:"textarea",required:!1,options:""}],sport:[{label:"参赛者姓名",name:"player_name",type:"input",required:!0,options:""},{label:"身份证号",name:"id_card",type:"input",required:!0,options:""},{label:"联系电话",name:"phone",type:"tel",required:!0,options:""},{label:"紧急联系人",name:"emergency_contact",type:"input",required:!0,options:""},{label:"紧急联系人电话",name:"emergency_phone",type:"tel",required:!0,options:""},{label:"参赛项目",name:"events",type:"checkbox",required:!0,options:"100米,200米,800米,跳远,跳高"},{label:"是否有运动损伤史",name:"injury_history",type:"radio",required:!0,options:"是,否"},{label:"特殊说明",name:"special_notes",type:"textarea",required:!1,options:""}]};i[e]&&(this.formFieldsList=i[e],this.$message.success("模板应用成功"))}else this.$confirm("确定要清空所有字段吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.formFieldsList=[],t.$message.success("已清空所有字段")}))},previewForm:function(){0!==this.formFieldsList.length?this.previewDialogVisible=!0:this.$message.warning("请先添加表单字段")}}},u=p,f=(i("3741"),i("2877")),v=Object(f["a"])(u,a,l,!1,null,"028275c7",null);t["default"]=v.exports}}]);