{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753847579546}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}