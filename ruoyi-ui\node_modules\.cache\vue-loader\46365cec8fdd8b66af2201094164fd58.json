{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=template&id=1a21e6f4", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1753694616742}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1mb3JtIDptb2RlbD0icXVlcnlQYXJhbXMiIHJlZj0icXVlcnlGb3JtIiBzaXplPSJzbWFsbCIgOmlubGluZT0idHJ1ZSIgdi1zaG93PSJzaG93U2VhcmNoIiBsYWJlbC13aWR0aD0iNjhweCI+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpobnnm67lkI3np7AiIHByb3A9InByb2plY3ROYW1lIj4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMucHJvamVjdE5hbWUiCiAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpemhueebruWQjeensCIKICAgICAgICBjbGVhcmFibGUKICAgICAgICBAa2V5dXAuZW50ZXIubmF0aXZlPSJoYW5kbGVRdWVyeSIKICAgICAgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Z+O5biCIiBwcm9wPSJjaXR5Ij4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuY2l0eSIKICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5Z+O5biCIgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIEBrZXl1cC5lbnRlci5uYXRpdmU9ImhhbmRsZVF1ZXJ5IgogICAgICAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLooYzkuJoiIHByb3A9ImluZHVzdHJ5Ij4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuaW5kdXN0cnkiCiAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeihjOS4miIKICAgICAgICBjbGVhcmFibGUKICAgICAgICBAa2V5dXAuZW50ZXIubmF0aXZlPSJoYW5kbGVRdWVyeSIKICAgICAgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i54q25oCBIiBwcm9wPSJzdGF0dXMiPgogICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9InF1ZXJ5UGFyYW1zLnN0YXR1cyIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeeKtuaAgSIgY2xlYXJhYmxlPgogICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuW+heWuoeaguCIgdmFsdWU9IjEiIC8+CiAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5a6h5qC46YCa6L+HIiB2YWx1ZT0iMiIgLz4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlrqHmoLjkuI3pgJrov4ciIHZhbHVlPSIzIiAvPgogICAgICA8L2VsLXNlbGVjdD4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbT4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLXNlYXJjaCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJoYW5kbGVRdWVyeSI+5pCc57SiPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gaWNvbj0iZWwtaWNvbi1yZWZyZXNoIiBzaXplPSJtaW5pIiBAY2xpY2s9InJlc2V0UXVlcnkiPumHjee9rjwvZWwtYnV0dG9uPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgPC9lbC1mb3JtPgoKICA8ZWwtcm93IDpndXR0ZXI9IjEwIiBjbGFzcz0ibWI4Ij4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJzdWNjZXNzIgogICAgICAgIHBsYWluCiAgICAgICAgaWNvbj0iZWwtaWNvbi1waWN0dXJlIgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVTcG9uc29yVXBsb2FkIgogICAgICAgIHYtaGFzUGVybWk9IlsnbWluaWFwcDpoYWl0YW5nOnByb2plY3Q6ZWRpdCddIgogICAgICA+6LWe5Yqp5ZWG5Zu+54mHPC9lbC1idXR0b24+CiAgICA8L2VsLWNvbD4KCiAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0iZGFuZ2VyIgogICAgICAgIHBsYWluCiAgICAgICAgaWNvbj0iZWwtaWNvbi1kZWxldGUiCiAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICA6ZGlzYWJsZWQ9Im11bHRpcGxlIgogICAgICAgIEBjbGljaz0iaGFuZGxlRGVsZXRlIgogICAgICAgIHYtaGFzUGVybWk9IlsnbWluaWFwcDpoYWl0YW5nOnByb2plY3Q6cmVtb3ZlJ10iCiAgICAgID7liKDpmaQ8L2VsLWJ1dHRvbj4KICAgIDwvZWwtY29sPgogICAgPGVsLWNvbCA6c3Bhbj0iMS41Ij4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9Indhcm5pbmciCiAgICAgICAgcGxhaW4KICAgICAgICBpY29uPSJlbC1pY29uLWRvd25sb2FkIgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVFeHBvcnQiCiAgICAgICAgdi1oYXNQZXJtaT0iWydtaW5pYXBwOmhhaXRhbmc6cHJvamVjdDpleHBvcnQnXSIKICAgICAgPuWvvOWHujwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+CiAgICA8cmlnaHQtdG9vbGJhciA6c2hvd1NlYXJjaC5zeW5jPSJzaG93U2VhcmNoIiBAcXVlcnlUYWJsZT0iZ2V0TGlzdCI+PC9yaWdodC10b29sYmFyPgogIDwvZWwtcm93PgoKICA8ZWwtdGFibGUgdi1sb2FkaW5nPSJsb2FkaW5nIiA6ZGF0YT0icHJvamVjdExpc3QiIEBzZWxlY3Rpb24tY2hhbmdlPSJoYW5kbGVTZWxlY3Rpb25DaGFuZ2UiPgogICAgPGVsLXRhYmxlLWNvbHVtbiB0eXBlPSJzZWxlY3Rpb24iIHdpZHRoPSI1NSIgYWxpZ249ImNlbnRlciIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IumhueebruWQjeensCIgYWxpZ249ImNlbnRlciIgcHJvcD0icHJvamVjdE5hbWUiIDpzaG93LW92ZXJmbG93LXRvb2x0aXA9InRydWUiLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWboumYn+inhOaooSIgYWxpZ249ImNlbnRlciIgcHJvcD0idGVhbVNpemUiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLln47luIIiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNpdHkiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLotZvljLoiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNvbXBldGl0aW9uQXJlYSIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuihjOS4miIgYWxpZ249ImNlbnRlciIgcHJvcD0iaW5kdXN0cnkiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLlpKnlpKfmoKHlj4siIGFsaWduPSJjZW50ZXIiIHByb3A9ImlzVGp1QWx1bW5pIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8ZWwtdGFnIDp0eXBlPSJzY29wZS5yb3cuaXNUanVBbHVtbmkgPyAnc3VjY2VzcycgOiAnaW5mbyciPgogICAgICAgICAge3sgc2NvcGUucm93LmlzVGp1QWx1bW5pID8gJ+aYrycgOiAn5ZCmJyB9fQogICAgICAgIDwvZWwtdGFnPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmnInlhazlj7giIGFsaWduPSJjZW50ZXIiIHByb3A9Imhhc0NvbXBhbnkiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxlbC10YWcgOnR5cGU9InNjb3BlLnJvdy5oYXNDb21wYW55ID8gJ3N1Y2Nlc3MnIDogJ2luZm8nIj4KICAgICAgICAgIHt7IHNjb3BlLnJvdy5oYXNDb21wYW55ID8gJ+aYrycgOiAn5ZCmJyB9fQogICAgICAgIDwvZWwtdGFnPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLlhazlj7jlkI3np7AiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNvbXBhbnlOYW1lIiA6c2hvdy1vdmVyZmxvdy10b29sdGlwPSJ0cnVlIi8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLogZTns7vkuroiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNvbnRhY3ROYW1lIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6IGU57O755S16K+dIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJjb250YWN0UGhvbmUiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmiqXlkI3ml7bpl7QiIGFsaWduPSJjZW50ZXIiIHByb3A9InJlZ2lzdHJhdGlvblRpbWUiIHdpZHRoPSIxODAiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxzcGFuPnt7IHBhcnNlVGltZShzY29wZS5yb3cucmVnaXN0cmF0aW9uVGltZSwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JykgfX08L3NwYW4+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IueKtuaAgSIgYWxpZ249ImNlbnRlciIgcHJvcD0ic3RhdHVzIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8ZWwtdGFnIDp0eXBlPSJnZXRTdGF0dXNUeXBlKHNjb3BlLnJvdy5zdGF0dXMpIj4KICAgICAgICAgIHt7IGdldFN0YXR1c1RleHQoc2NvcGUucm93LnN0YXR1cykgfX0KICAgICAgICA8L2VsLXRhZz4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5pON5L2cIiBhbGlnbj0iY2VudGVyIiBjbGFzcy1uYW1lPSJzbWFsbC1wYWRkaW5nIGZpeGVkLXdpZHRoIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tdmlldyIKICAgICAgICAgIEBjbGljaz0iaGFuZGxlVmlldyhzY29wZS5yb3cpIgogICAgICAgICAgdi1oYXNQZXJtaT0iWydtaW5pYXBwOmhhaXRhbmc6cHJvamVjdDpxdWVyeSddIgogICAgICAgID7mn6XnnIs8L2VsLWJ1dHRvbj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tY2hlY2siCiAgICAgICAgICBAY2xpY2s9ImhhbmRsZUF1ZGl0KHNjb3BlLnJvdykiCiAgICAgICAgICB2LWhhc1Blcm1pPSJbJ21pbmlhcHA6aGFpdGFuZzpwcm9qZWN0OmF1ZGl0J10iCiAgICAgICAgICB2LWlmPSJzY29wZS5yb3cuc3RhdHVzID09PSAxIgogICAgICAgID7lrqHmoLg8L2VsLWJ1dHRvbj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgICAgQGNsaWNrPSJoYW5kbGVEZWxldGUoc2NvcGUucm93KSIKICAgICAgICAgIHYtaGFzUGVybWk9IlsnbWluaWFwcDpoYWl0YW5nOnByb2plY3Q6cmVtb3ZlJ10iCiAgICAgICAgPuWIoOmZpDwvZWwtYnV0dG9uPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgPC9lbC10YWJsZT4KICAKICA8cGFnaW5hdGlvbgogICAgdi1zaG93PSJ0b3RhbD4wIgogICAgOnRvdGFsPSJ0b3RhbCIKICAgIDpwYWdlLnN5bmM9InF1ZXJ5UGFyYW1zLnBhZ2VOdW0iCiAgICA6bGltaXQuc3luYz0icXVlcnlQYXJhbXMucGFnZVNpemUiCiAgICBAcGFnaW5hdGlvbj0iZ2V0TGlzdCIKICAvPgoKCgogIDwhLS0g5a6h5qC45a+56K+d5qGGIC0tPgogIDxlbC1kaWFsb2cgdGl0bGU9IuWuoeaguOmhueebruaKpeWQjSIgOnZpc2libGUuc3luYz0iYXVkaXRPcGVuIiB3aWR0aD0iNTAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGVsLWZvcm0gcmVmPSJhdWRpdEZvcm0iIDptb2RlbD0iYXVkaXRGb3JtIiA6cnVsZXM9ImF1ZGl0UnVsZXMiIGxhYmVsLXdpZHRoPSI4MHB4Ij4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5a6h5qC457uT5p6cIiBwcm9wPSJzdGF0dXMiPgogICAgICAgIDxlbC1yYWRpby1ncm91cCB2LW1vZGVsPSJhdWRpdEZvcm0uc3RhdHVzIj4KICAgICAgICAgIDxlbC1yYWRpbyA6bGFiZWw9IjIiPuWuoeaguOmAmui/hzwvZWwtcmFkaW8+CiAgICAgICAgICA8ZWwtcmFkaW8gOmxhYmVsPSIzIj7lrqHmoLjkuI3pgJrov4c8L2VsLXJhZGlvPgogICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlrqHmoLjlpIfms6giIHByb3A9ImF1ZGl0UmVtYXJrIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iYXVkaXRGb3JtLmF1ZGl0UmVtYXJrIiB0eXBlPSJ0ZXh0YXJlYSIgOnJvd3M9IjQiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlrqHmoLjlpIfms6giIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPC9lbC1mb3JtPgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9InN1Ym1pdEF1ZGl0Ij7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjYW5jZWxBdWRpdCI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0g6LWe5Yqp5ZWG5Zu+54mH5LiK5Lyg5a+56K+d5qGGIC0tPgogIDxlbC1kaWFsb2cgdGl0bGU9Iui1nuWKqeWVhuWbvueJh+euoeeQhiIgOnZpc2libGUuc3luYz0ic3BvbnNvck9wZW4iIHdpZHRoPSI3MDBweCIgYXBwZW5kLXRvLWJvZHk+CiAgICA8ZWwtZm9ybSByZWY9InNwb25zb3JGb3JtIiA6bW9kZWw9InNwb25zb3JGb3JtIiBsYWJlbC13aWR0aD0iMTIwcHgiPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlvZPliY3otZ7liqnllYblm77niYciPgogICAgICAgIDxkaXYgdi1pZj0ic3BvbnNvckZvcm0uc3BvbnNvclVuaXQgJiYgc3BvbnNvckZvcm0uc3BvbnNvclVuaXQudHJpbSgpICE9PSAnJyIgc3R5bGU9InBhZGRpbmc6IDEwcHg7IGJhY2tncm91bmQ6ICNmMGY5ZmY7IGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7IGJvcmRlci1yYWRpdXM6IDRweDsgbWFyZ2luLWJvdHRvbTogMTBweDsiPgogICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tc3VjY2VzcyIgc3R5bGU9ImNvbG9yOiAjNjdjMjNhOyBtYXJnaW4tcmlnaHQ6IDVweDsiPjwvaT4KICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzQwOWVmZjsiPuW3suiuvue9rui1nuWKqeWVhuWbvueJhzwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IHYtZWxzZSBzdHlsZT0icGFkZGluZzogMTBweDsgYmFja2dyb3VuZDogI2ZkZjZlYzsgYm9yZGVyOiAxcHggc29saWQgI2Y1ZGFiMTsgYm9yZGVyLXJhZGl1czogNHB4OyBtYXJnaW4tYm90dG9tOiAxMHB4OyI+CiAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi13YXJuaW5nIiBzdHlsZT0iY29sb3I6ICNlNmEyM2M7IG1hcmdpbi1yaWdodDogNXB4OyI+PC9pPgogICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjZTZhMjNjOyI+5pqC5pyq6K6+572u6LWe5Yqp5ZWG5Zu+54mHPC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5LiK5Lyg5paw5Zu+54mHIj4KICAgICAgICA8aW1hZ2UtdXBsb2FkIHYtbW9kZWw9InNwb25zb3JGb3JtLnNwb25zb3JVbml0IiA6bGltaXQ9IjEiLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8L2VsLWZvcm0+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0ic3VibWl0U3BvbnNvckZvcm0iPuehriDlrpo8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImNhbmNlbFNwb25zb3IiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKCgogIDwhLS0g5p+l55yL6K+m5oOF5a+56K+d5qGGIC0tPgogIDxlbC1kaWFsb2cgdGl0bGU9IumhueebruaKpeWQjeivpuaDhSIgOnZpc2libGUuc3luYz0idmlld09wZW4iIHdpZHRoPSI4MDBweCIgYXBwZW5kLXRvLWJvZHk+CiAgICA8ZWwtZGVzY3JpcHRpb25zIDpjb2x1bW49IjIiIGJvcmRlcj4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLpobnnm67lkI3np7AiPnt7IHZpZXdGb3JtLnByb2plY3ROYW1lIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlm6LpmJ/op4TmqKEiPnt7IHZpZXdGb3JtLnRlYW1TaXplIH195Lq6PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLln47luIIiPnt7IHZpZXdGb3JtLmNpdHkgfX08L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9Iui1m+WMuiI+e3sgdmlld0Zvcm0uY29tcGV0aXRpb25BcmVhIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLooYzkuJoiPnt7IHZpZXdGb3JtLmluZHVzdHJ5IH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlpKnlpKfmoKHlj4siPnt7IHZpZXdGb3JtLmlzVGp1QWx1bW5pID8gJ+aYrycgOiAn5ZCmJyB9fTwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6aG555uu5o+P6L+wIiA6c3Bhbj0iMiI+e3sgdmlld0Zvcm0ucHJvamVjdERlc2NyaXB0aW9uIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLmmK/lkKbmnInlhazlj7giPnt7IHZpZXdGb3JtLmhhc0NvbXBhbnkgPyAn5pivJyA6ICflkKYnIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlhazlj7jlkI3np7AiIHYtaWY9InZpZXdGb3JtLmhhc0NvbXBhbnkiPnt7IHZpZXdGb3JtLmNvbXBhbnlOYW1lIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLljrvlubTokKXmlLYiIHYtaWY9InZpZXdGb3JtLmhhc0NvbXBhbnkiPnt7IHZpZXdGb3JtLmxhc3RZZWFyUmV2ZW51ZSB9feS4h+WFgzwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6aG555uu5Lyw5YC8IiB2LWlmPSJ2aWV3Rm9ybS5oYXNDb21wYW55Ij57eyB2aWV3Rm9ybS5wcm9qZWN0VmFsdWF0aW9uIH195LiH5YWDPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLmnIDmlrDono3otYTova7mrKEiIHYtaWY9InZpZXdGb3JtLmhhc0NvbXBhbnkiPnt7IHZpZXdGb3JtLmxhdGVzdEZ1bmRpbmdSb3VuZCB9fTwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5oqV6LWE5py65p6EIiB2LWlmPSJ2aWV3Rm9ybS5oYXNDb21wYW55Ij57eyB2aWV3Rm9ybS5pbnZlc3RtZW50SW5zdGl0dXRpb24gfX08L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuWFrOWPuGxvZ28iIHYtaWY9InZpZXdGb3JtLmhhc0NvbXBhbnkgJiYgdmlld0Zvcm0uY29tcGFueUxvZ28iPgogICAgICAgIDxlbC1pbWFnZQogICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDBweDsgaGVpZ2h0OiA2MHB4IgogICAgICAgICAgOnNyYz0idmlld0Zvcm0uY29tcGFueUxvZ28iCiAgICAgICAgICA6cHJldmlldy1zcmMtbGlzdD0iW3ZpZXdGb3JtLmNvbXBhbnlMb2dvXSIKICAgICAgICAgIGZpdD0iY292ZXIiPgogICAgICAgIDwvZWwtaW1hZ2U+CiAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6aG555uuQlAiIHYtaWY9InZpZXdGb3JtLnByb2plY3RCcCI+CiAgICAgICAgPGVsLWxpbmsgdHlwZT0icHJpbWFyeSIgOmhyZWY9InZpZXdGb3JtLnByb2plY3RCcCIgdGFyZ2V0PSJfYmxhbmsiIDp1bmRlcmxpbmU9ImZhbHNlIj4KICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWRvd25sb2FkIj48L2k+IOS4i+i9vemhueebrkJQCiAgICAgICAgPC9lbC1saW5rPgogICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuaOqOiNkOS6uiI+e3sgdmlld0Zvcm0ucmVjb21tZW5kZXIgfX08L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9Iui1nuWKqeWNleS9jSIgdi1pZj0idmlld0Zvcm0uc3BvbnNvclVuaXQiPgogICAgICAgIDxlbC1pbWFnZQogICAgICAgICAgc3R5bGU9IndpZHRoOiAxMjBweDsgaGVpZ2h0OiA2MHB4IgogICAgICAgICAgOnNyYz0idmlld0Zvcm0uc3BvbnNvclVuaXQiCiAgICAgICAgICA6cHJldmlldy1zcmMtbGlzdD0iW3ZpZXdGb3JtLnNwb25zb3JVbml0XSIKICAgICAgICAgIGZpdD0iY29udGFpbiI+CiAgICAgICAgPC9lbC1pbWFnZT4KICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLogZTns7vkurrlp5PlkI0iPnt7IHZpZXdGb3JtLmNvbnRhY3ROYW1lIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLogZTns7vkurrnlLXor50iPnt7IHZpZXdGb3JtLmNvbnRhY3RQaG9uZSB9fTwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6IGU57O75Lq65b6u5L+hIj57eyB2aWV3Rm9ybS5jb250YWN0V2VjaGF0IH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLogZTns7vkurrogYzkvY0iPnt7IHZpZXdGb3JtLmNvbnRhY3RQb3NpdGlvbiB9fTwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5oql5ZCN5pe26Ze0Ij57eyBwYXJzZVRpbWUodmlld0Zvcm0ucmVnaXN0cmF0aW9uVGltZSwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JykgfX08L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IueKtuaAgSI+e3sgZ2V0U3RhdHVzVGV4dCh2aWV3Rm9ybS5zdGF0dXMpIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlrqHmoLjml7bpl7QiIHYtaWY9InZpZXdGb3JtLmF1ZGl0VGltZSI+e3sgcGFyc2VUaW1lKHZpZXdGb3JtLmF1ZGl0VGltZSwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JykgfX08L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IuWuoeaguOWkh+azqCIgdi1pZj0idmlld0Zvcm0uYXVkaXRSZW1hcmsiPnt7IHZpZXdGb3JtLmF1ZGl0UmVtYXJrIH19PC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KICAgIDwvZWwtZGVzY3JpcHRpb25zPgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9InZpZXdPcGVuID0gZmFsc2UiPuWFsyDpl608L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgo8L2Rpdj4K"}, null]}