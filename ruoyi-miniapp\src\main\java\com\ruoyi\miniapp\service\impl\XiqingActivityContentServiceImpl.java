package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.XiqingActivityContentMapper;
import com.ruoyi.miniapp.domain.XiqingActivityContent;
import com.ruoyi.miniapp.service.IXiqingActivityContentService;

/**
 * 西青金种子专区活动内容管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class XiqingActivityContentServiceImpl implements IXiqingActivityContentService 
{
    @Autowired
    private XiqingActivityContentMapper xiqingActivityContentMapper;

    /**
     * 查询西青金种子专区活动内容管理
     * 
     * @param contentId 西青金种子专区活动内容管理主键
     * @return 西青金种子专区活动内容管理
     */
    @Override
    public XiqingActivityContent selectXiqingActivityContentByContentId(Long contentId)
    {
        return xiqingActivityContentMapper.selectXiqingActivityContentByContentId(contentId);
    }

    /**
     * 查询西青金种子专区活动内容管理列表
     * 
     * @param xiqingActivityContent 西青金种子专区活动内容管理
     * @return 西青金种子专区活动内容管理
     */
    @Override
    public List<XiqingActivityContent> selectXiqingActivityContentList(XiqingActivityContent xiqingActivityContent)
    {
        return xiqingActivityContentMapper.selectXiqingActivityContentList(xiqingActivityContent);
    }

    /**
     * 修改西青金种子专区活动内容管理
     * 
     * @param xiqingActivityContent 西青金种子专区活动内容管理
     * @return 结果
     */
    @Override
    public int updateXiqingActivityContent(XiqingActivityContent xiqingActivityContent)
    {
        xiqingActivityContent.setUpdateTime(DateUtils.getNowDate());
        return xiqingActivityContentMapper.updateXiqingActivityContent(xiqingActivityContent);
    }

    /**
     * 获取启用的专区活动内容（小程序端调用）
     * 
     * @return 西青金种子专区活动内容管理
     */
    @Override
    public XiqingActivityContent selectEnabledXiqingActivityContent()
    {
        return xiqingActivityContentMapper.selectEnabledXiqingActivityContent();
    }
}
