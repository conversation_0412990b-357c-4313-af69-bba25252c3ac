<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.XiqingRoadshowRegistrationMapper">
    
    <resultMap type="XiqingRoadshowRegistration" id="XiqingRoadshowRegistrationResult">
        <result property="registrationId"    column="registration_id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="userId"    column="user_id"    />
        <result property="formData"    column="form_data"    />
        <result property="registrationTime"    column="registration_time"    />
        <result property="status"    column="status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="activityTitle"    column="activity_title"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectXiqingRoadshowRegistrationVo">
        select r.registration_id, r.activity_id, r.user_id, r.form_data, r.registration_time, r.status, r.audit_remark, r.audit_time, r.audit_by, r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
               a.activity_title
        from xiqing_roadshow_registration r
        left join xiqing_roadshow_activity a on r.activity_id = a.activity_id
    </sql>

    <select id="selectXiqingRoadshowRegistrationList" parameterType="XiqingRoadshowRegistration" resultMap="XiqingRoadshowRegistrationResult">
        <include refid="selectXiqingRoadshowRegistrationVo"/>
        <where>
            <if test="activityId != null "> and r.activity_id = #{activityId}</if>
            <if test="userId != null "> and r.user_id = #{userId}</if>
            <if test="status != null  and status != ''"> and r.status = #{status}</if>
            <if test="registrationTime != null "> and r.registration_time = #{registrationTime}</if>
        </where>
        order by r.registration_time desc
    </select>
    
    <select id="selectXiqingRoadshowRegistrationByRegistrationId" parameterType="Long" resultMap="XiqingRoadshowRegistrationResult">
        <include refid="selectXiqingRoadshowRegistrationVo"/>
        where r.registration_id = #{registrationId}
    </select>
        
    <insert id="insertXiqingRoadshowRegistration" parameterType="XiqingRoadshowRegistration" useGeneratedKeys="true" keyProperty="registrationId">
        insert into xiqing_roadshow_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="formData != null and formData != ''">form_data,</if>
            <if test="registrationTime != null">registration_time,</if>
            <if test="status != null">status,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="formData != null and formData != ''">#{formData},</if>
            <if test="registrationTime != null">#{registrationTime},</if>
            <if test="status != null">#{status},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            create_time
         </trim>
    </insert>

    <update id="updateXiqingRoadshowRegistration" parameterType="XiqingRoadshowRegistration">
        update xiqing_roadshow_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="formData != null and formData != ''">form_data = #{formData},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where registration_id = #{registrationId}
    </update>

    <delete id="deleteXiqingRoadshowRegistrationByRegistrationId" parameterType="Long">
        delete from xiqing_roadshow_registration where registration_id = #{registrationId}
    </delete>

    <delete id="deleteXiqingRoadshowRegistrationByRegistrationIds" parameterType="String">
        delete from xiqing_roadshow_registration where registration_id in 
        <foreach item="registrationId" collection="array" open="(" separator="," close=")">
            #{registrationId}
        </foreach>
    </delete>
</mapper>
