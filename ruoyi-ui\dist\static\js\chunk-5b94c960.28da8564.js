(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b94c960"],{7110:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"用户名",prop:"userName"}},[n("el-input",{attrs:{placeholder:"请输入用户名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),n("el-form-item",{attrs:{label:"模块名称",prop:"moduleName"}},[n("el-input",{attrs:{placeholder:"请输入模块名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.moduleName,callback:function(t){e.$set(e.queryParams,"moduleName",t)},expression:"queryParams.moduleName"}})],1),n("el-form-item",{attrs:{label:"操作类型",prop:"operationType"}},[n("el-select",{attrs:{placeholder:"请选择操作类型",clearable:""},model:{value:e.queryParams.operationType,callback:function(t){e.$set(e.queryParams,"operationType",t)},expression:"queryParams.operationType"}},[n("el-option",{attrs:{label:"检测",value:"检测"}}),n("el-option",{attrs:{label:"过滤",value:"过滤"}}),n("el-option",{attrs:{label:"替换",value:"替换"}})],1)],1),n("el-form-item",{attrs:{label:"创建时间"}},[n("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWordLog:remove"],expression:"['miniapp:sensitiveWordLog:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWordLog:export"],expression:"['miniapp:sensitiveWordLog:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWordLog:clean"],expression:"['miniapp:sensitiveWordLog:clean']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini"},on:{click:e.handleClean}},[e._v("清理日志")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.logList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"日志ID",align:"center",prop:"logId"}}),n("el-table-column",{attrs:{label:"用户名",align:"center",prop:"userName"}}),n("el-table-column",{attrs:{label:"模块名称",align:"center",prop:"moduleName"}}),n("el-table-column",{attrs:{label:"操作类型",align:"center",prop:"operationType"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-tag",{attrs:{type:e.getOperationTypeTag(t.row.operationType)}},[e._v(" "+e._s(t.row.operationType)+" ")])]}}])}),n("el-table-column",{attrs:{label:"原始内容",align:"center",prop:"originalContent","show-overflow-tooltip":!0,width:"200"}}),n("el-table-column",{attrs:{label:"过滤后内容",align:"center",prop:"filteredContent","show-overflow-tooltip":!0,width:"200"}}),n("el-table-column",{attrs:{label:"命中数量",align:"center",prop:"hitCount"}}),n("el-table-column",{attrs:{label:"客户端IP",align:"center",prop:"clientIp"}}),n("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWordLog:query"],expression:"['miniapp:sensitiveWordLog:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(n){return e.handleView(t.row)}}},[e._v("查看")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:sensitiveWordLog:remove"],expression:"['miniapp:sensitiveWordLog:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:"日志详情",visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"用户名："}},[n("span",[e._v(e._s(e.form.userName))])])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"模块名称："}},[n("span",[e._v(e._s(e.form.moduleName))])])],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"操作类型："}},[n("el-tag",{attrs:{type:e.getOperationTypeTag(e.form.operationType)}},[e._v(" "+e._s(e.form.operationType)+" ")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"命中数量："}},[n("span",[e._v(e._s(e.form.hitCount))])])],1)],1),n("el-form-item",{attrs:{label:"原始内容："}},[n("el-input",{attrs:{type:"textarea",rows:3,readonly:""},model:{value:e.form.originalContent,callback:function(t){e.$set(e.form,"originalContent",t)},expression:"form.originalContent"}})],1),e.form.filteredContent?n("el-form-item",{attrs:{label:"过滤后内容："}},[n("el-input",{attrs:{type:"textarea",rows:3,readonly:""},model:{value:e.form.filteredContent,callback:function(t){e.$set(e.form,"filteredContent",t)},expression:"form.filteredContent"}})],1):e._e(),e.hitWordsList.length>0?n("el-form-item",{attrs:{label:"命中敏感词："}},e._l(e.hitWordsList,(function(t){return n("el-tag",{key:t,staticStyle:{"margin-right":"5px"},attrs:{type:"danger"}},[e._v(" "+e._s(t)+" ")])})),1):e._e(),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"客户端IP："}},[n("span",[e._v(e._s(e.form.clientIp))])])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"创建时间："}},[n("span",[e._v(e._s(e.parseTime(e.form.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])])],1)],1),e.form.userAgent?n("el-form-item",{attrs:{label:"用户代理："}},[n("el-input",{attrs:{type:"textarea",rows:2,readonly:""},model:{value:e.form.userAgent,callback:function(t){e.$set(e.form,"userAgent",t)},expression:"form.userAgent"}})],1):e._e()],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.open=!1}}},[e._v("关 闭")])],1)],1),n("el-dialog",{attrs:{title:"清理日志",visible:e.cleanOpen,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.cleanOpen=t}}},[n("el-form",{ref:"cleanForm",attrs:{model:e.cleanForm,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"保留天数：",prop:"days"}},[n("el-input-number",{attrs:{min:1,max:365,"controls-position":"right"},model:{value:e.cleanForm.days,callback:function(t){e.$set(e.cleanForm,"days",t)},expression:"cleanForm.days"}}),n("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"5px"}},[e._v(" 将删除 "+e._s(e.cleanForm.days)+" 天前的所有日志记录 ")])],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitClean}},[e._v("确 定")]),n("el-button",{on:{click:function(t){e.cleanOpen=!1}}},[e._v("取 消")])],1)],1)],1)},r=[],i=n("5530"),o=(n("d81d"),n("b64b"),n("d3b7"),n("0643"),n("a573"),n("8cb7")),l={name:"SensitiveWordLog",data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,logList:[],title:"",open:!1,cleanOpen:!1,dateRange:[],queryParams:{pageNum:1,pageSize:10,userName:null,moduleName:null,operationType:null},form:{},cleanForm:{days:30},hitWordsList:[]}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.params={},null!=this.dateRange&&""!=this.dateRange&&(this.queryParams.params["beginTime"]=this.dateRange[0],this.queryParams.params["endTime"]=this.dateRange[1]),Object(o["o"])(this.queryParams).then((function(t){e.logList=t.rows,e.total=t.total,e.loading=!1}))},getOperationTypeTag:function(e){var t={"检测":"info","过滤":"warning","替换":"success"};return t[e]||"info"},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.logId})),this.multiple=!e.length},handleView:function(e){var t=this;Object(o["l"])(e.logId).then((function(e){if(t.form=e.data,t.hitWordsList=[],t.form.hitWords)try{t.hitWordsList=JSON.parse(t.form.hitWords)}catch(n){console.error("解析命中敏感词失败:",n)}t.open=!0}))},handleDelete:function(e){var t=this,n=e.logId||this.ids;this.$confirm('是否确认删除日志编号为"'+n+'"的数据项？',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){return Object(o["g"])(n)})).then((function(){t.getList(),t.$message({type:"success",message:"删除成功!"})})).catch((function(){}))},handleExport:function(){this.download("miniapp/sensitiveWordLog/export",Object(i["a"])({},this.queryParams),"sensitiveWordLog_".concat((new Date).getTime(),".xlsx"))},handleClean:function(){this.cleanOpen=!0},submitClean:function(){var e=this;this.$confirm("确认清理 ".concat(this.cleanForm.days," 天前的所有日志记录吗？"),"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){return Object(o["d"])(e.cleanForm.days)})).then((function(t){e.cleanOpen=!1,e.getList(),e.$message({type:"success",message:t.msg})})).catch((function(){}))}}},s=l,u=n("2877"),c=Object(u["a"])(s,a,r,!1,null,null,null);t["default"]=c.exports},"8cb7":function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"i",(function(){return i})),n.d(t,"k",(function(){return o})),n.d(t,"b",(function(){return l})),n.d(t,"r",(function(){return s})),n.d(t,"f",(function(){return u})),n.d(t,"m",(function(){return c})),n.d(t,"j",(function(){return m})),n.d(t,"a",(function(){return p})),n.d(t,"q",(function(){return d})),n.d(t,"e",(function(){return f})),n.d(t,"c",(function(){return h})),n.d(t,"h",(function(){return g})),n.d(t,"p",(function(){return b})),n.d(t,"o",(function(){return v})),n.d(t,"l",(function(){return y})),n.d(t,"g",(function(){return w})),n.d(t,"d",(function(){return x}));var a=n("b775");function r(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/list",method:"get",params:e})}function i(){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/enabled",method:"get"})}function o(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/"+e,method:"get"})}function l(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory",method:"put",data:e})}function u(e){return Object(a["a"])({url:"/miniapp/sensitiveWordCategory/"+e,method:"delete"})}function c(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/list",method:"get",params:e})}function m(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/"+e,method:"get"})}function p(e){return Object(a["a"])({url:"/miniapp/sensitiveWord",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/miniapp/sensitiveWord",method:"put",data:e})}function f(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/"+e,method:"delete"})}function h(e){return Object(a["a"])({url:"/miniapp/sensitiveWord/check",method:"post",params:{text:e}})}function g(e,t){return Object(a["a"])({url:"/miniapp/sensitiveWord/filter",method:"post",params:{text:e,replacement:t}})}function b(){return Object(a["a"])({url:"/miniapp/sensitiveWord/refresh",method:"post"})}function v(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/list",method:"get",params:e})}function y(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/"+e,method:"get"})}function w(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/"+e,method:"delete"})}function x(e){return Object(a["a"])({url:"/miniapp/sensitiveWordLog/clean",method:"delete",params:{days:e}})}}}]);