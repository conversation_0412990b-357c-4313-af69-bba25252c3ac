{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\xiqing\\activity.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\xiqing\\activity.js", "mtime": 1753694616738}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9yb290L0Rlc2t0b3AvXHU5ODc5XHU3NkVFXHU4QkIwXHU1RjU1XHVGRjA4XHU1NDM0XHU5Rjk5XHU5Rjk5XHVGRjA5L3RqdWhhaXRhbmdfbWluaWFwcC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0QWN0aXZpdHkgPSBnZXRBY3Rpdml0eTsKZXhwb3J0cy5saXN0QWN0aXZpdHkgPSBsaXN0QWN0aXZpdHk7CmV4cG9ydHMudXBkYXRlQWN0aXZpdHkgPSB1cGRhdGVBY3Rpdml0eTsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouilv+mdkumHkeenjeWtkOS4k+WMuua0u+WKqOWGheWuueeuoeeQhuWIl+ihqApmdW5jdGlvbiBsaXN0QWN0aXZpdHkocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouilv+mdkumHkeenjeWtkOS4k+WMuua0u+WKqOWGheWuueeuoeeQhuivpue7hgpmdW5jdGlvbiBnZXRBY3Rpdml0eShjb250ZW50SWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL3hpcWluZy9hY3Rpdml0eS8nICsgY29udGVudElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDkv67mlLnopb/pnZLph5Hnp43lrZDkuJPljLrmtLvliqjlhoXlrrnnrqHnkIYKZnVuY3Rpb24gdXBkYXRlQWN0aXZpdHkoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21pbmlhcHAveGlxaW5nL2FjdGl2aXR5JywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listActivity", "query", "request", "url", "method", "params", "getActivity", "contentId", "updateActivity", "data"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/xiqing/activity.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询西青金种子专区活动内容管理列表\r\nexport function listActivity(query) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询西青金种子专区活动内容管理详细\r\nexport function getActivity(contentId) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity/' + contentId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改西青金种子专区活动内容管理\r\nexport function updateActivity(data) {\r\n  return request({\r\n    url: '/miniapp/xiqing/activity',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,SAAS,EAAE;EACrC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,SAAS;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}