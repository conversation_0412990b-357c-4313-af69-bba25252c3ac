{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\xiqing\\registration-manage.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\xiqing\\registration-manage.js", "mtime": 1753694616741}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listRegistrationManage", "query", "request", "url", "method", "params", "getRegistrationManage", "registrationId", "delRegistrationManage", "registrationIds", "exportRegistrationManage", "auditRegistrationManage", "data"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/xiqing/registration-manage.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询西青金种子路演报名管理列表\r\nexport function listRegistrationManage(query) {\r\n  return request({\r\n    url: '/miniapp/xiqing/registration-manage/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询西青金种子路演报名管理详细\r\nexport function getRegistrationManage(registrationId) {\r\n  return request({\r\n    url: '/miniapp/xiqing/registration-manage/' + registrationId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除西青金种子路演报名管理\r\nexport function delRegistrationManage(registrationIds) {\r\n  return request({\r\n    url: '/miniapp/xiqing/registration-manage/' + registrationIds,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出西青金种子路演报名管理\r\nexport function exportRegistrationManage(query) {\r\n  return request({\r\n    url: '/miniapp/xiqing/registration-manage/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 审核路演报名\r\nexport function auditRegistrationManage(data) {\r\n  return request({\r\n    url: '/miniapp/xiqing/registration-manage/audit',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,qBAAqBA,CAACC,cAAc,EAAE;EACpD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC,GAAGI,cAAc;IAC5DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,qBAAqBA,CAACC,eAAe,EAAE;EACrD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC,GAAGM,eAAe;IAC7DL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,wBAAwBA,CAACT,KAAK,EAAE;EAC9C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}