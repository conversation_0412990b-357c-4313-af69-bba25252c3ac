{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue?vue&type=template&id=77376cec&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue", "mtime": 1753846134162}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1mb3JtIDptb2RlbD0icXVlcnlQYXJhbXMiIHJlZj0icXVlcnlGb3JtIiBzaXplPSJzbWFsbCIgOmlubGluZT0idHJ1ZSIgdi1zaG93PSJzaG93U2VhcmNoIiBsYWJlbC13aWR0aD0iNjhweCI+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnsbvlnovlkI3np7AiIHByb3A9ImNhdGVnb3J5TmFtZSI+CiAgICAgIDxlbC1pbnB1dAogICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLmNhdGVnb3J5TmFtZSIKICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl57G75Z6L5ZCN56ewIgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIEBrZXl1cC5lbnRlci5uYXRpdmU9ImhhbmRsZVF1ZXJ5IgogICAgICAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnirbmgIEiIHByb3A9InN0YXR1cyI+CiAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icXVlcnlQYXJhbXMuc3RhdHVzIiBwbGFjZWhvbGRlcj0i6K+36YCJ5oup54q25oCBIiBjbGVhcmFibGU+CiAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgdi1mb3I9ImRpY3QgaW4gZGljdC50eXBlLnN5c19ub3JtYWxfZGlzYWJsZSIKICAgICAgICAgIDprZXk9ImRpY3QudmFsdWUiCiAgICAgICAgICA6bGFiZWw9ImRpY3QubGFiZWwiCiAgICAgICAgICA6dmFsdWU9ImRpY3QudmFsdWUiCiAgICAgICAgLz4KICAgICAgPC9lbC1zZWxlY3Q+CiAgICA8L2VsLWZvcm0taXRlbT4KICAgIDxlbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgaWNvbj0iZWwtaWNvbi1zZWFyY2giIHNpemU9Im1pbmkiIEBjbGljaz0iaGFuZGxlUXVlcnkiPuaQnOe0ojwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIGljb249ImVsLWljb24tcmVmcmVzaCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJyZXNldFF1ZXJ5Ij7ph43nva48L2VsLWJ1dHRvbj4KICAgIDwvZWwtZm9ybS1pdGVtPgogIDwvZWwtZm9ybT4KCiAgPGVsLXJvdyA6Z3V0dGVyPSIxMCIgY2xhc3M9Im1iOCI+CiAgICA8IS0tIOmakOiXj+aWsOWinuaMiemSriAtLT4KICAgIDwhLS0gPGVsLWNvbCA6c3Bhbj0iMS41Ij4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgcGxhaW4KICAgICAgICBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICBAY2xpY2s9ImhhbmRsZUFkZCIKICAgICAgICB2LWhhc1Blcm1pPSJbJ21pbmlhcHA6ZGVtYW5kY2F0ZWdvcnk6YWRkJ10iCiAgICAgID7mlrDlop48L2VsLWJ1dHRvbj4KICAgIDwvZWwtY29sPiAtLT4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJzdWNjZXNzIgogICAgICAgIHBsYWluCiAgICAgICAgaWNvbj0iZWwtaWNvbi1lZGl0IgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgOmRpc2FibGVkPSJzaW5nbGUiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVVcGRhdGUiCiAgICAgICAgdi1oYXNQZXJtaT0iWydtaW5pYXBwOmRlbWFuZGNhdGVnb3J5OmVkaXQnXSIKICAgICAgPuS/ruaUuTwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+CiAgICA8IS0tIOmakOiXj+WIoOmZpOaMiemSriAtLT4KICAgIDwhLS0gPGVsLWNvbCA6c3Bhbj0iMS41Ij4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgOmRpc2FibGVkPSJtdWx0aXBsZSIKICAgICAgICBAY2xpY2s9ImhhbmRsZURlbGV0ZSIKICAgICAgICB2LWhhc1Blcm1pPSJbJ21pbmlhcHA6ZGVtYW5kY2F0ZWdvcnk6cmVtb3ZlJ10iCiAgICAgID7liKDpmaQ8L2VsLWJ1dHRvbj4KICAgIDwvZWwtY29sPiAtLT4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJ3YXJuaW5nIgogICAgICAgIHBsYWluCiAgICAgICAgaWNvbj0iZWwtaWNvbi1kb3dubG9hZCIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIEBjbGljaz0iaGFuZGxlRXhwb3J0IgogICAgICAgIHYtaGFzUGVybWk9IlsnbWluaWFwcDpkZW1hbmRjYXRlZ29yeTpleHBvcnQnXSIKICAgICAgPuWvvOWHujwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+CiAgICA8cmlnaHQtdG9vbGJhciA6c2hvd1NlYXJjaC5zeW5jPSJzaG93U2VhcmNoIiBAcXVlcnlUYWJsZT0iZ2V0TGlzdCI+PC9yaWdodC10b29sYmFyPgogIDwvZWwtcm93PgoKICA8ZWwtdGFibGUKICAgIHYtbG9hZGluZz0ibG9hZGluZyIKICAgIDpkYXRhPSJjYXRlZ29yeUxpc3QiCiAgICBAc2VsZWN0aW9uLWNoYW5nZT0iaGFuZGxlU2VsZWN0aW9uQ2hhbmdlIgogICAgcm93LWtleT0iY2F0ZWdvcnlJZCIKICA+CiAgICA8ZWwtdGFibGUtY29sdW1uIHR5cGU9InNlbGVjdGlvbiIgd2lkdGg9IjU1IiBhbGlnbj0iY2VudGVyIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i57G75Z6LSUQiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNhdGVnb3J5SWQiIHdpZHRoPSIxMDAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLnsbvlnovlkI3np7AiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNhdGVnb3J5TmFtZSIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9Iuexu+Wei+agh+ivhiIgYWxpZ249ImNlbnRlciIgcHJvcD0iY2F0ZWdvcnlDb2RlIiB3aWR0aD0iMTIwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i566A56ewIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJjYXRlZ29yeVNob3J0TmFtZSIgd2lkdGg9IjEyMCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWbvuaghyIgYWxpZ249ImNlbnRlciIgcHJvcD0iY2F0ZWdvcnlJY29uIiB3aWR0aD0iODAiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxpbWcgdi1pZj0ic2NvcGUucm93LmNhdGVnb3J5SWNvbiIgOnNyYz0ic2NvcGUucm93LmNhdGVnb3J5SWNvbiIgY2xhc3M9ImNhdGVnb3J5LWljb24iIGFsdD0i57G75Z6L5Zu+5qCHIiAvPgogICAgICAgIDxzcGFuIHYtZWxzZSBjbGFzcz0ibm8taWNvbiI+5peg5Zu+5qCHPC9zcGFuPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLnsbvlnovmj4/ov7AiIGFsaWduPSJjZW50ZXIiIHByb3A9ImNhdGVnb3J5RGVzYyIgc2hvdy1vdmVyZmxvdy10b29sdGlwIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmjpLluo8iIGFsaWduPSJjZW50ZXIiIHByb3A9InNvcnRPcmRlciIgd2lkdGg9IjEyMCI+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgdi1tb2RlbD0ic2NvcGUucm93LnNvcnRPcmRlciIKICAgICAgICAgIDptaW49IjAiCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgOmNvbnRyb2xzPSJmYWxzZSIKICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZVNvcnRDaGFuZ2Uoc2NvcGUucm93KSIKICAgICAgICAgIHN0eWxlPSJ3aWR0aDogODBweDsiCiAgICAgICAgLz4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i54q25oCBIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJzdGF0dXMiIHdpZHRoPSIxMDAiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxkaWN0LXRhZyA6b3B0aW9ucz0iZGljdC50eXBlLnN5c19ub3JtYWxfZGlzYWJsZSIgOnZhbHVlPSJzY29wZS5yb3cuc3RhdHVzIi8+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWIm+W7uuaXtumXtCIgYWxpZ249ImNlbnRlciIgcHJvcD0iY3JlYXRlVGltZSIgd2lkdGg9IjE4MCI+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPHNwYW4+e3sgcGFyc2VUaW1lKHNjb3BlLnJvdy5jcmVhdGVUaW1lLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKSB9fTwvc3Bhbj4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5pON5L2cIiBhbGlnbj0iY2VudGVyIiBjbGFzcy1uYW1lPSJzbWFsbC1wYWRkaW5nIGZpeGVkLXdpZHRoIiB3aWR0aD0iMTgwIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tZWRpdCIKICAgICAgICAgIEBjbGljaz0iaGFuZGxlVXBkYXRlKHNjb3BlLnJvdykiCiAgICAgICAgICB2LWhhc1Blcm1pPSJbJ21pbmlhcHA6ZGVtYW5kY2F0ZWdvcnk6ZWRpdCddIgogICAgICAgID7kv67mlLk8L2VsLWJ1dHRvbj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tc2V0dGluZyIKICAgICAgICAgIEBjbGljaz0iaGFuZGxlRm9ybUNvbmZpZyhzY29wZS5yb3cpIgogICAgICAgICAgdi1oYXNQZXJtaT0iWydtaW5pYXBwOmRlbWFuZGNhdGVnb3J5OmVkaXQnXSIKICAgICAgICA+6KGo5Y2V6YWN572uPC9lbC1idXR0b24+CiAgICAgICAgPCEtLSDpmpDol4/liKDpmaTmjInpkq4gLS0+CiAgICAgICAgPCEtLSA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgICAgQGNsaWNrPSJoYW5kbGVEZWxldGUoc2NvcGUucm93KSIKICAgICAgICAgIHYtaGFzUGVybWk9IlsnbWluaWFwcDpkZW1hbmRjYXRlZ29yeTpyZW1vdmUnXSIKICAgICAgICA+5Yig6ZmkPC9lbC1idXR0b24+IC0tPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgPC9lbC10YWJsZT4KCiAgPHBhZ2luYXRpb24KICAgIHYtc2hvdz0idG90YWwgPiAwIgogICAgOnRvdGFsPSJ0b3RhbCIKICAgIDpwYWdlLnN5bmM9InF1ZXJ5UGFyYW1zLnBhZ2VOdW0iCiAgICA6bGltaXQuc3luYz0icXVlcnlQYXJhbXMucGFnZVNpemUiCiAgICBAcGFnaW5hdGlvbj0iZ2V0TGlzdCIKICAvPgoKICA8IS0tIOa3u+WKoOaIluS/ruaUuemcgOaxguexu+Wei+WvueivneahhiAtLT4KICA8ZWwtZGlhbG9nIDp0aXRsZT0idGl0bGUiIDp2aXNpYmxlLnN5bmM9Im9wZW4iIHdpZHRoPSI5MDBweCIgYXBwZW5kLXRvLWJvZHk+CiAgICA8ZWwtZm9ybSByZWY9ImZvcm0iIDptb2RlbD0iZm9ybSIgOnJ1bGVzPSJydWxlcyIgbGFiZWwtd2lkdGg9IjEwMHB4Ij4KICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIyMCI+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i57G75Z6L5ZCN56ewIiBwcm9wPSJjYXRlZ29yeU5hbWUiPgogICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5jYXRlZ29yeU5hbWUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXnsbvlnovlkI3np7AiIC8+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnsbvlnovmoIfor4YiIHByb3A9ImNhdGVnb3J5Q29kZSI+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uY2F0ZWdvcnlDb2RlIgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXnsbvlnovmoIfor4bku6PnoIHvvIzlpoLvvJp0ZWNo44CBYnVzaW5lc3MiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSJmb3JtLmNhdGVnb3J5SWQgIT0gbnVsbCIKICAgICAgICAgICAgLz4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iZm9ybS10aXAiPgogICAgICAgICAgICAgIDxwPuKAoiDnlKjkuo7moIfor4bpnIDmsYLnsbvlnovnmoTllK/kuIDku6PnoIE8L3A+CiAgICAgICAgICAgICAgPHA+4oCiIOW7uuiuruagvOW8j++8mnRlY2jjgIFidXNpbmVzc+OAgXNlcnZpY2XnrYk8L3A+CiAgICAgICAgICAgICAgPHAgdi1pZj0iZm9ybS5jYXRlZ29yeUlkID09IG51bGwiPuKAoiDkuIDml6borr7nva7lkI7kuI3lj6/kv67mlLk8L3A+CiAgICAgICAgICAgICAgPHAgdi1lbHNlIHN0eWxlPSJjb2xvcjogI2Y1NmM2YzsiPuKAoiDnsbvlnovmoIfor4bkuI3lj6/kv67mlLk8L3A+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgIDwvZWwtcm93PgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnsbvlnovnroDnp7AiIHByb3A9ImNhdGVnb3J5U2hvcnROYW1lIj4KICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uY2F0ZWdvcnlTaG9ydE5hbWUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXnsbvlnovlkI3np7DnroDnp7AiIC8+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmjpLluo8iIHByb3A9InNvcnRPcmRlciI+CiAgICAgICAgICAgIDxlbC1pbnB1dC1udW1iZXIgdi1tb2RlbD0iZm9ybS5zb3J0T3JkZXIiIDptaW49IjAiIHBsYWNlaG9sZGVyPSLmlbDlrZfotorlsI/otorpnaDliY0iIHN0eWxlPSJ3aWR0aDogMTAwJSIgLz4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIyMCI+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i57G75Z6L5Zu+5qCHIiBwcm9wPSJjYXRlZ29yeUljb24iPgogICAgICAgICAgICA8SW1hZ2VVcGxvYWQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmNhdGVnb3J5SWNvbiIKICAgICAgICAgICAgICA6bGltaXQ9IjEiCiAgICAgICAgICAgICAgOmZpbGVTaXplPSIyIgogICAgICAgICAgICAgIDppc1Nob3dUaXA9InRydWUiCiAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZvcm0tdGlwIj4KICAgICAgICAgICAgICA8cD7igKIg5pSv5oyBIGpwZ+OAgXBuZ+OAgWdpZiDmoLzlvI88L3A+CiAgICAgICAgICAgICAgPHA+4oCiIOaWh+S7tuWkp+Wwj+S4jei2hei/hyAyTUI8L3A+CiAgICAgICAgICAgICAgPHA+4oCiIOW7uuiuruWwuuWvuCAzMngzMiDlg4/ntKDvvIzkv53or4HmuIXmmbDluqY8L3A+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgIDwvZWwtcm93PgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnsbvlnovmj4/ov7AiIHByb3A9ImNhdGVnb3J5RGVzYyI+CiAgICAgICAgICAgIDxFZGl0b3IKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmNhdGVnb3J5RGVzYyIKICAgICAgICAgICAgICA6bWluLWhlaWdodD0iMjAwIgogICAgICAgICAgICAgIDpoZWlnaHQ9IjMwMCIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl57G75Z6L5o+P6L+wLi4uIgogICAgICAgICAgICAvPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgIDwvZWwtcm93PgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnirbmgIEiIHByb3A9InN0YXR1cyI+CiAgICAgICAgICAgIDxlbC1yYWRpby1ncm91cCB2LW1vZGVsPSJmb3JtLnN0YXR1cyI+CiAgICAgICAgICAgICAgPGVsLXJhZGlvCiAgICAgICAgICAgICAgICB2LWZvcj0iZGljdCBpbiBkaWN0LnR5cGUuc3lzX25vcm1hbF9kaXNhYmxlIgogICAgICAgICAgICAgICAgOmtleT0iZGljdC52YWx1ZSIKICAgICAgICAgICAgICAgIDpsYWJlbD0iZGljdC52YWx1ZSIKICAgICAgICAgICAgICA+e3tkaWN0LmxhYmVsfX08L2VsLXJhZGlvPgogICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5aSH5rOoIiBwcm9wPSJyZW1hcmsiPgogICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5yZW1hcmsiIHR5cGU9InRleHRhcmVhIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5aSH5rOoIiA6cm93cz0iMyIgLz4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KICAgIDwvZWwtZm9ybT4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJzdWJtaXRGb3JtIj7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjYW5jZWwiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8IS0tIOihqOWNleWtl+autemFjee9ruWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nIHRpdGxlPSLooajljZXlrZfmrrXphY3nva4iIDp2aXNpYmxlLnN5bmM9ImZvcm1Db25maWdPcGVuIiB3aWR0aD0iMTQwMHB4IiBhcHBlbmQtdG8tYm9keT4KICAgIDxkaXYgY2xhc3M9ImZvcm0tY29uZmlnLWNvbnRhaW5lciI+CiAgICAgIDwhLS0g5bem5L6n6YWN572u5Yy65Z+fIC0tPgogICAgICA8ZGl2IGNsYXNzPSJjb25maWctYXJlYSI+CiAgICAgICAgPGRpdiBjbGFzcz0iY29uZmlnLWhlYWRlciI+CiAgICAgICAgICA8aDQ+5a2X5q616YWN572uPC9oND4KICAgICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlci1hY3Rpb25zIj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJzdWNjZXNzIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJhZGRGb3JtTW9kdWxlIiBpY29uPSJlbC1pY29uLWZvbGRlci1hZGQiPua3u+WKoOaooeWdlzwvZWwtYnV0dG9uPgogICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBAY2xpY2s9ImFkZEZvcm1GaWVsZCIgaWNvbj0iZWwtaWNvbi1wbHVzIj7mt7vliqDlrZfmrrU8L2VsLWJ1dHRvbj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgoKICAgICAgICA8IS0tIOmihOiuvuaooeadvyAtLT4KICAgICAgICA8ZGl2IGNsYXNzPSJ0ZW1wbGF0ZS1zZWN0aW9uIj4KICAgICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0ic2VsZWN0ZWRUZW1wbGF0ZSIgcGxhY2Vob2xkZXI9IumAieaLqemihOiuvuaooeadvyIgc2l6ZT0ic21hbGwiIEBjaGFuZ2U9ImFwcGx5VGVtcGxhdGUiPgogICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLln7rnoYDpnIDmsYLmqKHmnb8iIHZhbHVlPSJiYXNpYyIgLz4KICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oqA5pyv6ZyA5rGC5qih5p2/IiB2YWx1ZT0idGVjaCIgLz4KICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5ZWG5Yqh5ZCI5L2c5qih5p2/IiB2YWx1ZT0iYnVzaW5lc3MiIC8+CiAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPCEtLSDmqKHlnZflkozlrZfmrrXliJfooaggLS0+CiAgICAgICAgPGRpdiBjbGFzcz0ibW9kdWxlcy1saXN0Ij4KICAgICAgICAgIDxkaXYgdi1mb3I9Iihtb2R1bGUsIG1vZHVsZUluZGV4KSBpbiBmb3JtTW9kdWxlc0xpc3QiIDprZXk9Im1vZHVsZUluZGV4IiBjbGFzcz0ibW9kdWxlLWl0ZW0iPgogICAgICAgICAgICA8ZWwtY2FyZCBzaGFkb3c9ImhvdmVyIiBjbGFzcz0ibW9kdWxlLWNhcmQiPgogICAgICAgICAgICAgIDwhLS0g5qih5Z2X5aS06YOoIC0tPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im1vZHVsZS1oZWFkZXIiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibW9kdWxlLXRpdGxlLXNlY3Rpb24iPgogICAgICAgICAgICAgICAgICA8IS0tIOaooeWdl+WbvuaghyAtLT4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibW9kdWxlLWljb24tc2VjdGlvbiI+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibW9kdWxlLWljb24tZGlzcGxheSIgQGNsaWNrPSJvcGVuSWNvblVwbG9hZGVyKG1vZHVsZUluZGV4KSI+CiAgICAgICAgICAgICAgICAgICAgICA8aW1nIHYtaWY9Im1vZHVsZS5pY29uIiA6c3JjPSJtb2R1bGUuaWNvbiIgY2xhc3M9ImN1c3RvbS1pY29uIiBhbHQ9IuaooeWdl+WbvuaghyIgLz4KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgdi1lbHNlIGNsYXNzPSJkZWZhdWx0LWljb24tcGxhY2Vob2xkZXIiPgogICAgICAgICAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1wbHVzIj48L2k+CiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuS4iuS8oOWbvuaghzwvc3Bhbj4KICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxlbC1kcm9wZG93biB0cmlnZ2VyPSJjbGljayIgQGNvbW1hbmQ9ImhhbmRsZUljb25Db21tYW5kKCRldmVudCwgbW9kdWxlSW5kZXgpIiB2LWlmPSJtb2R1bGUuaWNvbiI+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIHNpemU9Im1pbmkiIGNsYXNzPSJpY29uLWVkaXQtYnRuIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tZWRpdCI+PC9pPgogICAgICAgICAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtZHJvcGRvd24tbWVudSBzbG90PSJkcm9wZG93biI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1kcm9wZG93bi1pdGVtIGNvbW1hbmQ9InVwbG9hZCI+6YeN5paw5LiK5LygPC9lbC1kcm9wZG93bi1pdGVtPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtZHJvcGRvd24taXRlbSBjb21tYW5kPSJyZW1vdmUiPuenu+mZpOWbvuaghzwvZWwtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICAgICAgICAgIDwvZWwtZHJvcGRvd24tbWVudT4KICAgICAgICAgICAgICAgICAgICA8L2VsLWRyb3Bkb3duPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9Im1vZHVsZS5uYW1lIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmqKHlnZflkI3np7AiCiAgICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9Im1vZHVsZS1uYW1lLWlucHV0IgogICAgICAgICAgICAgICAgICAgIEBibHVyPSJ2YWxpZGF0ZU1vZHVsZU5hbWUobW9kdWxlKSIKICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibW9kdWxlLWFjdGlvbnMiPgogICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIHNpemU9Im1pbmkiIEBjbGljaz0ibW92ZU1vZHVsZVVwKG1vZHVsZUluZGV4KSIgOmRpc2FibGVkPSJtb2R1bGVJbmRleCA9PT0gMCI+5LiK56e7PC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0idGV4dCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJtb3ZlTW9kdWxlRG93bihtb2R1bGVJbmRleCkiIDpkaXNhYmxlZD0ibW9kdWxlSW5kZXggPT09IGZvcm1Nb2R1bGVzTGlzdC5sZW5ndGggLSAxIj7kuIvnp7s8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBzaXplPSJtaW5pIiBAY2xpY2s9ImFkZEZpZWxkVG9Nb2R1bGUobW9kdWxlSW5kZXgpIiBpY29uPSJlbC1pY29uLXBsdXMiPua3u+WKoOWtl+autTwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIHNpemU9Im1pbmkiIEBjbGljaz0icmVtb3ZlTW9kdWxlKG1vZHVsZUluZGV4KSIgc3R5bGU9ImNvbG9yOiAjZjU2YzZjOyI+5Yig6Zmk5qih5Z2XPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KCgoKICAgICAgICAgICAgICA8IS0tIOWtl+auteWIl+ihqCAtLT4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWVsZHMtbGlzdCIgdi1pZj0ibW9kdWxlLmZpZWxkcyAmJiBtb2R1bGUuZmllbGRzLmxlbmd0aCA+IDAiPgogICAgICAgICAgICAgICAgPGRpdiB2LWZvcj0iKGZpZWxkLCBmaWVsZEluZGV4KSBpbiBtb2R1bGUuZmllbGRzIiA6a2V5PSJmaWVsZEluZGV4IiBjbGFzcz0iZmllbGQtaXRlbSI+CiAgICAgICAgICAgICAgICAgIDxlbC1jYXJkIHNoYWRvdz0iaG92ZXIiIGNsYXNzPSJmaWVsZC1jYXJkIj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWVsZC1oZWFkZXIiPgogICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImZpZWxkLXRpdGxlIj4KICAgICAgICAgICAgICAgICAgICAgICAge3sgZmllbGQubGFiZWwgfHwgJ+acquWRveWQjeWtl+autScgfX0KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLXRhZyB2LWlmPSJmaWVsZC5oaWRkZW4iIHNpemU9Im1pbmkiIHR5cGU9ImluZm8iIHN0eWxlPSJtYXJnaW4tbGVmdDogOHB4OyI+6ZqQ6JePPC9lbC10YWc+CiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJmaWVsZC1hY3Rpb25zIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBzaXplPSJtaW5pIiBAY2xpY2s9Im1vdmVGaWVsZFVwSW5Nb2R1bGUobW9kdWxlSW5kZXgsIGZpZWxkSW5kZXgpIiA6ZGlzYWJsZWQ9ImZpZWxkSW5kZXggPT09IDAiPuS4iuenuzwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIHNpemU9Im1pbmkiIEBjbGljaz0ibW92ZUZpZWxkRG93bkluTW9kdWxlKG1vZHVsZUluZGV4LCBmaWVsZEluZGV4KSIgOmRpc2FibGVkPSJmaWVsZEluZGV4ID09PSBtb2R1bGUuZmllbGRzLmxlbmd0aCAtIDEiPuS4i+enuzwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIHNpemU9Im1pbmkiIEBjbGljaz0icmVtb3ZlRmllbGRGcm9tTW9kdWxlKG1vZHVsZUluZGV4LCBmaWVsZEluZGV4KSIgc3R5bGU9ImNvbG9yOiAjZjU2YzZjOyI+5Yig6ZmkPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgICAgPGVsLWZvcm0gOm1vZGVsPSJmaWVsZCIgbGFiZWwtd2lkdGg9IjgwcHgiIHNpemU9InNtYWxsIj4KICAgICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWtl+auteagh+etviI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmaWVsZC5sYWJlbCIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWtl+auteagh+etviIgQGlucHV0PSJnZW5lcmF0ZUZpZWxkTmFtZShmaWVsZCkiIC8+CiAgICAgICAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWtl+auteexu+WeiyI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iZmllbGQudHlwZSIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeWtl+auteexu+WeiyIgQGNoYW5nZT0ib25GaWVsZFR5cGVDaGFuZ2UoZmllbGQpIj4KICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmlofmnKzovpPlhaUiIHZhbHVlPSJpbnB1dCIgLz4KICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlpJrooYzmlofmnKwiIHZhbHVlPSJ0ZXh0YXJlYSIgLz4KICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmlbDlrZfovpPlhaUiIHZhbHVlPSJudW1iZXIiIC8+CiAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i55S16K+d5Y+356CBIiB2YWx1ZT0idGVsIiAvPgogICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IumCrueuseWcsOWdgCIgdmFsdWU9ImVtYWlsIiAvPgogICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuWNlemAieahhiIgdmFsdWU9InJhZGlvIiAvPgogICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuWkmumAieahhiIgdmFsdWU9ImNoZWNrYm94IiAvPgogICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuS4i+aLiemAieaLqSIgdmFsdWU9InNlbGVjdCIgLz4KICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLml6XmnJ/pgInmi6kiIHZhbHVlPSJkYXRlIiAvPgogICAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaXtumXtOmAieaLqSIgdmFsdWU9InRpbWUiIC8+CiAgICAgICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5paH5Lu25LiK5LygIiB2YWx1ZT0iZmlsZSIgLz4KICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLpnZnmgIHlsZXnpLoiIHZhbHVlPSJzdGF0aWMiIC8+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmj5DnpLrmloflrZciIHYtaWY9ImZpZWxkLnR5cGUgIT09ICdzdGF0aWMnIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZpZWxkLnBsYWNlaG9sZGVyIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5o+Q56S65paH5a2X77yM5aaC77ya6K+36L6T5YWl5aeT5ZCNIiAvPgogICAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmmL7npLrlhoXlrrkiIHYtaWY9ImZpZWxkLnR5cGUgPT09ICdzdGF0aWMnIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZpZWxkLnN0YXRpY0NvbnRlbnQiIHR5cGU9InRleHRhcmVhIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl6KaB5pi+56S655qE6Z2Z5oCB5YaF5a65IiA6cm93cz0iMyIgLz4KICAgICAgICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5piv5ZCm5b+F5aGrIiB2LWlmPSJmaWVsZC50eXBlICE9PSAnc3RhdGljJyI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1zd2l0Y2ggdi1tb2RlbD0iZmllbGQucmVxdWlyZWQiIC8+CiAgICAgICAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaYr+WQpumakOiXjyI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbC1zd2l0Y2ggdi1tb2RlbD0iZmllbGQuaGlkZGVuIiBAY2hhbmdlPSJvbkZpZWxkSGlkZGVuQ2hhbmdlKGZpZWxkKSIgLz4KICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZmllbGQtdGlwIiB2LWlmPSJmaWVsZC5oaWRkZW4iPgogICAgICAgICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWluZm8iPjwvaT4KICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7pmpDol4/nmoTlrZfmrrXlnKjlsI/nqIvluo/nq6/kuI3kvJrmmL7npLrnu5nnlKjmiLc8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpgInpobnphY3nva4iIHYtaWY9IlsncmFkaW8nLCAnY2hlY2tib3gnLCAnc2VsZWN0J10uaW5jbHVkZXMoZmllbGQudHlwZSkiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZmllbGQub3B0aW9ucyIgdHlwZT0idGV4dGFyZWEiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXpgInpobnvvIznlKjpgJflj7fliIbpmpQiIDpyb3dzPSIyIiAvPgogICAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgICAgICAgPC9lbC1mb3JtPgogICAgICAgICAgICAgICAgICA8L2VsLWNhcmQ+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgPCEtLSDnqbrmqKHlnZfmj5DnpLogLS0+CiAgICAgICAgICAgICAgPGRpdiB2LWVsc2UgY2xhc3M9ImVtcHR5LW1vZHVsZSI+CiAgICAgICAgICAgICAgICA8ZWwtZW1wdHkgZGVzY3JpcHRpb249IuaaguaXoOWtl+autSIgOmltYWdlLXNpemU9IjYwIj4KICAgICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBzaXplPSJzbWFsbCIgQGNsaWNrPSJhZGRGaWVsZFRvTW9kdWxlKG1vZHVsZUluZGV4KSI+5re75Yqg5a2X5q61PC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICA8L2VsLWVtcHR5PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWNhcmQ+CiAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICA8IS0tIOepuueKtuaAgSAtLT4KICAgICAgICAgIDxkaXYgdi1pZj0iZm9ybU1vZHVsZXNMaXN0Lmxlbmd0aCA9PT0gMCIgY2xhc3M9ImVtcHR5LW1vZHVsZXMiPgogICAgICAgICAgICA8ZWwtZW1wdHkgZGVzY3JpcHRpb249IuaaguaXoOaooeWdlyIgOmltYWdlLXNpemU9IjgwIj4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0iYWRkRm9ybU1vZHVsZSI+5Yib5bu656ys5LiA5Liq5qih5Z2XPC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvZWwtZW1wdHk+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIOWPs+S+p+mihOiniOWMuuWfnyAtLT4KICAgICAgPGRpdiBjbGFzcz0icHJldmlldy1hcmVhIj4KICAgICAgICA8ZGl2IGNsYXNzPSJwcmV2aWV3LWhlYWRlciI+CiAgICAgICAgICA8aDQ+6KGo5Y2V6aKE6KeIPC9oND4KICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0ic3VjY2VzcyIgc2l6ZT0ic21hbGwiIEBjbGljaz0icHJldmlld0Zvcm0iPumihOiniOihqOWNlTwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9InByZXZpZXctY29udGVudCI+CiAgICAgICAgICA8ZGl2IHYtZm9yPSIobW9kdWxlLCBtb2R1bGVJbmRleCkgaW4gZm9ybU1vZHVsZXNMaXN0IiA6a2V5PSJtb2R1bGVJbmRleCIgY2xhc3M9InByZXZpZXctbW9kdWxlIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0icHJldmlldy1tb2R1bGUtaGVhZGVyIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcmV2aWV3LW1vZHVsZS10aXRsZSI+CiAgICAgICAgICAgICAgICA8aW1nIHYtaWY9Im1vZHVsZS5pY29uIiA6c3JjPSJtb2R1bGUuaWNvbiIgY2xhc3M9InByZXZpZXctbW9kdWxlLWljb24taW1nIiBhbHQ9IuaooeWdl+WbvuaghyIgLz4KICAgICAgICAgICAgICAgIDxpIHYtZWxzZSBjbGFzcz0iZWwtaWNvbi1mb2xkZXItb3BlbmVkIHByZXZpZXctbW9kdWxlLWljb24iPjwvaT4KICAgICAgICAgICAgICAgIDxoNT57eyBtb2R1bGUubmFtZSB8fCAn5pyq5ZG95ZCN5qih5Z2XJyB9fTwvaDU+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZWwtZm9ybSBsYWJlbC13aWR0aD0iMTAwcHgiIHNpemU9InNtYWxsIiB2LWlmPSJtb2R1bGUuZmllbGRzICYmIG1vZHVsZS5maWVsZHMubGVuZ3RoID4gMCI+CiAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSB2LWZvcj0iZmllbGQgaW4gbW9kdWxlLmZpZWxkcyIgOmtleT0iZmllbGQubmFtZSIgOmxhYmVsPSJmaWVsZC5sYWJlbCIgOnJlcXVpcmVkPSJmaWVsZC5yZXF1aXJlZCAmJiBmaWVsZC50eXBlICE9PSAnc3RhdGljJyIgOmNsYXNzPSJ7ICdoaWRkZW4tZmllbGQnOiBmaWVsZC5oaWRkZW4gfSI+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9ImZpZWxkLmhpZGRlbiIgY2xhc3M9ImhpZGRlbi1maWVsZC1pbmRpY2F0b3IiPgogICAgICAgICAgICAgICAgICA8ZWwtdGFnIHNpemU9Im1pbmkiIHR5cGU9ImluZm8iPumakOiXj+Wtl+autTwvZWwtdGFnPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8IS0tIOmdmeaAgeWxleekuiAtLT4KICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iZmllbGQudHlwZSA9PT0gJ3N0YXRpYyciIGNsYXNzPSJzdGF0aWMtY29udGVudCI+CiAgICAgICAgICAgICAgICAgIHt7IGZpZWxkLnN0YXRpY0NvbnRlbnQgfHwgJ+aaguaXoOWGheWuuScgfX0KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPCEtLSDmlofmnKzovpPlhaUgLS0+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAnaW5wdXQnIiB2LW1vZGVsPSJwcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSIgOnBsYWNlaG9sZGVyPSJmaWVsZC5wbGFjZWhvbGRlciB8fCAn6K+36L6T5YWlJyIgZGlzYWJsZWQgLz4KICAgICAgICAgICAgICAgIDwhLS0g5aSa6KGM5paH5pysIC0tPgogICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ3RleHRhcmVhJyIgdi1tb2RlbD0icHJldmlld0RhdGFbZmllbGQubmFtZV0iIHR5cGU9InRleHRhcmVhIiA6cGxhY2Vob2xkZXI9ImZpZWxkLnBsYWNlaG9sZGVyIHx8ICfor7fovpPlhaUnIiBkaXNhYmxlZCAvPgogICAgICAgICAgICAgICAgPCEtLSDmlbDlrZfovpPlhaUgLS0+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQtbnVtYmVyIHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ251bWJlciciIHYtbW9kZWw9InByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdIiA6cGxhY2Vob2xkZXI9ImZpZWxkLnBsYWNlaG9sZGVyIHx8ICfor7fovpPlhaXmlbDlrZcnIiBkaXNhYmxlZCAvPgogICAgICAgICAgICAgICAgPCEtLSDnlLXor53lj7fnoIEgLS0+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAndGVsJyIgdi1tb2RlbD0icHJldmlld0RhdGFbZmllbGQubmFtZV0iIDpwbGFjZWhvbGRlcj0iZmllbGQucGxhY2Vob2xkZXIgfHwgJ+ivt+i+k+WFpeeUteivneWPt+eggSciIGRpc2FibGVkIC8+CiAgICAgICAgICAgICAgICA8IS0tIOmCrueuseWcsOWdgCAtLT4KICAgICAgICAgICAgICAgIDxlbC1pbnB1dCB2LWVsc2UtaWY9ImZpZWxkLnR5cGUgPT09ICdlbWFpbCciIHYtbW9kZWw9InByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdIiA6cGxhY2Vob2xkZXI9ImZpZWxkLnBsYWNlaG9sZGVyIHx8ICfor7fovpPlhaXpgq7nrrHlnLDlnYAnIiBkaXNhYmxlZCAvPgogICAgICAgICAgICAgICAgPCEtLSDljZXpgInmoYYgLS0+CiAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAncmFkaW8nIiB2LW1vZGVsPSJwcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSIgZGlzYWJsZWQ+CiAgICAgICAgICAgICAgICAgIDxlbC1yYWRpbyB2LWZvcj0ib3B0aW9uIGluIGdldEZpZWxkT3B0aW9ucyhmaWVsZCkiIDprZXk9Im9wdGlvbiIgOmxhYmVsPSJvcHRpb24iPnt7IG9wdGlvbiB9fTwvZWwtcmFkaW8+CiAgICAgICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICAgICAgPCEtLSDlpJrpgInmoYYgLS0+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3gtZ3JvdXAgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAnY2hlY2tib3gnIiB2LW1vZGVsPSJwcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSIgZGlzYWJsZWQ+CiAgICAgICAgICAgICAgICAgIDxlbC1jaGVja2JveCB2LWZvcj0ib3B0aW9uIGluIGdldEZpZWxkT3B0aW9ucyhmaWVsZCkiIDprZXk9Im9wdGlvbiIgOmxhYmVsPSJvcHRpb24iPnt7IG9wdGlvbiB9fTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgICAgICA8L2VsLWNoZWNrYm94LWdyb3VwPgogICAgICAgICAgICAgICAgPCEtLSDkuIvmi4npgInmi6kgLS0+CiAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0IHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ3NlbGVjdCciIHYtbW9kZWw9InByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdIiA6cGxhY2Vob2xkZXI9ImZpZWxkLnBsYWNlaG9sZGVyIHx8ICfor7fpgInmi6knIiBkaXNhYmxlZD4KICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiB2LWZvcj0ib3B0aW9uIGluIGdldEZpZWxkT3B0aW9ucyhmaWVsZCkiIDprZXk9Im9wdGlvbiIgOmxhYmVsPSJvcHRpb24iIDp2YWx1ZT0ib3B0aW9uIiAvPgogICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgICAgICA8IS0tIOaXpeacn+mAieaLqSAtLT4KICAgICAgICAgICAgICAgIDxlbC1kYXRlLXBpY2tlciB2LWVsc2UtaWY9ImZpZWxkLnR5cGUgPT09ICdkYXRlJyIgdi1tb2RlbD0icHJldmlld0RhdGFbZmllbGQubmFtZV0iIHR5cGU9ImRhdGUiIDpwbGFjZWhvbGRlcj0iZmllbGQucGxhY2Vob2xkZXIgfHwgJ+ivt+mAieaLqeaXpeacnyciIGRpc2FibGVkIC8+CiAgICAgICAgICAgICAgICA8IS0tIOaXtumXtOmAieaLqSAtLT4KICAgICAgICAgICAgICAgIDxlbC10aW1lLXBpY2tlciB2LWVsc2UtaWY9ImZpZWxkLnR5cGUgPT09ICd0aW1lJyIgdi1tb2RlbD0icHJldmlld0RhdGFbZmllbGQubmFtZV0iIDpwbGFjZWhvbGRlcj0iZmllbGQucGxhY2Vob2xkZXIgfHwgJ+ivt+mAieaLqeaXtumXtCciIGRpc2FibGVkIC8+CiAgICAgICAgICAgICAgICA8IS0tIOaWh+S7tuS4iuS8oCAtLT4KICAgICAgICAgICAgICAgIDxlbC11cGxvYWQgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAnZmlsZSciIGFjdGlvbj0iIyIgZGlzYWJsZWQ+CiAgICAgICAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIHR5cGU9InByaW1hcnkiIGRpc2FibGVkPnt7IGZpZWxkLnBsYWNlaG9sZGVyIHx8ICfngrnlh7vkuIrkvKAnIH19PC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICA8L2VsLXVwbG9hZD4KICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPC9lbC1mb3JtPgogICAgICAgICAgICA8ZGl2IHYtZWxzZSBjbGFzcz0icHJldmlldy1lbXB0eS1tb2R1bGUiPgogICAgICAgICAgICAgIDxzcGFuPuivpeaooeWdl+aaguaXoOWtl+autTwvc3Bhbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICA8IS0tIOepuueKtuaAgSAtLT4KICAgICAgICAgIDxkaXYgdi1pZj0iZm9ybU1vZHVsZXNMaXN0Lmxlbmd0aCA9PT0gMCIgY2xhc3M9InByZXZpZXctZW1wdHkiPgogICAgICAgICAgICA8ZWwtZW1wdHkgZGVzY3JpcHRpb249IuaaguaXoOihqOWNlemFjee9riIgOmltYWdlLXNpemU9IjYwIiAvPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9InNhdmVGb3JtQ29uZmlnIj7kv53lrZjphY3nva48L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImNhbmNlbEZvcm1Db25maWciPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8IS0tIOihqOWNlemihOiniOWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nIHRpdGxlPSLooajljZXpooTop4giIDp2aXNpYmxlLnN5bmM9InByZXZpZXdPcGVuIiB3aWR0aD0iODAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGRpdiBjbGFzcz0icHJldmlldy1kaWFsb2ctY29udGVudCI+CiAgICAgIDxkaXYgdi1mb3I9Iihtb2R1bGUsIG1vZHVsZUluZGV4KSBpbiBmb3JtTW9kdWxlc0xpc3QiIDprZXk9Im1vZHVsZUluZGV4IiBjbGFzcz0icHJldmlldy1kaWFsb2ctbW9kdWxlIj4KICAgICAgICA8ZGl2IGNsYXNzPSJwcmV2aWV3LWRpYWxvZy1tb2R1bGUtaGVhZGVyIj4KICAgICAgICAgIDxkaXYgY2xhc3M9InByZXZpZXctZGlhbG9nLW1vZHVsZS10aXRsZSI+CiAgICAgICAgICAgIDxpbWcgdi1pZj0ibW9kdWxlLmljb24iIDpzcmM9Im1vZHVsZS5pY29uIiBjbGFzcz0icHJldmlldy1kaWFsb2ctbW9kdWxlLWljb24taW1nIiBhbHQ9IuaooeWdl+WbvuaghyIgLz4KICAgICAgICAgICAgPGkgdi1lbHNlIGNsYXNzPSJlbC1pY29uLWZvbGRlci1vcGVuZWQgcHJldmlldy1kaWFsb2ctbW9kdWxlLWljb24iPjwvaT4KICAgICAgICAgICAgPGg0Pnt7IG1vZHVsZS5uYW1lIHx8ICfmnKrlkb3lkI3mqKHlnZcnIH19PC9oND4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1mb3JtIGxhYmVsLXdpZHRoPSIxMDBweCIgdi1pZj0ibW9kdWxlLmZpZWxkcyAmJiBtb2R1bGUuZmllbGRzLmxlbmd0aCA+IDAiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSB2LWZvcj0iZmllbGQgaW4gbW9kdWxlLmZpZWxkcyIgOmtleT0iZmllbGQubmFtZSIgOmxhYmVsPSJmaWVsZC5sYWJlbCIgOnJlcXVpcmVkPSJmaWVsZC5yZXF1aXJlZCAmJiBmaWVsZC50eXBlICE9PSAnc3RhdGljJyIgOmNsYXNzPSJ7ICdoaWRkZW4tZmllbGQnOiBmaWVsZC5oaWRkZW4gfSI+CiAgICAgICAgICAgIDxkaXYgdi1pZj0iZmllbGQuaGlkZGVuIiBjbGFzcz0iaGlkZGVuLWZpZWxkLWluZGljYXRvciI+CiAgICAgICAgICAgICAgPGVsLXRhZyBzaXplPSJtaW5pIiB0eXBlPSJpbmZvIj7pmpDol4/lrZfmrrU8L2VsLXRhZz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwhLS0g6Z2Z5oCB5bGV56S6IC0tPgogICAgICAgICAgICA8ZGl2IHYtaWY9ImZpZWxkLnR5cGUgPT09ICdzdGF0aWMnIiBjbGFzcz0ic3RhdGljLWNvbnRlbnQiPgogICAgICAgICAgICAgIHt7IGZpZWxkLnN0YXRpY0NvbnRlbnQgfHwgJ+aaguaXoOWGheWuuScgfX0KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwhLS0g5paH5pys6L6T5YWlIC0tPgogICAgICAgICAgICA8ZWwtaW5wdXQgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAnaW5wdXQnIiB2LW1vZGVsPSJwcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSIgOnBsYWNlaG9sZGVyPSJmaWVsZC5wbGFjZWhvbGRlciB8fCAn6K+36L6T5YWlJyIgLz4KICAgICAgICAgICAgPCEtLSDlpJrooYzmlofmnKwgLS0+CiAgICAgICAgICAgIDxlbC1pbnB1dCB2LWVsc2UtaWY9ImZpZWxkLnR5cGUgPT09ICd0ZXh0YXJlYSciIHYtbW9kZWw9InByZXZpZXdEaWFsb2dEYXRhW2ZpZWxkLm5hbWVdIiB0eXBlPSJ0ZXh0YXJlYSIgOnBsYWNlaG9sZGVyPSJmaWVsZC5wbGFjZWhvbGRlciB8fCAn6K+36L6T5YWlJyIgLz4KICAgICAgICAgICAgPCEtLSDmlbDlrZfovpPlhaUgLS0+CiAgICAgICAgICAgIDxlbC1pbnB1dC1udW1iZXIgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAnbnVtYmVyJyIgdi1tb2RlbD0icHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0iIDpwbGFjZWhvbGRlcj0iZmllbGQucGxhY2Vob2xkZXIgfHwgJ+ivt+i+k+WFpeaVsOWtlyciIC8+CiAgICAgICAgICAgIDwhLS0g55S16K+d5Y+356CBIC0tPgogICAgICAgICAgICA8ZWwtaW5wdXQgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAndGVsJyIgdi1tb2RlbD0icHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0iIDpwbGFjZWhvbGRlcj0iZmllbGQucGxhY2Vob2xkZXIgfHwgJ+ivt+i+k+WFpeeUteivneWPt+eggSciIC8+CiAgICAgICAgICAgIDwhLS0g6YKu566x5Zyw5Z2AIC0tPgogICAgICAgICAgICA8ZWwtaW5wdXQgdi1lbHNlLWlmPSJmaWVsZC50eXBlID09PSAnZW1haWwnIiB2LW1vZGVsPSJwcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSIgOnBsYWNlaG9sZGVyPSJmaWVsZC5wbGFjZWhvbGRlciB8fCAn6K+36L6T5YWl6YKu566x5Zyw5Z2AJyIgLz4KICAgICAgICAgICAgPCEtLSDljZXpgInmoYYgLS0+CiAgICAgICAgICAgIDxlbC1yYWRpby1ncm91cCB2LWVsc2UtaWY9ImZpZWxkLnR5cGUgPT09ICdyYWRpbyciIHYtbW9kZWw9InByZXZpZXdEaWFsb2dEYXRhW2ZpZWxkLm5hbWVdIj4KICAgICAgICAgICAgICA8ZWwtcmFkaW8gdi1mb3I9Im9wdGlvbiBpbiBnZXRGaWVsZE9wdGlvbnMoZmllbGQpIiA6a2V5PSJvcHRpb24iIDpsYWJlbD0ib3B0aW9uIj57eyBvcHRpb24gfX08L2VsLXJhZGlvPgogICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICA8IS0tIOWkmumAieahhiAtLT4KICAgICAgICAgICAgPGVsLWNoZWNrYm94LWdyb3VwIHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94JyIgdi1tb2RlbD0icHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0iPgogICAgICAgICAgICAgIDxlbC1jaGVja2JveCB2LWZvcj0ib3B0aW9uIGluIGdldEZpZWxkT3B0aW9ucyhmaWVsZCkiIDprZXk9Im9wdGlvbiIgOmxhYmVsPSJvcHRpb24iPnt7IG9wdGlvbiB9fTwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgICAgICAgICAgIDwhLS0g5LiL5ouJ6YCJ5oupIC0tPgogICAgICAgICAgICA8ZWwtc2VsZWN0IHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ3NlbGVjdCciIHYtbW9kZWw9InByZXZpZXdEaWFsb2dEYXRhW2ZpZWxkLm5hbWVdIiA6cGxhY2Vob2xkZXI9ImZpZWxkLnBsYWNlaG9sZGVyIHx8ICfor7fpgInmi6knIj4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJvcHRpb24gaW4gZ2V0RmllbGRPcHRpb25zKGZpZWxkKSIgOmtleT0ib3B0aW9uIiA6bGFiZWw9Im9wdGlvbiIgOnZhbHVlPSJvcHRpb24iIC8+CiAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8IS0tIOaXpeacn+mAieaLqSAtLT4KICAgICAgICAgICAgPGVsLWRhdGUtcGlja2VyIHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ2RhdGUnIiB2LW1vZGVsPSJwcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSIgdHlwZT0iZGF0ZSIgOnBsYWNlaG9sZGVyPSJmaWVsZC5wbGFjZWhvbGRlciB8fCAn6K+36YCJ5oup5pel5pyfJyIgLz4KICAgICAgICAgICAgPCEtLSDml7bpl7TpgInmi6kgLS0+CiAgICAgICAgICAgIDxlbC10aW1lLXBpY2tlciB2LWVsc2UtaWY9ImZpZWxkLnR5cGUgPT09ICd0aW1lJyIgdi1tb2RlbD0icHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0iIDpwbGFjZWhvbGRlcj0iZmllbGQucGxhY2Vob2xkZXIgfHwgJ+ivt+mAieaLqeaXtumXtCciIC8+CiAgICAgICAgICAgIDwhLS0g5paH5Lu25LiK5LygIC0tPgogICAgICAgICAgICA8ZWwtdXBsb2FkIHYtZWxzZS1pZj0iZmllbGQudHlwZSA9PT0gJ2ZpbGUnIiBhY3Rpb249IiMiPgogICAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIHR5cGU9InByaW1hcnkiPnt7IGZpZWxkLnBsYWNlaG9sZGVyIHx8ICfngrnlh7vkuIrkvKAnIH19PC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvZWwtdXBsb2FkPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1mb3JtPgogICAgICAgIDxkaXYgdi1lbHNlIGNsYXNzPSJwcmV2aWV3LWRpYWxvZy1lbXB0eS1tb2R1bGUiPgogICAgICAgICAgPHNwYW4+6K+l5qih5Z2X5pqC5peg5a2X5q61PC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KCiAgICAgIDwhLS0g56m654q25oCBIC0tPgogICAgICA8ZGl2IHYtaWY9ImZvcm1Nb2R1bGVzTGlzdC5sZW5ndGggPT09IDAiIGNsYXNzPSJwcmV2aWV3LWRpYWxvZy1lbXB0eSI+CiAgICAgICAgPGVsLWVtcHR5IGRlc2NyaXB0aW9uPSLmmoLml6DooajljZXphY3nva4iIDppbWFnZS1zaXplPSI2MCIgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJwcmV2aWV3T3BlbiA9IGZhbHNlIj7lhbMg6ZetPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLSDlm77moIfkuIrkvKDlr7nor53moYYgLS0+CiAgPGVsLWRpYWxvZyB0aXRsZT0i5LiK5Lyg5qih5Z2X5Zu+5qCHIiA6dmlzaWJsZS5zeW5jPSJpY29uVXBsb2FkZXJPcGVuIiB3aWR0aD0iNTAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGRpdiBjbGFzcz0iaWNvbi11cGxvYWRlci1jb250ZW50Ij4KICAgICAgPGRpdiBjbGFzcz0idXBsb2FkLXNlY3Rpb24iPgogICAgICAgIDxoND7kuIrkvKDoh6rlrprkuYnlm77moIc8L2g0PgogICAgICAgIDxJbWFnZVVwbG9hZAogICAgICAgICAgdi1tb2RlbD0idXBsb2FkZWRJY29uIgogICAgICAgICAgOmxpbWl0PSIxIgogICAgICAgICAgOmZpbGVTaXplPSIyIgogICAgICAgICAgOmlzU2hvd1RpcD0idHJ1ZSIKICAgICAgICAvPgogICAgICAgIDxkaXYgY2xhc3M9InVwbG9hZC10aXBzIj4KICAgICAgICAgIDxwPuKAoiDmlK/mjIEganBn44CBcG5n44CBZ2lmIOagvOW8jzwvcD4KICAgICAgICAgIDxwPuKAoiDmlofku7blpKflsI/kuI3otoXov4cgMk1CPC9wPgogICAgICAgICAgPHA+4oCiIOW7uuiuruWwuuWvuCAzMngzMiDlg4/ntKDvvIzkv53or4HmuIXmmbDluqY8L3A+CiAgICAgICAgICA8cD7igKIg5bu66K6u5L2/55So6YCP5piO6IOM5pmv55qEUE5H5qC85byPPC9wPgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImNhbmNlbEljb25VcGxvYWQiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImNvbmZpcm1JY29uVXBsb2FkIiA6ZGlzYWJsZWQ9IiF1cGxvYWRlZEljb24iPuehriDlrpo8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgo8L2Rpdj4K"}, null]}