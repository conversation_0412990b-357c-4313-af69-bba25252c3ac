<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段名称唯一性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>活动表单字段名称唯一性测试</h1>
    
    <div class="test-section">
        <h2>测试1: 基础唯一性生成</h2>
        <div id="test1-results"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 重复标签处理</h2>
        <div id="test2-results"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 中文标签转换</h2>
        <div id="test3-results"></div>
    </div>

    <script>
        // 模拟Vue组件中的方法
        class FormFieldManager {
            constructor() {
                this.formFields = [];
            }

            // 生成唯一字段名称
            generateUniqueFieldName(label) {
                const pinyin = {
                    '姓名': 'name',
                    '联系电话': 'phone',
                    '电话': 'phone',
                    '邮箱': 'email',
                    '邮箱地址': 'email',
                    '公司': 'company',
                    '项目名称': 'project_name',
                    '项目描述': 'project_description',
                    '团队规模': 'team_size'
                };
                
                // 生成基础名称
                let baseName = pinyin[label] || label.toLowerCase().replace(/[\s\u4e00-\u9fa5]+/g, '_').replace(/[^\w_]/g, '');
                
                // 确保名称不为空
                if (!baseName) {
                    baseName = 'field';
                }
                
                // 检查名称是否已存在，如果存在则添加数字后缀
                let uniqueName = baseName;
                let counter = 1;
                
                while (this.isFieldNameExists(uniqueName)) {
                    uniqueName = `${baseName}_${counter}`;
                    counter++;
                }
                
                return uniqueName;
            }

            // 检查字段名称是否已存在
            isFieldNameExists(name) {
                return this.formFields.some(field => field.name === name);
            }

            // 添加字段
            addField(label) {
                const name = this.generateUniqueFieldName(label);
                this.formFields.push({ name, label });
                return name;
            }

            // 清空字段
            clearFields() {
                this.formFields = [];
            }
        }

        // 运行测试
        function runTests() {
            const manager = new FormFieldManager();

            // 测试1: 基础唯一性生成
            const test1Results = document.getElementById('test1-results');
            manager.clearFields();
            
            const basicLabels = ['姓名', '电话', '邮箱', '公司'];
            const basicResults = basicLabels.map(label => {
                const name = manager.addField(label);
                return `标签: "${label}" → 字段名: "${name}"`;
            });
            
            test1Results.innerHTML = basicResults.map(result => 
                `<div class="result success">${result}</div>`
            ).join('');

            // 测试2: 重复标签处理
            const test2Results = document.getElementById('test2-results');
            manager.clearFields();
            
            const duplicateLabels = ['姓名', '姓名', '姓名', '电话', '电话'];
            const duplicateResults = duplicateLabels.map(label => {
                const name = manager.addField(label);
                return `标签: "${label}" → 字段名: "${name}"`;
            });
            
            test2Results.innerHTML = duplicateResults.map(result => 
                `<div class="result success">${result}</div>`
            ).join('');

            // 测试3: 中文标签转换
            const test3Results = document.getElementById('test3-results');
            manager.clearFields();
            
            const chineseLabels = ['用户姓名', '联系方式', '工作单位', '项目介绍', '其他信息'];
            const chineseResults = chineseLabels.map(label => {
                const name = manager.addField(label);
                return `标签: "${label}" → 字段名: "${name}"`;
            });
            
            test3Results.innerHTML = chineseResults.map(result => 
                `<div class="result success">${result}</div>`
            ).join('');

            // 验证唯一性
            const allNames = manager.formFields.map(field => field.name);
            const uniqueNames = [...new Set(allNames)];
            
            if (allNames.length === uniqueNames.length) {
                document.body.insertAdjacentHTML('beforeend', 
                    '<div class="test-section"><h2>✅ 所有测试通过！字段名称均为唯一。</h2></div>'
                );
            } else {
                document.body.insertAdjacentHTML('beforeend', 
                    '<div class="test-section"><h2 class="error">❌ 测试失败！发现重复的字段名称。</h2></div>'
                );
            }
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
