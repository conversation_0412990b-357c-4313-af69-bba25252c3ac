{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\content\\barrage\\index.vue", "mtime": 1753931003624}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0QmFycmFnZSwgZ2V0QmFycmFnZSwgZGVsQmFycmFnZSwgYXVkaXRCYXJyYWdlLCBiYXRjaEFwcHJvdmVCYXJyYWdlLCBiYXRjaFJlamVjdEJhcnJhZ2UsIGV4cG9ydEJhcnJhZ2UsIGdldEJhcnJhZ2VDb25maWcgfSBmcm9tICJAL2FwaS9taW5pYXBwL2JhcnJhZ2UiOw0KaW1wb3J0IHsgbGlzdENvbmZpZywgdXBkYXRlQ29uZmlnIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2NvbmZpZyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk1pbmlCYXJyYWdlIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlvLnluZXooajmoLzmlbDmja4NCiAgICAgIGJhcnJhZ2VMaXN0OiBbXSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuivpuaDheW8ueWHuuWxgg0KICAgICAgZGV0YWlsT3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrlrqHmoLjlvLnlh7rlsYINCiAgICAgIGF1ZGl0T3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrmibnph4/mi5Lnu53lvLnlh7rlsYINCiAgICAgIGJhdGNoUmVqZWN0T3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrphY3nva7nrqHnkIblvLnlh7rlsYINCiAgICAgIGNvbmZpZ09wZW46IGZhbHNlLA0KICAgICAgLy8g6YWN572u5Yqg6L2954q25oCBDQogICAgICBjb25maWdMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOWuoeaguOagh+mimA0KICAgICAgYXVkaXRUaXRsZTogIiIsDQogICAgICAvLyDlvZPliY3lvLnluZUNCiAgICAgIGN1cnJlbnRCYXJyYWdlOiB7fSwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOW8ueW5leivpuaDhQ0KICAgICAgYmFycmFnZURldGFpbDoge30sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgY29udGVudDogbnVsbCwNCiAgICAgICAgdXNlck5pY2tuYW1lOiBudWxsLA0KICAgICAgICBhdWRpdFN0YXR1czogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOWuoeaguOihqOWNleWPguaVsA0KICAgICAgYXVkaXRGb3JtOiB7DQogICAgICAgIGJhcnJhZ2VJZDogbnVsbCwNCiAgICAgICAgYXVkaXRTdGF0dXM6IG51bGwsDQogICAgICAgIGF1ZGl0UmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOaJuemHj+aLkue7neihqOWNleWPguaVsA0KICAgICAgYmF0Y2hSZWplY3RGb3JtOiB7DQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0sDQogICAgICAvLyDphY3nva7ooajljZXlj4LmlbANCiAgICAgIGNvbmZpZ0Zvcm06IHsNCiAgICAgICAgcm93czogMiwNCiAgICAgICAgc3BlZWQ6IDEwLA0KICAgICAgICBpbnRlcnZhbDogNg0KICAgICAgfSwNCiAgICAgIC8vIOWuoeaguOihqOWNleagoemqjA0KICAgICAgYXVkaXRSdWxlczogew0KICAgICAgICAvLyDlrqHmoLjlpIfms6jmlLnkuLrpnZ7lv4XloasNCiAgICAgIH0sDQogICAgICAvLyDmibnph4/mi5Lnu53ooajljZXmoKHpqowNCiAgICAgIGJhdGNoUmVqZWN0UnVsZXM6IHsNCiAgICAgICAgcmVqZWN0UmVhc29uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaLkue7neWOn+WboOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICAvLyDphY3nva7ooajljZXmoKHpqowNCiAgICAgIGNvbmZpZ1J1bGVzOiB7DQogICAgICAgIHJvd3M6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5by55bmV6KGM5pWw5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDMsIG1lc3NhZ2U6ICLlvLnluZXooYzmlbDlv4XpobvlnKgxLTPkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzcGVlZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlvLnluZXmu5rliqjpgJ/luqbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtaW46IDEsIG1heDogNTAsIG1lc3NhZ2U6ICLlvLnluZXmu5rliqjpgJ/luqblv4XpobvlnKgxLTUw5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgaW50ZXJ2YWw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5by55bmV5Y+R6YCB6Ze06ZqU5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtYXg6IDYwLCBtZXNzYWdlOiAi5by55bmV5Y+R6YCB6Ze06ZqU5b+F6aG75ZyoMS02MOenkuS5i+mXtCIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouW8ueW5leWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKTsNCiAgICAgIGxpc3RCYXJyYWdlKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYmFycmFnZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5iYXJyYWdlSWQpOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLyoqIOivpuaDheaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIGdldEJhcnJhZ2Uocm93LmJhcnJhZ2VJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYmFycmFnZURldGFpbCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlrqHmoLjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBdWRpdChyb3csIGF1ZGl0U3RhdHVzKSB7DQogICAgICB0aGlzLmN1cnJlbnRCYXJyYWdlID0gcm93Ow0KICAgICAgdGhpcy5hdWRpdEZvcm0gPSB7DQogICAgICAgIGJhcnJhZ2VJZDogcm93LmJhcnJhZ2VJZCwNCiAgICAgICAgYXVkaXRTdGF0dXM6IGF1ZGl0U3RhdHVzLA0KICAgICAgICBhdWRpdFJlbWFyazogJycNCiAgICAgIH07DQogICAgICB0aGlzLmF1ZGl0VGl0bGUgPSBhdWRpdFN0YXR1cyA9PT0gJzEnID8gJ+WuoeaguOmAmui/hycgOiAn5a6h5qC45ouS57udJzsNCiAgICAgIHRoaXMuYXVkaXRPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTlrqHmoLggKi8NCiAgICBzdWJtaXRBdWRpdCgpIHsNCiAgICAgIC8vIOebtOaOpeaPkOS6pO+8jOS4jemcgOimgeihqOWNlemqjOivgQ0KICAgICAgYXVkaXRCYXJyYWdlKHRoaXMuYXVkaXRGb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5a6h5qC45oiQ5YqfIik7DQogICAgICAgIHRoaXMuYXVkaXRPcGVuID0gZmFsc2U7DQogICAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaJuemHj+WuoeaguOmAmui/hyAqLw0KICAgIGhhbmRsZUJhdGNoQXBwcm92ZSgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeWuoeaguOeahOW8ueW5lSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmibnph4/lrqHmoLjpgJrov4fpgInkuK3nmoTlvLnluZXvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgcmV0dXJuIGJhdGNoQXBwcm92ZUJhcnJhZ2UodGhpcy5pZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmibnph4/lrqHmoLjpgJrov4fmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDmibnph4/lrqHmoLjmi5Lnu50gKi8NCiAgICBoYW5kbGVCYXRjaFJlamVjdCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeaLkue7neeahOW8ueW5lSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmJhdGNoUmVqZWN0Rm9ybS5yZWplY3RSZWFzb24gPSAnJzsNCiAgICAgIHRoaXMuYmF0Y2hSZWplY3RPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmibnph4/mi5Lnu50gKi8NCiAgICBzdWJtaXRCYXRjaFJlamVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuYmF0Y2hSZWplY3RGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgICAgYmFycmFnZUlkczogdGhpcy5pZHMsDQogICAgICAgICAgICByZWplY3RSZWFzb246IHRoaXMuYmF0Y2hSZWplY3RGb3JtLnJlamVjdFJlYXNvbg0KICAgICAgICAgIH07DQogICAgICAgICAgYmF0Y2hSZWplY3RCYXJyYWdlKHBhcmFtcykudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmibnph4/mi5Lnu53miJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuYmF0Y2hSZWplY3RPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgYmFycmFnZUlkcyA9IHJvdy5iYXJyYWdlSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlvLnluZXnvJblj7fkuLoiJyArIGJhcnJhZ2VJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxCYXJyYWdlKGJhcnJhZ2VJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBsZXQgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5by55bmV5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgIHJldHVybiBleHBvcnRCYXJyYWdlKHBhcmFtcyk7DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kZG93bmxvYWQuZXhjZWwocmVzcG9uc2UsICflvLnluZXmlbDmja4ueGxzeCcpOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrqHmoLjnirbmgIHnsbvlnosgKi8NCiAgICBnZXRBdWRpdFN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICcwJzogJ3dhcm5pbmcnLA0KICAgICAgICAnMSc6ICdzdWNjZXNzJywNCiAgICAgICAgJzInOiAnZGFuZ2VyJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAnaW5mbyc7DQogICAgfSwNCiAgICAvKiog6I635Y+W5a6h5qC454q25oCB5paH5pysICovDQogICAgZ2V0QXVkaXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAnMCc6ICflvoXlrqHmoLgnLA0KICAgICAgICAnMSc6ICflrqHmoLjpgJrov4cnLA0KICAgICAgICAnMic6ICflrqHmoLjmi5Lnu50nDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQogICAgLyoqIOmFjee9rueuoeeQhuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUNvbmZpZ01hbmFnZSgpIHsNCiAgICAgIHRoaXMubG9hZEJhcnJhZ2VDb25maWcoKTsNCiAgICAgIHRoaXMuY29uZmlnT3BlbiA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5Yqg6L295by55bmV6YWN572uICovDQogICAgbG9hZEJhcnJhZ2VDb25maWcoKSB7DQogICAgICBnZXRCYXJyYWdlQ29uZmlnKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnN0IGNvbmZpZyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuY29uZmlnRm9ybSA9IHsNCiAgICAgICAgICByb3dzOiBjb25maWcucm93cyB8fCAyLA0KICAgICAgICAgIHNwZWVkOiBjb25maWcuc3BlZWQgfHwgMTAsDQogICAgICAgICAgaW50ZXJ2YWw6IGNvbmZpZy5pbnRlcnZhbCB8fCA2DQogICAgICAgIH07DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bphY3nva7lpLHotKUiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOmFjee9riAqLw0KICAgIHN1Ym1pdENvbmZpZygpIHsNCiAgICAgIHRoaXMuJHJlZnMuY29uZmlnRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuY29uZmlnTG9hZGluZyA9IHRydWU7DQoNCiAgICAgICAgICAvLyDlh4blpIfopoHmm7TmlrDnmoTphY3nva7plK7lkI3lkozlgLwNCiAgICAgICAgICBjb25zdCBjb25maWdLZXlzID0gWw0KICAgICAgICAgICAgJ2Rhbm1ha3UuZGlzcGxheS5yb3dzJywNCiAgICAgICAgICAgICdkYW5tYWt1LnNjcm9sbC5zcGVlZCcsDQogICAgICAgICAgICAnZGFubWFrdS5zZW5kLmludGVydmFsJw0KICAgICAgICAgIF07DQoNCiAgICAgICAgICBjb25zdCBjb25maWdWYWx1ZXMgPSBbDQogICAgICAgICAgICB0aGlzLmNvbmZpZ0Zvcm0ucm93cy50b1N0cmluZygpLA0KICAgICAgICAgICAgdGhpcy5jb25maWdGb3JtLnNwZWVkLnRvU3RyaW5nKCksDQogICAgICAgICAgICB0aGlzLmNvbmZpZ0Zvcm0uaW50ZXJ2YWwudG9TdHJpbmcoKQ0KICAgICAgICAgIF07DQoNCiAgICAgICAgICAvLyDlhYjojrflj5bmiYDmnInnm7jlhbPphY3nva4NCiAgICAgICAgICB0aGlzLmdldEV4aXN0aW5nQ29uZmlncyhjb25maWdLZXlzLCBjb25maWdWYWx1ZXMpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bnjrDmnInphY3nva4gKi8NCiAgICBnZXRFeGlzdGluZ0NvbmZpZ3MoY29uZmlnS2V5cywgY29uZmlnVmFsdWVzKSB7DQogICAgICAvLyDmn6Xor6Lns7vnu5/phY3nva7vvIzojrflj5bov5nkuInkuKrphY3nva7nmoTor6bnu4bkv6Hmga8NCiAgICAgIGxpc3RDb25maWcoew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAwLA0KICAgICAgICBjb25maWdLZXk6ICcnICAvLyDojrflj5bmiYDmnInphY3nva7vvIznhLblkI7ov4fmu6QNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zdCBhbGxDb25maWdzID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgY29uc3QgdGFyZ2V0Q29uZmlncyA9IGFsbENvbmZpZ3MuZmlsdGVyKGNvbmZpZyA9Pg0KICAgICAgICAgIGNvbmZpZ0tleXMuaW5jbHVkZXMoY29uZmlnLmNvbmZpZ0tleSkNCiAgICAgICAgKTsNCg0KICAgICAgICAvLyDmm7TmlrDnjrDmnInphY3nva4NCiAgICAgICAgdGhpcy51cGRhdGVFeGlzdGluZ0NvbmZpZ3ModGFyZ2V0Q29uZmlncywgY29uZmlnS2V5cywgY29uZmlnVmFsdWVzKTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy5jb25maWdMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumFjee9ruWIl+ihqOWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bphY3nva7kv6Hmga/lpLHotKUiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOabtOaWsOeOsOaciemFjee9riAqLw0KICAgIHVwZGF0ZUV4aXN0aW5nQ29uZmlncyhleGlzdGluZ0NvbmZpZ3MsIGNvbmZpZ0tleXMsIGNvbmZpZ1ZhbHVlcykgew0KICAgICAgbGV0IHVwZGF0ZUNvdW50ID0gMDsNCiAgICAgIGNvbnN0IHRvdGFsQ291bnQgPSBjb25maWdLZXlzLmxlbmd0aDsNCg0KICAgICAgY29uZmlnS2V5cy5mb3JFYWNoKChrZXksIGluZGV4KSA9PiB7DQogICAgICAgIGNvbnN0IGV4aXN0aW5nQ29uZmlnID0gZXhpc3RpbmdDb25maWdzLmZpbmQoY29uZmlnID0+IGNvbmZpZy5jb25maWdLZXkgPT09IGtleSk7DQoNCiAgICAgICAgaWYgKGV4aXN0aW5nQ29uZmlnKSB7DQogICAgICAgICAgLy8g5pu05paw546w5pyJ6YWN572uDQogICAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgICAgIC4uLmV4aXN0aW5nQ29uZmlnLA0KICAgICAgICAgICAgY29uZmlnVmFsdWU6IGNvbmZpZ1ZhbHVlc1tpbmRleF0NCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgdXBkYXRlQ29uZmlnKHVwZGF0ZURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdXBkYXRlQ291bnQrKzsNCiAgICAgICAgICAgIGlmICh1cGRhdGVDb3VudCA9PT0gdG90YWxDb3VudCkgew0KICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6YWN572u5pu05paw5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuY29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIHRoaXMuY29uZmlnTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgY29uc29sZS5lcnJvcihg5pu05paw6YWN572uICR7a2V5fSDlpLHotKU6YCwgZXJyb3IpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOabtOaWsOmFjee9ruWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nfWApOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOmFjee9ruS4jeWtmOWcqO+8jOiusOW9lemUmeivrw0KICAgICAgICAgIGNvbnNvbGUud2Fybihg6YWN572uICR7a2V5fSDkuI3lrZjlnKhgKTsNCiAgICAgICAgICB1cGRhdGVDb3VudCsrOw0KICAgICAgICAgIGlmICh1cGRhdGVDb3VudCA9PT0gdG90YWxDb3VudCkgew0KICAgICAgICAgICAgdGhpcy5jb25maWdMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dXYXJuaW5nKCLpg6jliIbphY3nva7kuI3lrZjlnKjvvIzor7fogZTns7vnrqHnkIblkZgiKTsNCiAgICAgICAgICAgIHRoaXMuY29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwUA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/content/barrage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"弹幕内容\" prop=\"content\">\r\n        <el-input\r\n          v-model=\"queryParams.content\"\r\n          placeholder=\"请输入弹幕内容\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"发布者\" prop=\"userNickname\">\r\n        <el-input\r\n          v-model=\"queryParams.userNickname\"\r\n          placeholder=\"请输入发布者昵称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"审核状态\" prop=\"auditStatus\">\r\n        <el-select v-model=\"queryParams.auditStatus\" placeholder=\"请选择审核状态\" clearable>\r\n          <el-option label=\"待审核\" value=\"0\" />\r\n          <el-option label=\"审核通过\" value=\"1\" />\r\n          <el-option label=\"审核拒绝\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-check\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleBatchApprove\"\r\n          v-hasPermi=\"['miniapp:barrage:audit']\"\r\n        >批量通过</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleBatchReject\"\r\n          v-hasPermi=\"['miniapp:barrage:audit']\"\r\n        >批量拒绝</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:barrage:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:barrage:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-setting\"\r\n          size=\"mini\"\r\n          @click=\"handleConfigManage\"\r\n          v-hasPermi=\"['system:config:edit']\"\r\n        >弹幕配置</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"barrageList\" @selection-change=\"handleSelectionChange\" row-key=\"barrageId\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"发布者\" align=\"center\" width=\"140\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"user-info\">\r\n            <el-avatar\r\n              :size=\"40\"\r\n              :src=\"scope.row.userAvatarUrl\"\r\n              class=\"user-avatar\"\r\n              fit=\"cover\"\r\n            >\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </el-avatar>\r\n            <div class=\"user-details\">\r\n              <div class=\"user-nickname\">{{ scope.row.userNickName || '未设置昵称' }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"弹幕内容\" align=\"left\" prop=\"content\" min-width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"barrage-content-cell\">\r\n            {{ scope.row.content }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核状态\" align=\"center\" prop=\"auditStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getAuditStatusType(scope.row.auditStatus)\"\r\n            size=\"small\"\r\n          >\r\n            {{ getAuditStatusText(scope.row.auditStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"发布时间\" align=\"center\" prop=\"createTime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"time-info\">\r\n            <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</div>\r\n            <div class=\"time-detail\">{{ parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审核时间\" align=\"center\" prop=\"auditTime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.auditTime\" class=\"time-info\">\r\n            <div>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</div>\r\n            <div class=\"time-detail\">{{ parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}</div>\r\n          </div>\r\n          <span v-else class=\"no-data\">-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"220\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.auditStatus === '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleAudit(scope.row, '1')\"\r\n            v-hasPermi=\"['miniapp:barrage:audit']\"\r\n            style=\"color: #67C23A;\"\r\n          >通过</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.auditStatus === '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"handleAudit(scope.row, '2')\"\r\n            v-hasPermi=\"['miniapp:barrage:audit']\"\r\n            style=\"color: #F56C6C;\"\r\n          >拒绝</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            v-hasPermi=\"['miniapp:barrage:query']\"\r\n          >详情</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:barrage:remove']\"\r\n            style=\"color: #F56C6C;\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 弹幕详情对话框 -->\r\n    <el-dialog title=\"弹幕详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"发布者信息\" :span=\"2\">\r\n          <div class=\"user-info-detail\">\r\n            <el-avatar\r\n              :size=\"50\"\r\n              :src=\"barrageDetail.userAvatarUrl\"\r\n              class=\"user-avatar-detail\"\r\n              fit=\"cover\"\r\n            >\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </el-avatar>\r\n            <div class=\"user-details-detail\">\r\n              <div class=\"user-nickname-detail\">{{ barrageDetail.userNickName || '未设置昵称' }}</div>\r\n            </div>\r\n          </div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"弹幕内容\" :span=\"2\">\r\n          <div class=\"barrage-content\">{{ barrageDetail.content }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核状态\">\r\n          <el-tag :type=\"getAuditStatusType(barrageDetail.auditStatus)\">\r\n            {{ getAuditStatusText(barrageDetail.auditStatus) }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核时间\">\r\n          {{ barrageDetail.auditTime ? parseTime(barrageDetail.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核备注\" :span=\"2\" v-if=\"barrageDetail.auditRemark\">\r\n          {{ barrageDetail.auditRemark }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"发布时间\" :span=\"2\">\r\n          {{ parseTime(barrageDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 审核操作 -->\r\n      <div v-if=\"barrageDetail.auditStatus === '0'\" style=\"margin-top: 20px; text-align: center;\">\r\n        <el-button type=\"success\" @click=\"handleAudit(barrageDetail, '1')\">审核通过</el-button>\r\n        <el-button type=\"danger\" @click=\"handleAudit(barrageDetail, '2')\">审核拒绝</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog :title=\"auditTitle\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"100px\">\r\n        <el-form-item label=\"审核结果\">\r\n          <el-tag :type=\"auditForm.auditStatus === '1' ? 'success' : 'danger'\">\r\n            {{ auditForm.auditStatus === '1' ? '审核通过' : '审核拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"审核备注\">\r\n          <el-input\r\n            v-model=\"auditForm.auditRemark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入审核备注（可选）\"\r\n            :rows=\"4\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\r\n        <el-button @click=\"auditOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量拒绝对话框 -->\r\n    <el-dialog title=\"批量拒绝\" :visible.sync=\"batchRejectOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"batchRejectForm\" :model=\"batchRejectForm\" :rules=\"batchRejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"拒绝原因\" prop=\"rejectReason\">\r\n          <el-input\r\n            v-model=\"batchRejectForm.rejectReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            :rows=\"4\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitBatchReject\">确 定</el-button>\r\n        <el-button @click=\"batchRejectOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 弹幕配置管理对话框 -->\r\n    <el-dialog title=\"弹幕配置管理\" :visible.sync=\"configOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"configForm\" :model=\"configForm\" :rules=\"configRules\" label-width=\"120px\">\r\n        <el-form-item label=\"弹幕行数\" prop=\"rows\">\r\n          <el-input-number\r\n            v-model=\"configForm.rows\"\r\n            :min=\"1\"\r\n            :max=\"2\"\r\n            controls-position=\"right\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span style=\"margin-left: 10px; color: #909399; font-size: 12px;\">最大值为2行</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"弹幕滚动速度\" prop=\"speed\">\r\n          <el-input-number\r\n            v-model=\"configForm.speed\"\r\n            :min=\"1\"\r\n            :max=\"50\"\r\n            controls-position=\"right\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span style=\"margin-left: 10px; color: #909399; font-size: 12px;\">数值越大滚动越快</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"弹幕发送间隔\" prop=\"interval\">\r\n          <el-input-number\r\n            v-model=\"configForm.interval\"\r\n            :min=\"1\"\r\n            :max=\"60\"\r\n            controls-position=\"right\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span style=\"margin-left: 10px; color: #909399; font-size: 12px;\">单位：秒</span>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitConfig\" :loading=\"configLoading\">确 定</el-button>\r\n        <el-button @click=\"configOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listBarrage, getBarrage, delBarrage, auditBarrage, batchApproveBarrage, batchRejectBarrage, exportBarrage, getBarrageConfig } from \"@/api/miniapp/barrage\";\r\nimport { listConfig, updateConfig } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  name: \"MiniBarrage\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 弹幕表格数据\r\n      barrageList: [],\r\n      // 是否显示详情弹出层\r\n      detailOpen: false,\r\n      // 是否显示审核弹出层\r\n      auditOpen: false,\r\n      // 是否显示批量拒绝弹出层\r\n      batchRejectOpen: false,\r\n      // 是否显示配置管理弹出层\r\n      configOpen: false,\r\n      // 配置加载状态\r\n      configLoading: false,\r\n      // 审核标题\r\n      auditTitle: \"\",\r\n      // 当前弹幕\r\n      currentBarrage: {},\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 弹幕详情\r\n      barrageDetail: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        content: null,\r\n        userNickname: null,\r\n        auditStatus: null\r\n      },\r\n      // 审核表单参数\r\n      auditForm: {\r\n        barrageId: null,\r\n        auditStatus: null,\r\n        auditRemark: ''\r\n      },\r\n      // 批量拒绝表单参数\r\n      batchRejectForm: {\r\n        rejectReason: ''\r\n      },\r\n      // 配置表单参数\r\n      configForm: {\r\n        rows: 2,\r\n        speed: 10,\r\n        interval: 6\r\n      },\r\n      // 审核表单校验\r\n      auditRules: {\r\n        // 审核备注改为非必填\r\n      },\r\n      // 批量拒绝表单校验\r\n      batchRejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"拒绝原因不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 配置表单校验\r\n      configRules: {\r\n        rows: [\r\n          { required: true, message: \"弹幕行数不能为空\", trigger: \"blur\" },\r\n          { type: 'number', min: 1, max: 3, message: \"弹幕行数必须在1-3之间\", trigger: \"blur\" }\r\n        ],\r\n        speed: [\r\n          { required: true, message: \"弹幕滚动速度不能为空\", trigger: \"blur\" },\r\n          { type: 'number', min: 1, max: 50, message: \"弹幕滚动速度必须在1-50之间\", trigger: \"blur\" }\r\n        ],\r\n        interval: [\r\n          { required: true, message: \"弹幕发送间隔不能为空\", trigger: \"blur\" },\r\n          { type: 'number', min: 1, max: 60, message: \"弹幕发送间隔必须在1-60秒之间\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询弹幕列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      let params = this.addDateRange(this.queryParams, this.dateRange);\r\n      listBarrage(params).then(response => {\r\n        this.barrageList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.barrageId);\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      getBarrage(row.barrageId).then(response => {\r\n        this.barrageDetail = response.data;\r\n        this.detailOpen = true;\r\n      });\r\n    },\r\n    /** 审核按钮操作 */\r\n    handleAudit(row, auditStatus) {\r\n      this.currentBarrage = row;\r\n      this.auditForm = {\r\n        barrageId: row.barrageId,\r\n        auditStatus: auditStatus,\r\n        auditRemark: ''\r\n      };\r\n      this.auditTitle = auditStatus === '1' ? '审核通过' : '审核拒绝';\r\n      this.auditOpen = true;\r\n    },\r\n    /** 提交审核 */\r\n    submitAudit() {\r\n      // 直接提交，不需要表单验证\r\n      auditBarrage(this.auditForm).then(() => {\r\n        this.$modal.msgSuccess(\"审核成功\");\r\n        this.auditOpen = false;\r\n        this.detailOpen = false;\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 批量审核通过 */\r\n    handleBatchApprove() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要审核的弹幕\");\r\n        return;\r\n      }\r\n      this.$modal.confirm('是否确认批量审核通过选中的弹幕？').then(() => {\r\n        return batchApproveBarrage(this.ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"批量审核通过成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量审核拒绝 */\r\n    handleBatchReject() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要拒绝的弹幕\");\r\n        return;\r\n      }\r\n      this.batchRejectForm.rejectReason = '';\r\n      this.batchRejectOpen = true;\r\n    },\r\n    /** 提交批量拒绝 */\r\n    submitBatchReject() {\r\n      this.$refs.batchRejectForm.validate(valid => {\r\n        if (valid) {\r\n          const params = {\r\n            barrageIds: this.ids,\r\n            rejectReason: this.batchRejectForm.rejectReason\r\n          };\r\n          batchRejectBarrage(params).then(() => {\r\n            this.$modal.msgSuccess(\"批量拒绝成功\");\r\n            this.batchRejectOpen = false;\r\n            this.getList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const barrageIds = row.barrageId || this.ids;\r\n      this.$modal.confirm('是否确认删除弹幕编号为\"' + barrageIds + '\"的数据项？').then(function() {\r\n        return delBarrage(barrageIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      let params = this.addDateRange(this.queryParams, this.dateRange);\r\n      this.$modal.confirm('是否确认导出所有弹幕数据项？').then(() => {\r\n        this.loading = true;\r\n        return exportBarrage(params);\r\n      }).then(response => {\r\n        this.$download.excel(response, '弹幕数据.xlsx');\r\n        this.loading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 获取审核状态类型 */\r\n    getAuditStatusType(status) {\r\n      const statusMap = {\r\n        '0': 'warning',\r\n        '1': 'success',\r\n        '2': 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n    /** 获取审核状态文本 */\r\n    getAuditStatusText(status) {\r\n      const statusMap = {\r\n        '0': '待审核',\r\n        '1': '审核通过',\r\n        '2': '审核拒绝'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n    /** 配置管理按钮操作 */\r\n    handleConfigManage() {\r\n      this.loadBarrageConfig();\r\n      this.configOpen = true;\r\n    },\r\n    /** 加载弹幕配置 */\r\n    loadBarrageConfig() {\r\n      getBarrageConfig().then(response => {\r\n        const config = response.data;\r\n        this.configForm = {\r\n          rows: config.rows || 2,\r\n          speed: config.speed || 10,\r\n          interval: config.interval || 6\r\n        };\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"获取配置失败\");\r\n      });\r\n    },\r\n    /** 提交配置 */\r\n    submitConfig() {\r\n      this.$refs.configForm.validate(valid => {\r\n        if (valid) {\r\n          this.configLoading = true;\r\n\r\n          // 准备要更新的配置键名和值\r\n          const configKeys = [\r\n            'danmaku.display.rows',\r\n            'danmaku.scroll.speed',\r\n            'danmaku.send.interval'\r\n          ];\r\n\r\n          const configValues = [\r\n            this.configForm.rows.toString(),\r\n            this.configForm.speed.toString(),\r\n            this.configForm.interval.toString()\r\n          ];\r\n\r\n          // 先获取所有相关配置\r\n          this.getExistingConfigs(configKeys, configValues);\r\n        }\r\n      });\r\n    },\r\n    /** 获取现有配置 */\r\n    getExistingConfigs(configKeys, configValues) {\r\n      // 查询系统配置，获取这三个配置的详细信息\r\n      listConfig({\r\n        pageNum: 1,\r\n        pageSize: 100,\r\n        configKey: ''  // 获取所有配置，然后过滤\r\n      }).then(response => {\r\n        const allConfigs = response.rows || [];\r\n        const targetConfigs = allConfigs.filter(config =>\r\n          configKeys.includes(config.configKey)\r\n        );\r\n\r\n        // 更新现有配置\r\n        this.updateExistingConfigs(targetConfigs, configKeys, configValues);\r\n      }).catch(error => {\r\n        this.configLoading = false;\r\n        console.error('获取配置列表失败:', error);\r\n        this.$modal.msgError(\"获取配置信息失败\");\r\n      });\r\n    },\r\n    /** 更新现有配置 */\r\n    updateExistingConfigs(existingConfigs, configKeys, configValues) {\r\n      let updateCount = 0;\r\n      const totalCount = configKeys.length;\r\n\r\n      configKeys.forEach((key, index) => {\r\n        const existingConfig = existingConfigs.find(config => config.configKey === key);\r\n\r\n        if (existingConfig) {\r\n          // 更新现有配置\r\n          const updateData = {\r\n            ...existingConfig,\r\n            configValue: configValues[index]\r\n          };\r\n\r\n          updateConfig(updateData).then(() => {\r\n            updateCount++;\r\n            if (updateCount === totalCount) {\r\n              this.configLoading = false;\r\n              this.$modal.msgSuccess(\"配置更新成功\");\r\n              this.configOpen = false;\r\n            }\r\n          }).catch(error => {\r\n            this.configLoading = false;\r\n            console.error(`更新配置 ${key} 失败:`, error);\r\n            this.$modal.msgError(`更新配置失败: ${error.message || '未知错误'}`);\r\n          });\r\n        } else {\r\n          // 配置不存在，记录错误\r\n          console.warn(`配置 ${key} 不存在`);\r\n          updateCount++;\r\n          if (updateCount === totalCount) {\r\n            this.configLoading = false;\r\n            this.$modal.msgWarning(\"部分配置不存在，请联系管理员\");\r\n            this.configOpen = false;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.user-avatar {\r\n  flex-shrink: 0;\r\n  border: 2px solid #f0f0f0;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.user-nickname {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n  text-align: center;\r\n}\r\n\r\n.barrage-content {\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.barrage-content-cell {\r\n  max-width: 300px;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n  line-height: 1.5;\r\n  padding: 8px 0;\r\n}\r\n\r\n.time-info {\r\n  text-align: center;\r\n}\r\n\r\n.time-detail {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 2px;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.el-table .el-table__cell .el-button + .el-button {\r\n  margin-left: 5px;\r\n}\r\n\r\n/* 防止操作按钮换行 */\r\n::v-deep .el-table .small-padding .cell {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n}\r\n\r\n::v-deep .el-table .fixed-width .cell {\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 表格行高度调整 */\r\n::v-deep .el-table .el-table__row {\r\n  height: auto;\r\n}\r\n\r\n::v-deep .el-table .el-table__cell {\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 详情对话框样式 */\r\n.user-info-detail {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.user-avatar-detail {\r\n  flex-shrink: 0;\r\n  border: 2px solid #f0f0f0;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-details-detail {\r\n  flex: 1;\r\n}\r\n\r\n.user-nickname-detail {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1200px) {\r\n  .el-table .el-table__cell .el-button {\r\n    margin: 2px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n</style>\r\n"]}]}