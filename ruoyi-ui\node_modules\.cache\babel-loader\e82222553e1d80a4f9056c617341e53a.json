{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753946374542}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, null]}