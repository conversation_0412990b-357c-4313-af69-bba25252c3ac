{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1753694616742}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UHJvamVjdCwgZGVsUHJvamVjdCwgYXVkaXRQcm9qZWN0LCB1cGRhdGVTcG9uc29ySW1hZ2UsIGdldFNwb25zb3JJbWFnZSB9IGZyb20gIkAvYXBpL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0IjsNCmltcG9ydCBJbWFnZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJQcm9qZWN0IiwNCiAgY29tcG9uZW50czogew0KICAgIEltYWdlVXBsb2FkDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6aG555uu5oql5ZCN6KGo5qC85pWw5o2uDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICAvLyDmmK/lkKbmmL7npLrotZ7liqnllYblm77niYflvLnlh7rlsYINCiAgICAgIHNwb25zb3JPcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuWuoeaguOW8ueWHuuWxgg0KICAgICAgYXVkaXRPcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuafpeeci+ivpuaDheW8ueWHuuWxgg0KICAgICAgdmlld09wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBjaXR5OiBudWxsLA0KICAgICAgICBpbmR1c3RyeTogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6LWe5Yqp5ZWG6KGo5Y2V5Y+C5pWwDQogICAgICBzcG9uc29yRm9ybTogew0KICAgICAgICBzcG9uc29yVW5pdDogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOWuoeaguOihqOWNleWPguaVsA0KICAgICAgYXVkaXRGb3JtOiB7fSwNCiAgICAgIC8vIOafpeeci+ivpuaDheihqOWNleWPguaVsA0KICAgICAgdmlld0Zvcm06IHt9LA0KICAgICAgLy8g5a6h5qC46KGo5Y2V5qCh6aqMDQogICAgICBhdWRpdFJ1bGVzOiB7DQogICAgICAgIHN0YXR1czogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrqHmoLjnu5PmnpzkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoumhueebruaKpeWQjeWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFByb2plY3QodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucHJvamVjdExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQoNCiAgICAvKiog6LWe5Yqp5ZWG5Zu+54mH5LiK5Lyg5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlU3BvbnNvclVwbG9hZCgpIHsNCiAgICAgIGdldFNwb25zb3JJbWFnZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnNwb25zb3JGb3JtLnNwb25zb3JVbml0ID0gcmVzcG9uc2UuZGF0YSB8fCAnJzsNCiAgICAgICAgdGhpcy5zcG9uc29yT3BlbiA9IHRydWU7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuc3BvbnNvckZvcm0uc3BvbnNvclVuaXQgPSAnJzsNCiAgICAgICAgdGhpcy5zcG9uc29yT3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlj5bmtojotZ7liqnllYblm77niYfkuIrkvKAgKi8NCiAgICBjYW5jZWxTcG9uc29yKCkgew0KICAgICAgdGhpcy5zcG9uc29yT3BlbiA9IGZhbHNlOw0KICAgICAgLy8g5LiN6KaB5riF56m65pWw5o2u77yM5L+d5oyB5Y6f5pyJ5pWw5o2uDQogICAgICAvLyB0aGlzLnNwb25zb3JGb3JtLnNwb25zb3JVbml0ID0gbnVsbDsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTotZ7liqnllYblm77niYcgKi8NCiAgICBzdWJtaXRTcG9uc29yRm9ybSgpIHsNCiAgICAgIHVwZGF0ZVNwb25zb3JJbWFnZSh0aGlzLnNwb25zb3JGb3JtLnNwb25zb3JVbml0KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6LWe5Yqp5ZWG5Zu+54mH5pu05paw5oiQ5YqfIik7DQogICAgICAgIHRoaXMuc3BvbnNvck9wZW4gPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuabtOaWsOi1nuWKqeWVhuWbvueJh+Wksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5p+l55yL5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVmlldyhyb3cpIHsNCiAgICAgIHRoaXMudmlld0Zvcm0gPSByb3c7DQogICAgICB0aGlzLnZpZXdPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDlrqHmoLjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBdWRpdChyb3cpIHsNCiAgICAgIHRoaXMuYXVkaXRGb3JtID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBzdGF0dXM6IDIsDQogICAgICAgIGF1ZGl0UmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5hdWRpdE9wZW4gPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvKiog5a6h5qC45o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0QXVkaXQoKSB7DQogICAgICB0aGlzLiRyZWZzWyJhdWRpdEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGF1ZGl0UHJvamVjdCh0aGlzLmF1ZGl0Rm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuYXVkaXRPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5a6h5qC4ICovDQogICAgY2FuY2VsQXVkaXQoKSB7DQogICAgICB0aGlzLmF1ZGl0T3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5hdWRpdEZvcm0gPSB7fTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpobnnm67miqXlkI3nvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFByb2plY3QoaWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC9oYWl0YW5nL3Byb2plY3QvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcHJvamVjdF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDojrflj5bnirbmgIHmlofmnKwgKi8NCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAxOiByZXR1cm4gJ+W+heWuoeaguCc7DQogICAgICAgIGNhc2UgMjogcmV0dXJuICflrqHmoLjpgJrov4cnOw0KICAgICAgICBjYXNlIDM6IHJldHVybiAn5a6h5qC45LiN6YCa6L+HJzsNCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICfmnKrnn6UnOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOiOt+WPlueKtuaAgeexu+WeiyAqLw0KICAgIGdldFN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBzd2l0Y2ggKHN0YXR1cykgew0KICAgICAgICBjYXNlIDE6IHJldHVybiAnd2FybmluZyc7DQogICAgICAgIGNhc2UgMjogcmV0dXJuICdzdWNjZXNzJzsNCiAgICAgICAgY2FzZSAzOiByZXR1cm4gJ2Rhbmdlcic7DQogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnaW5mbyc7DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyPA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/project", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"城市\" prop=\"city\">\r\n        <el-input\r\n          v-model=\"queryParams.city\"\r\n          placeholder=\"请输入城市\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"行业\" prop=\"industry\">\r\n        <el-input\r\n          v-model=\"queryParams.industry\"\r\n          placeholder=\"请输入行业\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option label=\"待审核\" value=\"1\" />\r\n          <el-option label=\"审核通过\" value=\"2\" />\r\n          <el-option label=\"审核不通过\" value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-picture\"\r\n          size=\"mini\"\r\n          @click=\"handleSponsorUpload\"\r\n          v-hasPermi=\"['miniapp:haitang:project:edit']\"\r\n        >赞助商图片</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:haitang:project:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:haitang:project:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"projectList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" :show-overflow-tooltip=\"true\"/>\r\n      <el-table-column label=\"团队规模\" align=\"center\" prop=\"teamSize\" />\r\n      <el-table-column label=\"城市\" align=\"center\" prop=\"city\" />\r\n      <el-table-column label=\"赛区\" align=\"center\" prop=\"competitionArea\" />\r\n      <el-table-column label=\"行业\" align=\"center\" prop=\"industry\" />\r\n      <el-table-column label=\"天大校友\" align=\"center\" prop=\"isTjuAlumni\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.isTjuAlumni ? 'success' : 'info'\">\r\n            {{ scope.row.isTjuAlumni ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"有公司\" align=\"center\" prop=\"hasCompany\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.hasCompany ? 'success' : 'info'\">\r\n            {{ scope.row.hasCompany ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"公司名称\" align=\"center\" prop=\"companyName\" :show-overflow-tooltip=\"true\"/>\r\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contactName\" />\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusType(scope.row.status)\">\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:query']\"\r\n          >查看</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleAudit(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:audit']\"\r\n            v-if=\"scope.row.status === 1\"\r\n          >审核</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog title=\"审核项目报名\" :visible.sync=\"auditOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"80px\">\r\n        <el-form-item label=\"审核结果\" prop=\"status\">\r\n          <el-radio-group v-model=\"auditForm.status\">\r\n            <el-radio :label=\"2\">审核通过</el-radio>\r\n            <el-radio :label=\"3\">审核不通过</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"审核备注\" prop=\"auditRemark\">\r\n          <el-input v-model=\"auditForm.auditRemark\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入审核备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\r\n        <el-button @click=\"cancelAudit\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 赞助商图片上传对话框 -->\r\n    <el-dialog title=\"赞助商图片管理\" :visible.sync=\"sponsorOpen\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"sponsorForm\" :model=\"sponsorForm\" label-width=\"120px\">\r\n        <el-form-item label=\"当前赞助商图片\">\r\n          <div v-if=\"sponsorForm.sponsorUnit && sponsorForm.sponsorUnit.trim() !== ''\" style=\"padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-success\" style=\"color: #67c23a; margin-right: 5px;\"></i>\r\n            <span style=\"color: #409eff;\">已设置赞助商图片</span>\r\n          </div>\r\n          <div v-else style=\"padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-warning\" style=\"color: #e6a23c; margin-right: 5px;\"></i>\r\n            <span style=\"color: #e6a23c;\">暂未设置赞助商图片</span>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传新图片\">\r\n          <image-upload v-model=\"sponsorForm.sponsorUnit\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitSponsorForm\">确 定</el-button>\r\n        <el-button @click=\"cancelSponsor\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <!-- 查看详情对话框 -->\r\n    <el-dialog title=\"项目报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"项目名称\">{{ viewForm.projectName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"团队规模\">{{ viewForm.teamSize }}人</el-descriptions-item>\r\n        <el-descriptions-item label=\"城市\">{{ viewForm.city }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赛区\">{{ viewForm.competitionArea }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"行业\">{{ viewForm.industry }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"天大校友\">{{ viewForm.isTjuAlumni ? '是' : '否' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"项目描述\" :span=\"2\">{{ viewForm.projectDescription }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"是否有公司\">{{ viewForm.hasCompany ? '是' : '否' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"公司名称\" v-if=\"viewForm.hasCompany\">{{ viewForm.companyName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"去年营收\" v-if=\"viewForm.hasCompany\">{{ viewForm.lastYearRevenue }}万元</el-descriptions-item>\r\n        <el-descriptions-item label=\"项目估值\" v-if=\"viewForm.hasCompany\">{{ viewForm.projectValuation }}万元</el-descriptions-item>\r\n        <el-descriptions-item label=\"最新融资轮次\" v-if=\"viewForm.hasCompany\">{{ viewForm.latestFundingRound }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"投资机构\" v-if=\"viewForm.hasCompany\">{{ viewForm.investmentInstitution }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"公司logo\" v-if=\"viewForm.hasCompany && viewForm.companyLogo\">\r\n          <el-image\r\n            style=\"width: 100px; height: 60px\"\r\n            :src=\"viewForm.companyLogo\"\r\n            :preview-src-list=\"[viewForm.companyLogo]\"\r\n            fit=\"cover\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"项目BP\" v-if=\"viewForm.projectBp\">\r\n          <el-link type=\"primary\" :href=\"viewForm.projectBp\" target=\"_blank\" :underline=\"false\">\r\n            <i class=\"el-icon-download\"></i> 下载项目BP\r\n          </el-link>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"推荐人\">{{ viewForm.recommender }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赞助单位\" v-if=\"viewForm.sponsorUnit\">\r\n          <el-image\r\n            style=\"width: 120px; height: 60px\"\r\n            :src=\"viewForm.sponsorUnit\"\r\n            :preview-src-list=\"[viewForm.sponsorUnit]\"\r\n            fit=\"contain\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人姓名\">{{ viewForm.contactName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人电话\">{{ viewForm.contactPhone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人微信\">{{ viewForm.contactWechat }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人职位\">{{ viewForm.contactPosition }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报名时间\">{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"状态\">{{ getStatusText(viewForm.status) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核时间\" v-if=\"viewForm.auditTime\">{{ parseTime(viewForm.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核备注\" v-if=\"viewForm.auditRemark\">{{ viewForm.auditRemark }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listProject, delProject, auditProject, updateSponsorImage, getSponsorImage } from \"@/api/miniapp/haitang/project\";\r\nimport ImageUpload from \"@/components/ImageUpload\";\r\n\r\nexport default {\r\n  name: \"Project\",\r\n  components: {\r\n    ImageUpload\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目报名表格数据\r\n      projectList: [],\r\n      // 是否显示赞助商图片弹出层\r\n      sponsorOpen: false,\r\n      // 是否显示审核弹出层\r\n      auditOpen: false,\r\n      // 是否显示查看详情弹出层\r\n      viewOpen: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectName: null,\r\n        city: null,\r\n        industry: null,\r\n        status: null\r\n      },\r\n      // 赞助商表单参数\r\n      sponsorForm: {\r\n        sponsorUnit: null\r\n      },\r\n      // 审核表单参数\r\n      auditForm: {},\r\n      // 查看详情表单参数\r\n      viewForm: {},\r\n      // 审核表单校验\r\n      auditRules: {\r\n        status: [\r\n          { required: true, message: \"审核结果不能为空\", trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询项目报名列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProject(this.queryParams).then(response => {\r\n        this.projectList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 赞助商图片上传按钮操作 */\r\n    handleSponsorUpload() {\r\n      getSponsorImage().then(response => {\r\n        this.sponsorForm.sponsorUnit = response.data || '';\r\n        this.sponsorOpen = true;\r\n      }).catch(error => {\r\n        this.sponsorForm.sponsorUnit = '';\r\n        this.sponsorOpen = true;\r\n      });\r\n    },\r\n    /** 取消赞助商图片上传 */\r\n    cancelSponsor() {\r\n      this.sponsorOpen = false;\r\n      // 不要清空数据，保持原有数据\r\n      // this.sponsorForm.sponsorUnit = null;\r\n    },\r\n    /** 提交赞助商图片 */\r\n    submitSponsorForm() {\r\n      updateSponsorImage(this.sponsorForm.sponsorUnit).then(response => {\r\n        this.$modal.msgSuccess(\"赞助商图片更新成功\");\r\n        this.sponsorOpen = false;\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"更新赞助商图片失败\");\r\n      });\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.viewForm = row;\r\n      this.viewOpen = true;\r\n    },\r\n    /** 审核按钮操作 */\r\n    handleAudit(row) {\r\n      this.auditForm = {\r\n        id: row.id,\r\n        status: 2,\r\n        auditRemark: null\r\n      };\r\n      this.auditOpen = true;\r\n    },\r\n\r\n    /** 审核提交按钮 */\r\n    submitAudit() {\r\n      this.$refs[\"auditForm\"].validate(valid => {\r\n        if (valid) {\r\n          auditProject(this.auditForm).then(response => {\r\n            this.$modal.msgSuccess(\"审核成功\");\r\n            this.auditOpen = false;\r\n            this.getList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消审核 */\r\n    cancelAudit() {\r\n      this.auditOpen = false;\r\n      this.auditForm = {};\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除项目报名编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delProject(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/project/export', {\r\n        ...this.queryParams\r\n      }, `project_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 1: return '待审核';\r\n        case 2: return '审核通过';\r\n        case 3: return '审核不通过';\r\n        default: return '未知';\r\n      }\r\n    },\r\n    /** 获取状态类型 */\r\n    getStatusType(status) {\r\n      switch (status) {\r\n        case 1: return 'warning';\r\n        case 2: return 'success';\r\n        case 3: return 'danger';\r\n        default: return 'info';\r\n      }\r\n    }\r\n  }\r\n};\r\n</script> "]}]}