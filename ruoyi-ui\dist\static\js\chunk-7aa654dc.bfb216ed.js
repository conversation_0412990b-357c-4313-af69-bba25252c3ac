(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7aa654dc"],{9402:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"页面标题",prop:"pageTitle"}},[a("el-input",{attrs:{placeholder:"请输入页面标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.pageTitle,callback:function(t){e.$set(e.queryParams,"pageTitle",t)},expression:"queryParams.pageTitle"}})],1),a("el-form-item",{attrs:{label:"页面编码",prop:"pageCode"}},[a("el-input",{attrs:{placeholder:"请输入页面编码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.pageCode,callback:function(t){e.$set(e.queryParams,"pageCode",t)},expression:"queryParams.pageCode"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:content:page:add"],expression:"['miniapp:content:page:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:content:page:edit"],expression:"['miniapp:content:page:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:content:page:remove"],expression:"['miniapp:content:page:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:content:page:export"],expression:"['miniapp:content:page:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.pageList,"row-key":"contentId"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"内容ID",align:"center",prop:"contentId",width:"80"}}),a("el-table-column",{attrs:{label:"页面编码",align:"center",prop:"pageCode",width:"120"}}),a("el-table-column",{attrs:{label:"页面标题",align:"center",prop:"pageTitle",width:"150"}}),a("el-table-column",{attrs:{label:"页面内容",align:"center",prop:"pageContent","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"content-preview",domProps:{innerHTML:e._s(t.row.pageContent)}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:content:page:edit"],expression:"['miniapp:content:page:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:content:page:remove"],expression:"['miniapp:content:page:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"页面编码",prop:"pageCode"}},[a("el-input",{attrs:{placeholder:"请输入页面编码，如：join_us、about_us",disabled:null!=e.form.contentId},model:{value:e.form.pageCode,callback:function(t){e.$set(e.form,"pageCode",t)},expression:"form.pageCode"}}),a("div",{staticClass:"form-tip"},[a("p",[e._v("• 用于标识页面的唯一代码")]),a("p",[e._v("• 建议格式：join_us（加入我们）、about_us（关于我们）、user_agreement（用户协议）")]),null==e.form.contentId?a("p",[e._v("• 一旦设置后不可修改")]):a("p",{staticStyle:{color:"#f56c6c"}},[e._v("• 页面编码不可修改")])])],1),a("el-form-item",{attrs:{label:"页面标题",prop:"pageTitle"}},[a("el-input",{attrs:{placeholder:"请输入页面标题"},model:{value:e.form.pageTitle,callback:function(t){e.$set(e.form,"pageTitle",t)},expression:"form.pageTitle"}})],1),a("el-form-item",{attrs:{label:"页面内容",prop:"pageContent"}},[a("editor",{attrs:{"min-height":400,placeholder:"请输入页面内容"},model:{value:e.form.pageContent,callback:function(t){e.$set(e.form,"pageContent",t)},expression:"form.pageContent"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],r=a("5530"),o=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function l(e){return Object(o["a"])({url:"/miniapp/content/page/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/miniapp/content/page/"+e,method:"get"})}function p(e){return Object(o["a"])({url:"/miniapp/content/page",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/miniapp/content/page",method:"put",data:e})}function u(e){return Object(o["a"])({url:"/miniapp/content/page/"+e,method:"delete"})}var m={name:"MiniPageContent",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,pageList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,pageTitle:null,pageCode:null},form:{},rules:{pageCode:[{required:!0,message:"页面编码不能为空",trigger:"blur"},{pattern:/^[a-z_]{2,50}$/,message:"编码只能包含小写字母和下划线，长度2-50位",trigger:"blur"}],pageTitle:[{required:!0,message:"页面标题不能为空",trigger:"blur"}],pageContent:[{required:!0,message:"页面内容不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.pageList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={contentId:null,pageCode:null,pageTitle:null,pageContent:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.contentId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加页面内容"},handleUpdate:function(e){var t=this;this.reset();var a=e.contentId||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改页面内容"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.contentId?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):p(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.contentId||this.ids;this.$modal.confirm('是否确认删除页面内容编号为"'+a+'"的数据项？').then((function(){return u(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/content/page/export",Object(r["a"])({},this.queryParams),"page_content_".concat((new Date).getTime(),".xlsx"))}}},d=m,g=(a("d3e1"),a("2877")),h=Object(g["a"])(d,n,i,!1,null,"700d18f2",null);t["default"]=h.exports},"9f75":function(e,t,a){},d3e1:function(e,t,a){"use strict";a("9f75")}}]);