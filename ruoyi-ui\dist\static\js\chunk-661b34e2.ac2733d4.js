(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-661b34e2"],{6150:function(e,t,n){},6426:function(e,t,n){"use strict";n("6150")},b2bf:function(e,t,n){"use strict";n("fa1c")},deef:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"文章标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"请输入文章标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),n("el-form-item",{attrs:{label:"作者",prop:"author"}},[n("el-input",{attrs:{placeholder:"请输入作者",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.author,callback:function(t){e.$set(e.queryParams,"author",t)},expression:"queryParams.author"}})],1),n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:news:sync"],expression:"['miniapp:news:sync']"}],attrs:{type:"success",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.handleSync}},[e._v("同步微信")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:news:sync"],expression:"['miniapp:news:sync']"}],attrs:{type:"danger",plain:"",icon:"el-icon-refresh-right",size:"mini"},on:{click:e.handleResync}},[e._v("重新同步")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:news:export"],expression:"['miniapp:news:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.newsList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"文章标题",align:"center",prop:"title","min-width":"200","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{label:"作者",align:"center",prop:"author",width:"100"}}),n("el-table-column",{attrs:{label:"封面图",align:"center",prop:"thumbUrl",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.thumbUrl?n("img",{staticStyle:{width:"50px",height:"50px","object-fit":"cover","border-radius":"4px",cursor:"pointer"},attrs:{src:t.row.thumbUrl,alt:"封面图",referrerpolicy:"no-referrer"},on:{click:function(n){return e.previewImage(t.row.thumbUrl)}}}):n("span",[e._v("-")])]}}])}),n("el-table-column",{attrs:{label:"文章摘要",align:"center",prop:"digest","min-width":"200","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),n("el-table-column",{attrs:{label:"微信创建时间",align:"center",prop:"wechatCreateTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.wechatCreateTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),n("el-table-column",{attrs:{label:"同步时间",align:"center",prop:"syncTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.syncTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:news:query"],expression:"['miniapp:news:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(n){return e.handleView(t.row)}}},[e._v("查看")]),n("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-link"},on:{click:function(n){return e.handleOpenLink(t.row)}}},[e._v("打开链接")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"文章标题"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),n("el-form-item",{attrs:{label:"作者"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.author,callback:function(t){e.$set(e.form,"author",t)},expression:"form.author"}})],1),n("el-form-item",{attrs:{label:"封面图"}},[e.form.thumbUrl?n("div",{staticClass:"thumb-preview"},[n("img",{staticStyle:{"max-width":"200px","max-height":"150px","object-fit":"contain","border-radius":"4px",cursor:"pointer"},attrs:{src:e.form.thumbUrl,alt:"封面图",referrerpolicy:"no-referrer"},on:{click:function(t){return e.previewImage(e.form.thumbUrl)}}})]):n("div",{staticClass:"no-image"},[n("i",{staticClass:"el-icon-picture-outline"}),n("div",[e._v("暂无封面图")])])]),n("el-form-item",{attrs:{label:"文章摘要"}},[n("el-input",{attrs:{type:"textarea",rows:4,disabled:!0},model:{value:e.form.digest,callback:function(t){e.$set(e.form,"digest",t)},expression:"form.digest"}})],1),n("el-form-item",{attrs:{label:"微信文章ID"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.wechatArticleId,callback:function(t){e.$set(e.form,"wechatArticleId",t)},expression:"form.wechatArticleId"}})],1),n("el-form-item",{attrs:{label:"微信文章URL"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.wechatArticleUrl,callback:function(t){e.$set(e.form,"wechatArticleUrl",t)},expression:"form.wechatArticleUrl"}})],1),n("el-form-item",{attrs:{label:"状态"}},[n("el-select",{attrs:{disabled:!0},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"微信创建时间"}},[n("el-input",{attrs:{value:e.parseTime(e.form.wechatCreateTime,"{y}-{m}-{d} {h}:{i}:{s}"),disabled:!0}})],1),n("el-form-item",{attrs:{label:"同步时间"}},[n("el-input",{attrs:{value:e.parseTime(e.form.syncTime,"{y}-{m}-{d} {h}:{i}:{s}"),disabled:!0}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),n("el-dialog",{attrs:{title:"手动同步文章",visible:e.syncDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.syncDialogVisible=t}}},[n("el-form",{ref:"syncForm",attrs:{model:e.syncForm,rules:e.syncRules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"同步数量",prop:"count"}},[n("el-input-number",{attrs:{min:1,max:50},model:{value:e.syncForm.count,callback:function(t){e.$set(e.syncForm,"count",t)},expression:"syncForm.count"}})],1),n("el-form-item",{attrs:{label:"偏移量",prop:"offset"}},[n("el-input-number",{attrs:{min:0,max:1e3},model:{value:e.syncForm.offset,callback:function(t){e.$set(e.syncForm,"offset",t)},expression:"syncForm.offset"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.syncDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitSync}},[e._v("确 定")])],1)],1)],1)},i=[],r=n("5530"),l=(n("d81d"),n("d3b7"),n("0643"),n("a573"),n("b775"));function s(e){return Object(l["a"])({url:"/miniapp/haitang/news/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/miniapp/haitang/news/"+e,method:"get"})}function c(e,t){return Object(l["a"])({url:"/miniapp/haitang/news/sync/"+e+"/"+t,method:"post"})}function u(e){return Object(l["a"])({url:"/miniapp/haitang/news/resync/"+e,method:"post"})}var m={name:"News",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,newsList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,author:null,status:null,wechatCreateTime:null},form:{},rules:{},syncDialogVisible:!1,syncForm:{count:20,offset:0},syncRules:{count:[{required:!0,message:"同步数量不能为空",trigger:"blur"}],offset:[{required:!0,message:"偏移量不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.queryParams).then((function(t){e.newsList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,title:null,author:null,thumbUrl:null,digest:null,wechatArticleId:null,wechatArticleUrl:null,status:null,wechatCreateTime:null,syncTime:null,createdAt:null,updatedAt:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleView:function(e){var t=this;this.reset();var n=e.id||this.ids;o(n).then((function(e){t.form=e.data,t.open=!0,t.title="查看新闻详情"}))},handleOpenLink:function(e){e.wechatArticleUrl?window.open(e.wechatArticleUrl,"_blank"):this.$modal.msgError("该文章没有链接地址")},handleSync:function(){this.syncDialogVisible=!0},handleResync:function(){var e=this;this.$modal.confirm("此操作将清空所有现有数据并重新同步，是否继续？").then((function(){e.loading=!0,u(10).then((function(t){e.$modal.msgSuccess(t.msg),e.getList()})).finally((function(){e.loading=!1}))}))},submitSync:function(){var e=this;this.$refs["syncForm"].validate((function(t){t&&(e.syncDialogVisible=!1,e.loading=!0,c(e.syncForm.count,e.syncForm.offset).then((function(t){e.$modal.msgSuccess(t.msg),e.getList()})).finally((function(){e.loading=!1})))}))},handleExport:function(){this.download("miniapp/haitang/news/export",Object(r["a"])({},this.queryParams),"news_".concat((new Date).getTime(),".xlsx"))},previewImage:function(e){if(e){var t='\n          <div style="text-align: center; padding: 20px;">\n            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>\n            <div style="margin-top: 10px; color: #666;">图片加载中...</div>\n          </div>\n        ',n=(this.$msgbox({title:"封面图预览",dangerouslyUseHTMLString:!0,message:t,showCancelButton:!1,showConfirmButton:!0,confirmButtonText:"关闭",customClass:"image-preview-dialog"}),new Image);n.onload=function(){var t='\n            <img\n              src="'.concat(e,'"\n              alt="封面图预览"\n              referrerpolicy="no-referrer"\n              style="max-width: 100%; max-height: 500px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);"\n            />\n          '),n=document.querySelector(".image-preview-dialog .el-message-box__message");n&&(n.innerHTML=t)},n.onerror=function(){var e='\n            <div style="text-align: center; padding: 20px; color: #F56C6C;">\n              <i class="el-icon-picture-outline" style="font-size: 48px; margin-bottom: 10px;"></i>\n              <div>图片加载失败</div>\n              <div style="font-size: 12px; margin-top: 5px; color: #999;">请检查网络连接或图片链接</div>\n            </div>\n          ',t=document.querySelector(".image-preview-dialog .el-message-box__message");t&&(t.innerHTML=e)},n.src=e,n.referrerPolicy="no-referrer"}}}},p=m,d=(n("b2bf"),n("6426"),n("2877")),f=Object(d["a"])(p,a,i,!1,null,"778d7076",null);t["default"]=f.exports},fa1c:function(e,t,n){}}]);