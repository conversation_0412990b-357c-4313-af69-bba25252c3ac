<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.XiqingActivityContentMapper">
    
    <resultMap type="XiqingActivityContent" id="XiqingActivityContentResult">
        <result property="contentId"    column="content_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectXiqingActivityContentVo">
        select content_id, title, content, status, create_by, create_time, update_by, update_time, remark from xiqing_activity_content
    </sql>

    <select id="selectXiqingActivityContentList" parameterType="XiqingActivityContent" resultMap="XiqingActivityContentResult">
        <include refid="selectXiqingActivityContentVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectXiqingActivityContentByContentId" parameterType="Long" resultMap="XiqingActivityContentResult">
        <include refid="selectXiqingActivityContentVo"/>
        where content_id = #{contentId}
    </select>

    <select id="selectEnabledXiqingActivityContent" resultMap="XiqingActivityContentResult">
        <include refid="selectXiqingActivityContentVo"/>
        where status = '0'
        order by create_time desc
        limit 1
    </select>
        
    <update id="updateXiqingActivityContent" parameterType="XiqingActivityContent">
        update xiqing_activity_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where content_id = #{contentId}
    </update>
</mapper>
