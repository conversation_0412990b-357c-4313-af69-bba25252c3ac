{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=template&id=62cf47f3&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1753946374542}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}