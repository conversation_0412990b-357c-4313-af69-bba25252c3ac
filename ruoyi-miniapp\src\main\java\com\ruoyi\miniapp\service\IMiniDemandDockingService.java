package com.ruoyi.miniapp.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.miniapp.domain.MiniDemandDocking;

/**
 * 需求对接Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IMiniDemandDockingService 
{
    /**
     * 查询需求对接
     * 
     * @param dockingId 需求对接主键
     * @return 需求对接
     */
    public MiniDemandDocking selectMiniDemandDockingByDockingId(Long dockingId);

    /**
     * 查询需求对接列表
     * 
     * @param miniDemandDocking 需求对接
     * @return 需求对接集合
     */
    public List<MiniDemandDocking> selectMiniDemandDockingList(MiniDemandDocking miniDemandDocking);

    /**
     * 新增需求对接
     * 
     * @param miniDemandDocking 需求对接
     * @return 结果
     */
    public int insertMiniDemandDocking(MiniDemandDocking miniDemandDocking);

    /**
     * 修改需求对接
     * 
     * @param miniDemandDocking 需求对接
     * @return 结果
     */
    public int updateMiniDemandDocking(MiniDemandDocking miniDemandDocking);

    /**
     * 批量删除需求对接
     * 
     * @param dockingIds 需要删除的需求对接主键集合
     * @return 结果
     */
    public int deleteMiniDemandDockingByDockingIds(Long[] dockingIds);

    /**
     * 删除需求对接信息
     * 
     * @param dockingId 需求对接主键
     * @return 结果
     */
    public int deleteMiniDemandDockingByDockingId(Long dockingId);

    /**
     * 用户对接需求（我要对接）
     * 
     * @param demandId 需求ID
     * @param userId 用户ID
     * @return 对接结果，包含联系方式信息
     */
    public Map<String, Object> dockDemand(Long demandId, Long userId);

    /**
     * 检查用户是否已对接某需求
     * 
     * @param demandId 需求ID
     * @param userId 用户ID
     * @return 是否已对接
     */
    public boolean isDocked(Long demandId, Long userId);

    /**
     * 获取用户的对接列表（我的对接）
     * 
     * @param userId 用户ID
     * @return 对接列表
     */
    public List<MiniDemandDocking> getMyDockingList(Long userId);

    /**
     * 获取需求的对接列表（后台管理用）
     * 
     * @param demandId 需求ID
     * @return 对接列表
     */
    public List<MiniDemandDocking> getDockingListByDemandId(Long demandId);

    /**
     * 获取需求的对接详情列表（包含用户信息）
     * 
     * @param demandId 需求ID
     * @return 对接详情列表
     */
    public List<MiniDemandDocking> getDockingDetailListByDemandId(Long demandId);

    /**
     * 获取需求对接统计信息
     * 
     * @param demandId 需求ID
     * @return 统计信息
     */
    public Map<String, Object> getDockingStats(Long demandId);

    /**
     * 批量查询用户对接状态
     *
     * @param userId 用户ID
     * @param demandIds 需求ID列表
     * @return 对接状态列表
     */
    public List<MiniDemandDocking> batchGetDockingStatus(Long userId, List<Long> demandIds);


}
