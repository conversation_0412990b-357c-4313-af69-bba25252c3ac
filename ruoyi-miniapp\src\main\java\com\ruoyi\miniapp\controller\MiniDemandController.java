package com.ruoyi.miniapp.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniDemand;
import com.ruoyi.miniapp.domain.MiniDemandDocking;
import com.ruoyi.miniapp.service.IMiniDemandService;
import com.ruoyi.miniapp.service.IMiniDemandDockingService;
import com.ruoyi.miniapp.service.IMiniDemandDockingService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 需求信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "需求信息管理")
@RestController
@RequestMapping("/miniapp/demand")
public class MiniDemandController extends BaseController
{
    @Autowired
    private IMiniDemandService miniDemandService;

    @Autowired
    private IMiniDemandDockingService miniDemandDockingService;


    /**
     * 查询需求信息列表
     */
    @ApiOperation("查询需求信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniDemand miniDemand)
    {
        startPage();
        List<MiniDemand> list = miniDemandService.selectMiniDemandList(miniDemand);
        return getDataTable(list);
    }

    /**
     * 导出需求信息列表
     */
    @ApiOperation("导出需求信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:export')")
    @Log(title = "需求信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniDemand miniDemand)
    {
        List<MiniDemand> list = miniDemandService.selectMiniDemandList(miniDemand);
        ExcelUtil<MiniDemand> util = new ExcelUtil<MiniDemand>(MiniDemand.class);
        util.exportExcel(response, list, "需求信息数据");
    }

    /**
     * 获取需求信息详细信息
     */
    @ApiOperation("获取需求信息详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        return AjaxResult.success(miniDemandService.selectMiniDemandByDemandId(demandId));
    }

    /**
     * 新增需求信息
     */
    @ApiOperation("新增需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:add')")
    @Log(title = "需求信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("需求信息") @RequestBody MiniDemand miniDemand)
    {
        return toAjax(miniDemandService.insertMiniDemand(miniDemand));
    }

    /**
     * 修改需求信息
     */
    @ApiOperation("修改需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @Log(title = "需求信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("需求信息") @RequestBody MiniDemand miniDemand)
    {
        return toAjax(miniDemandService.updateMiniDemand(miniDemand));
    }

    /**
     * 获取需求的对接记录列表
     */
    @ApiOperation("获取需求的对接记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:list')")
    @GetMapping("/{demandId}/dockings")
    public AjaxResult getDockingList(@ApiParam("需求ID") @PathVariable Long demandId)
    {
        try
        {
            List<MiniDemandDocking> list = miniDemandDockingService.getDockingDetailListByDemandId(demandId);
            return AjaxResult.success(list);
        }
        catch (Exception e)
        {
            logger.error("获取需求对接记录失败", e);
            return error("获取对接记录失败：" + e.getMessage());
        }
    }

    /**
     * 删除需求信息
     */
    @ApiOperation("删除需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:remove')")
    @Log(title = "需求信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("需求ID数组") @RequestBody Long[] demandIds)
    {
        return toAjax(miniDemandService.deleteMiniDemandByDemandIds(demandIds));
    }

    /**
     * 下架需求信息
     */
    @ApiOperation("下架需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @Log(title = "需求信息", businessType = BusinessType.UPDATE)
    @PostMapping("/offShelf")
    public AjaxResult offShelf(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        MiniDemand miniDemand = new MiniDemand();
        miniDemand.setDemandId(demandId);
        miniDemand.setDemandStatus("2"); // 2表示已下架
        return toAjax(miniDemandService.updateMiniDemand(miniDemand));
    }

    /**
     * 上架需求信息
     */
    @ApiOperation("上架需求信息")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @Log(title = "需求信息", businessType = BusinessType.UPDATE)
    @PostMapping("/onShelf")
    public AjaxResult onShelf(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        MiniDemand miniDemand = new MiniDemand();
        miniDemand.setDemandId(demandId);
        miniDemand.setDemandStatus("0"); // 0表示已发布
        return toAjax(miniDemandService.updateMiniDemand(miniDemand));
    }

    /**
     * 根据分类ID查询需求信息列表
     */
    @ApiOperation("根据分类ID查询需求信息列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:list')")
    @PostMapping("/getDemandsByCategory")
    public AjaxResult getDemandsByCategory(@ApiParam("分类ID") @RequestBody Long categoryId)
    {
        List<MiniDemand> list = miniDemandService.selectMiniDemandsByCategoryId(categoryId);
        return AjaxResult.success(list);
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取推荐的需求信息列表
     */
    @ApiOperation("获取推荐的需求信息列表")
    @PostMapping("/app/getRecommendedList")
    public AjaxResult getRecommendedList()
    {
        List<MiniDemand> list = miniDemandService.selectRecommendedMiniDemandList();
        return AjaxResult.success(list);
    }

//    /**
//     * 获取启用的需求信息列表
//     */
//    @ApiOperation("获取启用的需求信息列表")
//    @PostMapping("/app/getEnabledList")
//    public AjaxResult getEnabledList()
//    {
//        List<MiniDemand> list = miniDemandService.selectEnabledMiniDemandList();
//        return AjaxResult.success(list);
//    }

//    /**
//     * 根据分类获取需求信息列表
//     */
//    @ApiOperation("根据分类获取需求信息列表")
//    @PostMapping("/app/getDemandsByCategory")
//    public AjaxResult getDemandsByCategoryForApp(@ApiParam("分类ID") @RequestBody Long categoryId)
//    {
//        List<MiniDemand> list = miniDemandService.selectMiniDemandsByCategoryId(categoryId);
//        return AjaxResult.success(list);
//    }

    /**
     * 获取需求详情
     */
    @ApiOperation("获取需求详情")
    @PostMapping("/app/getDetail")
    public AjaxResult getDetail(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        return AjaxResult.success(miniDemandService.selectMiniDemandByDemandId(demandId));
    }

    /**
     * 增加需求浏览次数
     */
    @ApiOperation("增加需求浏览次数")
    @PostMapping("/app/increaseViewCount")
    public AjaxResult increaseViewCount(@ApiParam("需求ID") @RequestBody Long demandId)
    {
        return toAjax(miniDemandService.increaseViewCount(demandId));
    }

    /**
     * 获取我的发布需求列表
     */
    @ApiOperation("获取我的发布需求列表")
    @PostMapping("/app/getMyPublishedDemands")
    public AjaxResult getMyPublishedDemands(@ApiParam("用户ID") @RequestBody Long userId)
    {
        List<MiniDemand> list = miniDemandService.selectMiniDemandListByUserId(userId);
        return AjaxResult.success(list);
    }

    // ================================ 需求对接相关接口 ================================

    /**
     * 我要对接（揭榜）
     */
    @ApiOperation("我要对接")
    @PostMapping("/app/dockDemand/{demandId}")
    public AjaxResult dockDemand(@ApiParam("需求ID") @PathVariable("demandId") Long demandId)
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long userId = loginUser.getUserId();

            // 执行对接操作
            Map<String, Object> result = miniDemandDockingService.dockDemand(demandId, userId);

            return AjaxResult.success("对接成功", result);
        }
        catch (Exception e)
        {
            logger.error("需求对接失败", e);
            return error("对接失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否已对接某需求
     */
    @ApiOperation("检查是否已对接某需求")
    @GetMapping("/app/checkDockingStatus/{demandId}")
    public AjaxResult checkDockingStatus(@ApiParam("需求ID") @PathVariable("demandId") Long demandId)
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long userId = loginUser.getUserId();

            // 检查对接状态
            boolean isDocked = miniDemandDockingService.isDocked(demandId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("isDocked", isDocked);

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("检查对接状态失败", e);
            return error("检查对接状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取我的对接列表
     */
    @ApiOperation("获取我的对接列表")
    @GetMapping("/app/getMyDockingList")
    public AjaxResult getMyDockingList()
    {
        try
        {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long userId = loginUser.getUserId();

            // 获取对接列表
            List<com.ruoyi.miniapp.domain.MiniDemandDocking> dockingList = miniDemandDockingService.getMyDockingList(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("dockingList", dockingList);
            result.put("dockingCount", dockingList.size());

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("获取对接列表失败", e);
            return error("获取对接列表失败：" + e.getMessage());
        }
    }



    /**
     * 获取需求对接统计信息
     */
    @ApiOperation("获取需求对接统计信息")
    @GetMapping("/app/getDockingStats/{demandId}")
    public AjaxResult getDockingStats(@ApiParam("需求ID") @PathVariable("demandId") Long demandId)
    {
        try
        {
            Map<String, Object> stats = miniDemandDockingService.getDockingStats(demandId);
            return AjaxResult.success("获取成功", stats);
        }
        catch (Exception e)
        {
            logger.error("获取对接统计失败", e);
            return error("获取对接统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取需求的对接详情列表（后台管理用）
     */
    @ApiOperation("获取需求的对接详情列表")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:list')")
    @GetMapping("/getDockingDetailList/{demandId}")
    public AjaxResult getDockingDetailList(@ApiParam("需求ID") @PathVariable("demandId") Long demandId)
    {
        try
        {
            List<com.ruoyi.miniapp.domain.MiniDemandDocking> dockingList = miniDemandDockingService.getDockingDetailListByDemandId(demandId);

            Map<String, Object> result = new HashMap<>();
            result.put("dockingList", dockingList);
            result.put("dockingCount", dockingList.size());

            return AjaxResult.success("获取成功", result);
        }
        catch (Exception e)
        {
            logger.error("获取需求对接详情失败", e);
            return error("获取需求对接详情失败：" + e.getMessage());
        }
    }

    /**
     * 手动更新需求状态（后台管理用）
     */
    @ApiOperation("手动更新需求状态")
    @PreAuthorize("@ss.hasPermi('miniapp:demand:edit')")
    @PutMapping("/updateStatus/{demandId}/{status}")
    public AjaxResult updateDemandStatus(@ApiParam("需求ID") @PathVariable("demandId") Long demandId,
                                         @ApiParam("状态") @PathVariable("status") String status)
    {
        try
        {
            // 验证状态值
            if (!"0".equals(status) && !"1".equals(status) && !"2".equals(status))
            {
                return error("无效的状态值");
            }

            MiniDemand demand = new MiniDemand();
            demand.setDemandId(demandId);
            demand.setDemandStatus(status);

            int result = miniDemandService.updateMiniDemand(demand);

            if (result > 0)
            {
                String statusText = "0".equals(status) ? "已发布" :
                                   "1".equals(status) ? "已对接" : "已下架";
                return AjaxResult.success("需求状态已更新为：" + statusText);
            }
            else
            {
                return error("状态更新失败");
            }
        }
        catch (Exception e)
        {
            logger.error("更新需求状态失败", e);
            return error("更新需求状态失败：" + e.getMessage());
        }
    }
}