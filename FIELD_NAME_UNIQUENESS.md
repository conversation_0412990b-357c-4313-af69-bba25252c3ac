# 活动表单字段名称唯一性改进

## 概述

本次改进确保了活动表单配置中每个字段的 `name` 属性都是唯一的，避免了字段名称重复导致的数据处理问题。

## 主要改进

### 1. 自动生成唯一字段名称

- **新增方法**: `generateUniqueFieldName(label)`
- **功能**: 基于字段标签自动生成唯一的字段名称
- **逻辑**: 
  - 首先尝试使用预定义的中文到英文映射
  - 如果没有映射，则将中文转换为下划线分隔的格式
  - 检查名称是否已存在，如存在则添加数字后缀（如 `name_1`, `name_2`）

### 2. 字段名称存在性检查

- **新增方法**: `isFieldNameExists(name)`
- **功能**: 检查指定的字段名称是否已经存在于当前表单字段中

### 3. 添加字段时自动分配唯一名称

- **修改方法**: `addFormField()`
- **改进**: 新添加的字段会自动获得唯一的默认名称

### 4. 模板应用时确保唯一性

- **修改方法**: `handleTemplateCommand()`
- **改进**: 应用预设模板时，每个字段都会重新生成唯一的名称

### 5. 保存时验证唯一性

- **修改方法**: `saveFormConfig()`
- **新增验证**: 
  - 检查字段名称不能为空
  - 检查字段名称必须唯一
  - 如发现重复名称，会阻止保存并提示用户

### 6. 界面显示改进

- **字段预览区域**: 显示字段的唯一标识符（绿色标签）
- **字段配置区域**: 
  - 新增"字段名称"输入框（只读），显示自动生成的唯一标识符
  - 在字段头部也显示字段名称标签

## 中文标签映射表

```javascript
const pinyin = {
  '姓名': 'name',
  '联系电话': 'phone',
  '电话': 'phone',
  '邮箱': 'email',
  '邮箱地址': 'email',
  '公司': 'company',
  '项目名称': 'project_name',
  '项目描述': 'project_description',
  '团队规模': 'team_size'
};
```

## 名称生成规则

1. **优先使用映射表**: 如果标签在映射表中存在，使用对应的英文名称
2. **中文转换**: 将中文字符替换为下划线，移除特殊字符
3. **默认名称**: 如果生成的名称为空，使用 `field` 作为基础名称
4. **唯一性保证**: 如果名称已存在，添加数字后缀（`_1`, `_2`, `_3`...）

## 示例

### 基础映射
- `姓名` → `name`
- `联系电话` → `phone`
- `邮箱地址` → `email`

### 重复处理
- 第一个`姓名` → `name`
- 第二个`姓名` → `name_1`
- 第三个`姓名` → `name_2`

### 中文转换
- `用户姓名` → `用户姓名` → `___` → `field` (因为转换后为空)
- `项目介绍` → `项目介绍` → `___` → `field`

## 测试

运行 `test-unique-names.html` 文件可以验证字段名称唯一性生成逻辑的正确性。

## 注意事项

1. **字段名称只读**: 用户无法直接修改字段名称，确保系统生成的唯一性
2. **自动更新**: 当用户修改字段标签时，如果字段名称为空，会自动生成新的唯一名称
3. **向后兼容**: 现有的表单配置不会受到影响，只有新添加或修改的字段才会应用新的唯一性规则

## 技术细节

- **文件位置**: `ruoyi-ui/src/views/miniapp/xiqing/activity-config/index.vue`
- **主要修改行数**: 约50行代码修改和新增
- **影响范围**: 仅限于活动表单配置功能，不影响其他模块
