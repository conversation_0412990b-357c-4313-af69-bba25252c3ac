package com.ruoyi.miniapp.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 需求信息对象 mini_demand
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniDemand extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 需求ID
     */
    private Long demandId;

    /**
     * 分类ID
     */
    @Excel(name = "分类ID")
    private Long categoryId;

    /**
     * 发布用户ID
     */
    @Excel(name = "发布用户ID")
    private Long userId;

    /**
     * 需求标题
     */
    @Excel(name = "需求标题")
    private String demandTitle;

    /**
     * 需求类型
     */
    @Excel(name = "需求类型")
    private String demandType;

    /**
     * 需求描述
     */
    @Excel(name = "需求描述")
    private String demandDesc;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    private String contactName;

    /**
     * 联系人手机
     */
    @Excel(name = "联系人手机")
    private String contactPhone;

    /**
     * 需求状态（0已发布 1已对接 2已下架）
     */
    @Excel(name = "需求状态", readConverterExp = "0=已发布,1=已对接,2=已下架")
    private String demandStatus;

    /**
     * 是否置顶（0否 1是）
     */
    @Excel(name = "是否置顶", readConverterExp = "0=否,1=是")
    private String isTop;

    /**
     * 浏览次数
     */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /**
     * 需求类型名称（关联查询字段，非数据库字段）
     */
    private String categoryName;

    /**
     * 需求类型名称（关联查询字段，非数据库字段）
     */
    private String categoryShortName;

    /**
     * 需求类型名称（关联查询字段，非数据库字段）
     */
    private String categoryCode;

    /**
     * 动态表单数据(JSON格式)
     */
    @Excel(name = "动态表单数据")
    private String formData;

    /**
     * 时间筛选条件（非数据库字段，用于查询）
     */
    private String timeFilter;

    /**
     * 是否有对接（非数据库字段，用于后台显示揭榜状态）
     */
    private Boolean hasDocking;

    /**
     * 对接总数（非数据库字段）
     */
    @ApiModelProperty("对接总数")
    private Integer dockingCount;

    /**
     * 已联系数量（非数据库字段）
     */
    @ApiModelProperty("已联系数量")
    private Integer contactedCount;

    /**
     * 未联系数量（非数据库字段）
     */
    @ApiModelProperty("未联系数量")
    private Integer uncontactedCount;

    public void setDemandId(Long demandId) {
        this.demandId = demandId;
    }

    public Long getDemandId() {
        return demandId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public String getCategoryShortName() {
        return categoryShortName;
    }

    public void setCategoryShortName(String categoryShortName) {
        this.categoryShortName = categoryShortName;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setDemandTitle(String demandTitle) {
        this.demandTitle = demandTitle;
    }

    public String getDemandTitle() {
        return demandTitle;
    }

    public void setDemandType(String demandType) {
        this.demandType = demandType;
    }

    public String getDemandType() {
        return demandType;
    }

    public void setDemandDesc(String demandDesc) {
        this.demandDesc = demandDesc;
    }

    public String getDemandDesc() {
        return demandDesc;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setDemandStatus(String demandStatus) {
        this.demandStatus = demandStatus;
    }

    public String getDemandStatus() {
        return demandStatus;
    }

    public void setIsTop(String isTop) {
        this.isTop = isTop;
    }

    public String getIsTop() {
        return isTop;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() {
        return viewCount;
    }


    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    public String getFormData() {
        return formData;
    }

    public void setTimeFilter(String timeFilter) {
        this.timeFilter = timeFilter;
    }

    public String getTimeFilter() {
        return timeFilter;
    }

    // 添加setTitle方法作为setDemandTitle的别名
    public void setTitle(String title) {
        this.demandTitle = title;
    }

    public String getTitle() {
        return demandTitle;
    }

    public void setHasDocking(Boolean hasDocking) {
        this.hasDocking = hasDocking;
    }

    public Boolean getHasDocking() {
        return hasDocking;
    }

    public void setDockingCount(Integer dockingCount) {
        this.dockingCount = dockingCount;
    }

    public Integer getDockingCount() {
        return dockingCount;
    }

    public void setContactedCount(Integer contactedCount) {
        this.contactedCount = contactedCount;
    }

    public Integer getContactedCount() {
        return contactedCount;
    }

    public void setUncontactedCount(Integer uncontactedCount) {
        this.uncontactedCount = uncontactedCount;
    }

    public Integer getUncontactedCount() {
        return uncontactedCount;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    @Override
    public String toString() {
        return "MiniDemand{" +
                "demandId=" + demandId +
                ", categoryId=" + categoryId +
                ", userId=" + userId +
                ", demandTitle='" + demandTitle + '\'' +
                ", demandType='" + demandType + '\'' +
                ", demandDesc='" + demandDesc + '\'' +
                ", contactName='" + contactName + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", demandStatus='" + demandStatus + '\'' +
                ", isTop='" + isTop + '\'' +
                ", viewCount=" + viewCount +
                ", categoryName='" + categoryName + '\'' +
                ", categoryShortName='" + categoryShortName + '\'' +
                ", categoryCode='" + categoryCode + '\'' +
                ", formData='" + formData + '\'' +
                ", timeFilter='" + timeFilter + '\'' +
                ", hasDocking=" + hasDocking +
                ", dockingCount=" + dockingCount +
                ", contactedCount=" + contactedCount +
                ", uncontactedCount=" + uncontactedCount +
                '}';
    }
}