package com.ruoyi.miniapp.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 敏感词过滤注解
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveWordFilter {
    
    /**
     * 模块名称
     */
    String moduleName() default "";
    
    /**
     * 处理策略
     */
    FilterStrategy strategy() default FilterStrategy.REPLACE;
    
    /**
     * 替换字符（当策略为REPLACE时使用）
     */
    char replacement() default '*';
    
    /**
     * 是否忽略错误（当过滤失败时是否继续执行）
     */
    boolean ignoreError() default true;

    /**
     * 指定要检测的字段名称数组
     * 如果为空，则检测所有String类型字段
     * 如果指定了字段名称，则只检测指定的字段
     */
    String[] fields() default {};
    
    /**
     * 过滤策略枚举
     */
    enum FilterStrategy {
        /**
         * 替换敏感词
         */
        REPLACE,
        
        /**
         * 拒绝包含敏感词的操作
         */
        REJECT,
        
        /**
         * 仅记录日志，不修改内容
         */
        LOG_ONLY
    }
}
