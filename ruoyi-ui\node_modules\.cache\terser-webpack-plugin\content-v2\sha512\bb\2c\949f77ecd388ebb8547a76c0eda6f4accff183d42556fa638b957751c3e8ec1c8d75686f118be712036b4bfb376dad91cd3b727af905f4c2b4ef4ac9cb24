{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-1ba0f8a5\"],{\"16bb\":function(e,t,a){},\"39e7\":function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"需求标题\",prop:\"demandTitle\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入需求标题\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.demandTitle,callback:function(t){e.$set(e.queryParams,\"demandTitle\",t)},expression:\"queryParams.demandTitle\"}})],1),a(\"el-form-item\",{attrs:{label:\"需求类型\",prop:\"categoryId\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择需求类型\",clearable:\"\"},model:{value:e.queryParams.categoryId,callback:function(t){e.$set(e.queryParams,\"categoryId\",t)},expression:\"queryParams.categoryId\"}},e._l(e.categoryList,(function(e){return a(\"el-option\",{key:e.categoryId,attrs:{label:e.categoryName,value:e.categoryId}})})),1)],1),a(\"el-form-item\",{attrs:{label:\"需求状态\",prop:\"demandStatus\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择需求状态\",clearable:\"\"},model:{value:e.queryParams.demandStatus,callback:function(t){e.$set(e.queryParams,\"demandStatus\",t)},expression:\"queryParams.demandStatus\"}},[a(\"el-option\",{attrs:{label:\"已发布\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"已对接\",value:\"1\"}}),a(\"el-option\",{attrs:{label:\"已下架\",value:\"2\"}})],1)],1),a(\"el-form-item\",{attrs:{label:\"对接状态\",prop:\"hasDocking\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择对接状态\",clearable:\"\"},model:{value:e.queryParams.hasDocking,callback:function(t){e.$set(e.queryParams,\"hasDocking\",t)},expression:\"queryParams.hasDocking\"}},[a(\"el-option\",{attrs:{label:\"未对接\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"对接中\",value:\"1\"}})],1)],1),a(\"el-form-item\",{attrs:{label:\"时间筛选\",prop:\"timeFilter\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择时间范围\",clearable:\"\"},model:{value:e.queryParams.timeFilter,callback:function(t){e.$set(e.queryParams,\"timeFilter\",t)},expression:\"queryParams.timeFilter\"}},[a(\"el-option\",{attrs:{label:\"一周内发布\",value:\"week_within\"}}),a(\"el-option\",{attrs:{label:\"发布满一周\",value:\"week_over\"}}),a(\"el-option\",{attrs:{label:\"发布满一月\",value:\"month_over\"}}),a(\"el-option\",{attrs:{label:\"发布满一年\",value:\"year_over\"}})],1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:add\"],expression:\"['miniapp:demand:add']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-plus\",size:\"mini\"},on:{click:e.handleAdd}},[e._v(\"新增\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:edit\"],expression:\"['miniapp:demand:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:remove\"],expression:\"['miniapp:demand:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:export\"],expression:\"['miniapp:demand:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.demandList},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"50\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"需求ID\",align:\"center\",prop:\"demandId\",width:\"70\"}}),a(\"el-table-column\",{attrs:{label:\"需求标题\",align:\"left\",prop:\"demandTitle\",\"min-width\":\"180\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"需求类型\",align:\"center\",prop:\"categoryName\",width:\"90\"}}),a(\"el-table-column\",{attrs:{label:\"需求状态\",align:\"center\",width:\"80\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"0\"===t.row.demandStatus?a(\"el-tag\",{attrs:{type:\"success\",size:\"small\"}},[e._v(\"已发布\")]):\"1\"===t.row.demandStatus?a(\"el-tag\",{attrs:{type:\"warning\",size:\"small\"}},[e._v(\"已对接\")]):a(\"el-tag\",{attrs:{type:\"danger\",size:\"small\"}},[e._v(\"已下架\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"对接状态\",align:\"center\",width:\"80\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[!0===t.row.hasDocking||1===t.row.hasDocking?a(\"el-tag\",{attrs:{type:\"warning\",size:\"small\"}},[e._v(\"对接中\")]):a(\"el-tag\",{attrs:{type:\"info\",size:\"small\"}},[e._v(\"未对接\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"是否置顶\",align:\"center\",width:\"80\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"1\"===t.row.isTop?a(\"el-tag\",{attrs:{type:\"warning\",size:\"mini\"}},[e._v(\"置顶\")]):a(\"span\",[e._v(\"-\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.createTime,\"{m}-{d} {h}:{i}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"320\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"table-actions\"},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:query\"],expression:\"['miniapp:demand:query']\"}],attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v(\"详情\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:edit\"],expression:\"['miniapp:demand:edit']\"}],attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(\"修改\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:remove\"],expression:\"['miniapp:demand:remove']\"}],attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:edit\"],expression:\"['miniapp:demand:edit']\"}],attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return e.handleToggleTop(t.row)}}},[e._v(e._s(\"1\"===t.row.isTop?\"取消置顶\":\"置顶\"))]),\"2\"!==t.row.demandStatus?a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:edit\"],expression:\"['miniapp:demand:edit']\"}],staticStyle:{color:\"#E6A23C\"},attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return e.handleOffShelf(t.row)}}},[e._v(\"下架\")]):e._e(),\"2\"===t.row.demandStatus?a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:demand:edit\"],expression:\"['miniapp:demand:edit']\"}],staticStyle:{color:\"#67C23A\"},attrs:{size:\"mini\",type:\"text\"},on:{click:function(a){return e.handleOnShelf(t.row)}}},[e._v(\"上架\")]):e._e()],1)]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total > 0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:\"联系记录\",visible:e.contactDialogVisible,width:\"50%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.contactDialogVisible=t}}},[a(\"el-form\",{ref:\"contactForm\",attrs:{model:e.contactForm,rules:e.contactRules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"对接用户\"}},[a(\"span\",[e._v(e._s(e.contactForm.userName)+\" (\"+e._s(e.contactForm.userPhone)+\")\")])]),a(\"el-form-item\",{attrs:{label:\"是否已联系\",prop:\"isContacted\"}},[a(\"el-radio-group\",{model:{value:e.contactForm.isContacted,callback:function(t){e.$set(e.contactForm,\"isContacted\",t)},expression:\"contactForm.isContacted\"}},[a(\"el-radio\",{attrs:{label:\"0\"}},[e._v(\"未联系\")]),a(\"el-radio\",{attrs:{label:\"1\"}},[e._v(\"已联系\")])],1)],1),\"1\"===e.contactForm.isContacted?a(\"el-form-item\",{attrs:{label:\"联系结果\",prop:\"contactResult\"}},[a(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择联系结果\",clearable:\"\"},model:{value:e.contactForm.contactResult,callback:function(t){e.$set(e.contactForm,\"contactResult\",t)},expression:\"contactForm.contactResult\"}},[a(\"el-option\",{attrs:{label:\"联系成功\",value:\"联系成功\"}}),a(\"el-option\",{attrs:{label:\"无人接听\",value:\"无人接听\"}}),a(\"el-option\",{attrs:{label:\"号码错误\",value:\"号码错误\"}}),a(\"el-option\",{attrs:{label:\"拒绝沟通\",value:\"拒绝沟通\"}}),a(\"el-option\",{attrs:{label:\"稍后联系\",value:\"稍后联系\"}}),a(\"el-option\",{attrs:{label:\"已有合作\",value:\"已有合作\"}}),a(\"el-option\",{attrs:{label:\"不感兴趣\",value:\"不感兴趣\"}}),a(\"el-option\",{attrs:{label:\"其他\",value:\"其他\"}})],1)],1):e._e(),a(\"el-form-item\",{attrs:{label:\"联系备注\",prop:\"contactNotes\"}},[a(\"el-input\",{attrs:{type:\"textarea\",rows:4,placeholder:\"请输入联系备注，如沟通内容、后续计划等\",maxlength:\"500\",\"show-word-limit\":\"\"},model:{value:e.contactForm.contactNotes,callback:function(t){e.$set(e.contactForm,\"contactNotes\",t)},expression:\"contactForm.contactNotes\"}})],1),\"1\"===e.contactForm.isContacted?a(\"el-form-item\",{attrs:{label:\"联系时间\",prop:\"contactTime\"}},[a(\"el-date-picker\",{staticStyle:{width:\"100%\"},attrs:{type:\"datetime\",placeholder:\"选择联系时间\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\"},model:{value:e.contactForm.contactTime,callback:function(t){e.$set(e.contactForm,\"contactTime\",t)},expression:\"contactForm.contactTime\"}})],1):e._e()],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(t){e.contactDialogVisible=!1}}},[e._v(\"取 消\")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitContactForm}},[e._v(\"确 定\")])],1)],1),a(\"el-dialog\",{attrs:{title:\"需求详情\",visible:e.detailDialogVisible,width:\"70%\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.detailDialogVisible=t}}},[a(\"div\",{staticClass:\"detail-content\"},[a(\"div\",{staticClass:\"info-section\"},[a(\"h4\",{staticClass:\"section-header\"},[e._v(\"内容信息\")]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"标题：\")]),a(\"span\",{staticClass:\"info-value\"},[e._v(e._s(e.detailForm.demandTitle||\"\"))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"类型：\")]),a(\"span\",{staticClass:\"info-value\"},[e._v(e._s(e.detailForm.categoryName||\"\"))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"状态：\")]),a(\"span\",{staticClass:\"info-value\"},[\"0\"===e.detailForm.demandStatus?a(\"el-tag\",{attrs:{type:\"success\",size:\"small\"}},[e._v(\"已发布\")]):\"1\"===e.detailForm.demandStatus?a(\"el-tag\",{attrs:{type:\"warning\",size:\"small\"}},[e._v(\"已对接\")]):a(\"el-tag\",{attrs:{type:\"danger\",size:\"small\"}},[e._v(\"已下架\")])],1)]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"对接状态：\")]),a(\"span\",{staticClass:\"info-value\"},[!0===e.detailForm.hasDocking||1===e.detailForm.hasDocking?a(\"el-tag\",{attrs:{type:\"warning\",size:\"small\"}},[e._v(\"对接中\")]):a(\"el-tag\",{attrs:{type:\"info\",size:\"small\"}},[e._v(\"未对接\")])],1)]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"是否置顶：\")]),a(\"span\",{staticClass:\"info-value\"},[\"1\"===e.detailForm.isTop?a(\"el-tag\",{attrs:{type:\"warning\",size:\"small\"}},[e._v(\"置顶\")]):a(\"span\",[e._v(\"否\")])],1)]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"浏览次数：\")]),a(\"span\",{staticClass:\"info-value\"},[e._v(e._s(e.detailForm.viewCount||0))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"发布时间：\")]),a(\"span\",{staticClass:\"info-value\"},[e._v(e._s(e.parseTime(e.detailForm.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]),e.detailForm.demandDesc?a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"需求描述：\")]),a(\"div\",{staticClass:\"info-value description-text\"},[e._v(e._s(e.detailForm.demandDesc))])]):e._e(),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"联系人：\")]),a(\"span\",{staticClass:\"info-value\"},[e._v(e._s(e.detailForm.contactName||\"\"))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"info-label\"},[e._v(\"联系电话：\")]),a(\"span\",{staticClass:\"info-value\"},[e._v(e._s(e.detailForm.contactPhone||\"\"))])])]),a(\"el-divider\",{attrs:{\"content-position\":\"left\"}},[a(\"i\",{staticClass:\"el-icon-document\"}),e._v(\" 表单数据 \")]),e.detailForm.formDataList&&e.detailForm.formDataList.length>0?a(\"div\",{staticClass:\"form-data-section\"},[a(\"el-descriptions\",{attrs:{column:1,border:\"\"}},e._l(e.detailForm.formDataList,(function(t,i){return a(\"el-descriptions-item\",{key:i,attrs:{label:t.label,\"label-style\":{width:\"120px\",fontWeight:\"bold\"}}},[\"textarea\"===t.type?[a(\"div\",{staticClass:\"textarea-content\"},[e._v(e._s(t.value||\"未填写\"))])]:\"select\"===t.type||\"radio\"===t.type?[a(\"el-tag\",{attrs:{type:\"primary\",size:\"small\"}},[e._v(e._s(t.value||\"未选择\"))])]:\"checkbox\"===t.type?[Array.isArray(t.value)&&t.value.length>0?a(\"div\",e._l(t.value,(function(t){return a(\"el-tag\",{key:t,staticStyle:{\"margin-right\":\"5px\"},attrs:{type:\"primary\",size:\"small\"}},[e._v(e._s(t))])})),1):a(\"span\",[e._v(\"未选择\")])]:\"file\"===t.type?[Array.isArray(t.value)&&t.value.length>0?a(\"div\",e._l(t.value,(function(t,i){return a(\"div\",{key:i,staticClass:\"file-item\"},[a(\"i\",{staticClass:\"el-icon-document\"}),a(\"a\",{staticClass:\"file-link\",attrs:{href:t.url||t,target:\"_blank\"}},[e._v(\" \"+e._s(t.name||e.getFileNameFromUrl(t.url||t))+\" \")])])})),0):\"string\"===typeof t.value&&t.value?a(\"span\",[a(\"i\",{staticClass:\"el-icon-document\"}),a(\"a\",{staticClass:\"file-link\",attrs:{href:t.value,target:\"_blank\"}},[e._v(\" \"+e._s(e.getFileNameFromUrl(t.value))+\" \")])]):a(\"span\",[e._v(\"未上传\")])]:\"tel\"===t.type?[a(\"span\",{staticClass:\"phone-number\"},[e._v(e._s(t.value||\"未填写\"))])]:[a(\"span\",[e._v(e._s(t.value||\"未填写\"))])]],2)})),1)],1):e._e(),a(\"el-divider\",{attrs:{\"content-position\":\"left\"}},[a(\"i\",{staticClass:\"el-icon-user-solid\"}),e._v(\" 对接记录 \"),a(\"el-tag\",{staticStyle:{\"margin-left\":\"8px\"},attrs:{size:\"small\",type:\"info\"}},[e._v(\" 共 \"+e._s((e.detailForm.dockingList||[]).length)+\" 条记录 \")])],1),a(\"div\",{staticClass:\"docking-section\"},[a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.detailForm.dockingLoading,expression:\"detailForm.dockingLoading\"}],key:\"docking-table-\"+e.tableRefreshKey,staticClass:\"docking-table\",attrs:{data:e.detailForm.dockingList||[],size:\"small\",border:\"\"}},[a(\"template\",{slot:\"empty\"},[a(\"div\",{staticClass:\"empty-data\"},[a(\"i\",{staticClass:\"el-icon-document\"}),a(\"p\",[e._v(\"暂无对接记录\")])])]),a(\"el-table-column\",{attrs:{type:\"index\",label:\"序号\",width:\"60\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"对接用户\",width:\"200\",align:\"left\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"user-info\"},[a(\"div\",{staticClass:\"user-name\"},[a(\"i\",{staticClass:\"el-icon-user\"}),a(\"strong\",[e._v(e._s(t.row.userName||\"未知用户\"))])]),t.row.userPhone?a(\"div\",{staticClass:\"user-phone\"},[a(\"i\",{staticClass:\"el-icon-phone\"}),e._v(\" \"+e._s(t.row.userPhone)+\" \")]):e._e()])]}}])}),a(\"el-table-column\",{attrs:{label:\"工作信息\",\"min-width\":\"200\",align:\"left\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"work-info\"},[t.row.userCompany?a(\"div\",{staticClass:\"company\"},[a(\"i\",{staticClass:\"el-icon-office-building\"}),e._v(\" \"+e._s(t.row.userCompany)+\" \")]):e._e(),t.row.userPosition?a(\"div\",{staticClass:\"position\"},[a(\"i\",{staticClass:\"el-icon-suitcase\"}),e._v(\" \"+e._s(t.row.userPosition)+\" \")]):e._e(),t.row.userCompany||t.row.userPosition?e._e():a(\"div\",{staticClass:\"no-info\"},[e._v(\" 暂无工作信息 \")])])]}}])}),a(\"el-table-column\",{attrs:{label:\"对接时间\",width:\"140\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"docking-time\"},[a(\"i\",{staticClass:\"el-icon-time\"}),e._v(\" \"+e._s(e.parseTime(t.row.dockingTime,\"{y}-{m}-{d} {h}:{i}\"))+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"联系状态\",width:\"100\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-tag\",{attrs:{type:\"1\"===t.row.isContacted||1===t.row.isContacted?\"success\":\"warning\",size:\"small\"}},[e._v(\" \"+e._s(\"1\"===t.row.isContacted||1===t.row.isContacted?\"已联系\":\"未联系\")+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",width:\"120\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{attrs:{size:\"mini\",type:\"primary\",plain:\"\",icon:\"el-icon-edit-outline\"},on:{click:function(a){return e.handleContactRecord(t.row)}}},[e._v(\" 联系记录 \")])]}}])})],2)],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(t){e.detailDialogVisible=!1}}},[e._v(\"关 闭\")])],1)]),a(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"800px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\"}},[a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"需求类型\",prop:\"categoryId\"}},[a(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择需求类型\"},on:{change:e.onCategoryChange},model:{value:e.form.categoryId,callback:function(t){e.$set(e.form,\"categoryId\",t)},expression:\"form.categoryId\"}},e._l(e.categoryList,(function(e){return a(\"el-option\",{key:e.categoryId,attrs:{label:e.categoryName,value:e.categoryId}})})),1)],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"需求状态\",prop:\"demandStatus\"}},[a(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择需求状态\"},model:{value:e.form.demandStatus,callback:function(t){e.$set(e.form,\"demandStatus\",t)},expression:\"form.demandStatus\"}},[a(\"el-option\",{attrs:{label:\"已发布\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"已对接\",value:\"1\"}}),a(\"el-option\",{attrs:{label:\"已下架\",value:\"2\"}})],1)],1)],1)],1),a(\"el-form-item\",{attrs:{label:\"需求标题\",prop:\"demandTitle\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入需求标题\"},model:{value:e.form.demandTitle,callback:function(t){e.$set(e.form,\"demandTitle\",t)},expression:\"form.demandTitle\"}})],1),a(\"el-form-item\",{attrs:{label:\"需求描述\",prop:\"demandDesc\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入需求描述\",rows:4},model:{value:e.form.demandDesc,callback:function(t){e.$set(e.form,\"demandDesc\",t)},expression:\"form.demandDesc\"}})],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"联系人姓名\",prop:\"contactName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系人姓名\"},model:{value:e.form.contactName,callback:function(t){e.$set(e.form,\"contactName\",t)},expression:\"form.contactName\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"联系人电话\",prop:\"contactPhone\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系人电话\"},model:{value:e.form.contactPhone,callback:function(t){e.$set(e.form,\"contactPhone\",t)},expression:\"form.contactPhone\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"是否置顶\",prop:\"isTop\"}},[a(\"el-radio-group\",{model:{value:e.form.isTop,callback:function(t){e.$set(e.form,\"isTop\",t)},expression:\"form.isTop\"}},[a(\"el-radio\",{attrs:{label:\"0\"}},[e._v(\"否\")]),a(\"el-radio\",{attrs:{label:\"1\"}},[e._v(\"是\")])],1)],1)],1)],1),e.categoryFieldsData&&e.categoryFieldsData.length>0?a(\"div\",{staticClass:\"dynamic-fields-section\"},[a(\"el-divider\",{attrs:{\"content-position\":\"left\"}},[a(\"span\",{staticStyle:{color:\"#409EFF\",\"font-weight\":\"bold\"}},[e._v(e._s(e.getCategoryName())+\"专属字段\")])]),e._l(e.categoryFieldsData,(function(t,i){return a(\"div\",{key:\"category-\"+i,staticClass:\"category-group\"},[t.name?a(\"div\",{staticClass:\"category-title\"},[a(\"i\",{staticClass:\"el-icon-folder-opened\"}),a(\"span\",[e._v(e._s(t.name))])]):e._e(),t.description?a(\"div\",{staticClass:\"category-description\"},[e._v(\" \"+e._s(t.description)+\" \")]):e._e(),e._l(t.fields,(function(t,i){return a(\"div\",{key:\"field-\"+t.name+\"-\"+i,staticClass:\"dynamic-field-item\"},[a(\"div\",{staticClass:\"field-label\"},[t.required?a(\"span\",{staticClass:\"required-mark\"},[e._v(\"*\")]):e._e(),e._v(\" \"+e._s(t.label)+\" \")]),a(\"div\",{staticClass:\"field-content\"},[\"static\"===t.type?a(\"div\",{staticClass:\"static-content\"},[e._v(\" \"+e._s(t.staticContent)+\" \")]):\"input\"===t.type?a(\"el-input\",{attrs:{value:e.form.dynamicData[t.name]||t.value||\"\",placeholder:t.placeholder||\"请输入\"+t.label},on:{input:function(a){return e.handleFieldInput(t,a)}}}):\"textarea\"===t.type?a(\"el-input\",{attrs:{value:e.form.dynamicData[t.name]||t.value||\"\",type:\"textarea\",placeholder:t.placeholder||\"请输入\"+t.label,rows:3},on:{input:function(a){return e.handleFieldInput(t,a)}}}):\"number\"===t.type?a(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{value:e.form.dynamicData[t.name]||t.value||0,placeholder:t.placeholder||\"请输入\"+t.label},on:{change:function(a){return e.handleFieldInput(t,a)}}}):\"tel\"===t.type||\"email\"===t.type?a(\"el-input\",{attrs:{value:e.form.dynamicData[t.name]||t.value||\"\",placeholder:t.placeholder||\"请输入\"+t.label},on:{input:function(a){return e.handleFieldInput(t,a)}}}):\"radio\"===t.type?a(\"el-radio-group\",{attrs:{value:e.form.dynamicData[t.name]||t.value||\"\"},on:{input:function(a){return e.handleFieldInput(t,a)}}},e._l(e.getFieldOptions(t),(function(i,n){return a(\"el-radio\",{key:t.name+\"-radio-\"+n+\"-\"+i,attrs:{label:i}},[e._v(e._s(i))])})),1):\"checkbox\"===t.type?a(\"el-checkbox-group\",{attrs:{value:e.form.dynamicData[t.name]||t.value||[]},on:{input:function(a){return e.handleFieldInput(t,a)}}},e._l(e.getFieldOptions(t),(function(i,n){return a(\"el-checkbox\",{key:t.name+\"-checkbox-\"+n+\"-\"+i,attrs:{label:i}},[e._v(e._s(i))])})),1):\"select\"===t.type?a(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{value:e.form.dynamicData[t.name]||t.value||\"\",placeholder:t.placeholder||\"请选择\"+t.label},on:{change:function(a){return e.handleFieldInput(t,a)}}},e._l(e.getFieldOptions(t),(function(e,i){return a(\"el-option\",{key:t.name+\"-option-\"+i+\"-\"+e,attrs:{label:e,value:e}})})),1):\"date\"===t.type?a(\"el-date-picker\",{staticStyle:{width:\"100%\"},attrs:{value:e.form.dynamicData[t.name]||t.value||null,type:\"date\",placeholder:t.placeholder||\"请选择\"+t.label},on:{change:function(a){return e.handleFieldInput(t,a)}}}):\"time\"===t.type?a(\"el-time-picker\",{staticStyle:{width:\"100%\"},attrs:{value:e.form.dynamicData[t.name]||t.value||null,placeholder:t.placeholder||\"请选择\"+t.label},on:{change:function(a){return e.handleFieldInput(t,a)}}}):\"file\"===t.type?a(\"div\",[t.value&&\"string\"===typeof t.value&&t.value.startsWith(\"http\")?a(\"div\",{staticClass:\"existing-file\"},[a(\"div\",{staticClass:\"file-display\"},[a(\"i\",{staticClass:\"el-icon-document\"}),a(\"a\",{staticClass:\"file-link\",attrs:{href:t.value,target:\"_blank\"}},[e._v(\" \"+e._s(e.getFileNameFromUrl(t.value))+\" \")]),a(\"el-button\",{staticClass:\"remove-file-btn\",attrs:{type:\"text\",size:\"mini\",icon:\"el-icon-delete\"},on:{click:function(a){return e.removeFileUrl(t)}}},[e._v(\" 删除 \")])],1)]):a(\"el-upload\",{attrs:{action:\"/dev-api/common/upload\",headers:e.uploadHeaders,\"on-success\":function(a,i,n){return e.handleFileSuccess(a,i,n,t)},\"on-remove\":function(a,i){return e.handleFileRemove(a,i,t)},\"file-list\":e.getFileList(t),\"on-preview\":e.handleFilePreview}},[a(\"el-button\",{attrs:{size:\"small\",type:\"primary\"}},[e._v(\"点击上传\")]),a(\"div\",{staticClass:\"el-upload__tip\",attrs:{slot:\"tip\"},slot:\"tip\"},[e._v(\"只能上传jpg/png文件，且不超过500kb\")])],1),Array.isArray(t.value)&&t.value.length>0?a(\"div\",{staticClass:\"uploaded-files-list\"},[a(\"div\",{staticClass:\"uploaded-files-title\"},[e._v(\"已上传文件：\")]),e._l(t.value,(function(i,n){return a(\"div\",{key:\"uploaded-\"+t.name+\"-\"+n,staticClass:\"uploaded-file-item\"},[a(\"i\",{staticClass:\"el-icon-document\"}),a(\"a\",{staticClass:\"file-link\",attrs:{href:i.url||i,target:\"_blank\"},on:{click:function(t){e.downloadFile(i.url||i,i.name||e.getFileNameFromUrl(i))}}},[e._v(\" \"+e._s(i.name||e.getFileNameFromUrl(i.url||i))+\" \")]),a(\"el-button\",{staticClass:\"remove-file-btn\",attrs:{type:\"text\",size:\"mini\",icon:\"el-icon-delete\"},on:{click:function(a){return e.removeUploadedFile(t,n)}}},[e._v(\" 删除 \")])],1)}))],2):e._e()],1):e._e()],1)])}))],2)}))],2):e._e(),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1)],1)},n=[],o=a(\"ade3\"),l=a(\"2909\"),s=a(\"53ca\"),r=a(\"5530\"),c=(a(\"99af\"),a(\"4de4\"),a(\"7db0\"),a(\"d81d\"),a(\"14d9\"),a(\"a434\"),a(\"b0c0\"),a(\"e9c4\"),a(\"b64b\"),a(\"d3b7\"),a(\"ac1f\"),a(\"466d\"),a(\"2ca0\"),a(\"498a\"),a(\"0643\"),a(\"2382\"),a(\"fffc\"),a(\"4e3e\"),a(\"a573\"),a(\"159b\"),a(\"b775\"));function d(e){return Object(c[\"a\"])({url:\"/miniapp/demand/list\",method:\"post\",data:e})}function m(e){return Object(c[\"a\"])({url:\"/miniapp/demand/getInfo\",method:\"post\",data:e})}function u(e){return Object(c[\"a\"])({url:\"/miniapp/demand/add\",method:\"post\",data:e})}function f(e){return Object(c[\"a\"])({url:\"/miniapp/demand/edit\",method:\"post\",data:e})}function p(e){var t=Array.isArray(e)?e:[e];return Object(c[\"a\"])({url:\"/miniapp/demand/remove\",method:\"post\",data:t})}function h(e){return Object(c[\"a\"])({url:\"/miniapp/demand/offShelf\",method:\"post\",data:e})}function g(e){return Object(c[\"a\"])({url:\"/miniapp/demand/onShelf\",method:\"post\",data:e})}function y(e){return Object(c[\"a\"])({url:\"/miniapp/docking/updateContactStatus\",method:\"put\",data:e})}var v=a(\"8cd4\"),b={name:\"MiniDemand\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,demandList:[],contactDialogVisible:!1,contactForm:{dockingId:null,userName:\"\",userPhone:\"\",isContacted:\"0\",contactResult:\"\",contactNotes:\"\",contactTime:\"\"},contactRules:{isContacted:[{required:!0,message:\"请选择是否已联系\",trigger:\"change\"}]},detailDialogVisible:!1,detailForm:{dockingList:[],formDataList:[]},tableRefreshKey:0,categoryList:[],dynamicFields:[],selectedCategoryName:\"\",categoryFieldsData:[],uploadHeaders:{Authorization:\"Bearer \"+this.$store.getters.token},title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,demandTitle:null,categoryId:null,demandStatus:null,hasDocking:null,timeFilter:null},form:{},rules:{categoryId:[{required:!0,message:\"需求类型不能为空\",trigger:\"change\"}],demandTitle:[{required:!0,message:\"需求标题不能为空\",trigger:\"blur\"}],demandDesc:[{required:!0,message:\"需求描述不能为空\",trigger:\"blur\"}],contactName:[{required:!0,message:\"联系人姓名不能为空\",trigger:\"blur\"}],contactPhone:[{required:!0,message:\"联系人电话不能为空\",trigger:\"blur\"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,message:\"请输入正确的手机号码\",trigger:\"blur\"}],demandStatus:[{required:!0,message:\"需求状态不能为空\",trigger:\"change\"}]}}},computed:{groupedDynamicFields:function(){var e={};return this.dynamicFields.forEach((function(t){var a=t.moduleTitle||\"其他字段\";e[a]||(e[a]=[]),e[a].push(t)})),e},safeDynamicData:function(){var e=Object(r[\"a\"])({},this.form.dynamicData);return this.dynamicFields.forEach((function(t){t.name&&((\"checkbox\"!==t.type||Array.isArray(e[t.name]))&&(\"file\"!==t.type||Array.isArray(e[t.name]))||(e[t.name]=[]))})),e}},created:function(){this.getList(),this.getCategoryList(),this.testNewDataFormat()},methods:Object(o[\"a\"])(Object(o[\"a\"])(Object(o[\"a\"])(Object(o[\"a\"])(Object(o[\"a\"])(Object(o[\"a\"])({getList:function(){var e=this;this.loading=!0,d(this.queryParams).then((function(t){e.demandList=t.rows,e.total=t.total,e.loading=!1})).catch((function(t){console.error(\"获取需求列表失败:\",t),e.loading=!1,e.$modal.msgError(\"获取需求列表失败\")}))},getCategoryList:function(){var e=this;Object(v[\"d\"])().then((function(t){e.categoryList=t.data})).catch((function(t){console.error(\"获取需求类型列表失败:\",t),e.$modal.msgError(\"获取需求类型列表失败\")}))},cancel:function(){this.open=!1,this.reset()},reset:function(){var e=this;this.form={demandId:null,categoryId:null,demandTitle:\"\",demandDesc:\"\",contactName:\"\",contactPhone:\"\",demandStatus:\"0\",isTop:\"0\",remark:\"\",dynamicData:{}},Object.keys(this.rules).forEach((function(t){t.startsWith(\"dynamicData.\")&&e.$delete(e.rules,t)})),this.dynamicFields=[],this.selectedCategoryName=\"\",this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.demandId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加需求\"},handleUpdate:function(e){var t=this;this.dynamicFields=[],this.selectedCategoryName=\"\";var a=e.demandId||this.ids;m(a).then((function(e){var a=e.data;if(t.$set(t.form,\"demandId\",a.demandId),t.$set(t.form,\"categoryId\",a.categoryId),t.$set(t.form,\"demandTitle\",a.demandTitle||\"\"),t.$set(t.form,\"demandDesc\",a.demandDesc||\"\"),t.$set(t.form,\"contactName\",a.contactName||\"\"),t.$set(t.form,\"contactPhone\",a.contactPhone||\"\"),t.$set(t.form,\"demandStatus\",a.demandStatus||\"0\"),t.$set(t.form,\"isTop\",a.isTop||\"0\"),t.$set(t.form,\"remark\",a.remark||\"\"),a.formData)try{var i=JSON.parse(a.formData);Array.isArray(i)&&i.length>0&&i[0].fields?(t.$set(t.form,\"dynamicData\",{}),i.forEach((function(e){e.fields&&e.fields.forEach((function(e){void 0!==e.value&&null!==e.value&&\"\"!==e.value&&t.$set(t.form.dynamicData,e.name,e.value)}))})),t.processCategoryFieldsData(i)):(t.$set(t.form,\"dynamicData\",i),t.loadDynamicFields(t.form.categoryId))}catch(n){console.error(\"解析动态表单数据失败:\",n),t.$set(t.form,\"dynamicData\",{}),t.loadDynamicFields(t.form.categoryId)}else t.$set(t.form,\"dynamicData\",{}),t.loadDynamicFields(t.form.categoryId);t.$nextTick((function(){t.$refs.form&&t.$refs.form.clearValidate()})),t.open=!0,t.title=\"修改需求\"}))},submitForm:function(){var e=this,t=!0,a=null;this.categoryFieldsData&&this.categoryFieldsData.length>0?this.categoryFieldsData.forEach((function(i){i.fields&&i.fields.forEach((function(i){if(i.required&&i.name&&\"static\"!==i.type){var n=e.form.dynamicData[i.name],o=!1;o=\"checkbox\"===i.type||\"file\"===i.type?!Array.isArray(n)||0===n.length:null===n||void 0===n||\"\"===n,o&&(t=!1,a||(a=i.label))}}))})):this.dynamicFields.forEach((function(i){if(i.required&&i.name){var n=e.form.dynamicData[i.name],o=!1;o=\"checkbox\"===i.type||\"file\"===i.type?!Array.isArray(n)||0===n.length:null===n||void 0===n||\"\"===n,o&&(t=!1,a||(a=i.label))}})),t?this.$refs[\"form\"].validate((function(t){if(t){var a=Object(r[\"a\"])({},e.form);if(e.categoryFieldsData&&e.categoryFieldsData.length>0){var i=e.categoryFieldsData.map((function(t){return Object(r[\"a\"])(Object(r[\"a\"])({},t),{},{fields:t.fields.map((function(t){return Object(r[\"a\"])(Object(r[\"a\"])({},t),{},{value:e.form.dynamicData[t.name]||t.value||(\"checkbox\"===t.type||\"file\"===t.type?[]:\"\")})}))})}));a.formData=JSON.stringify(i)}else a.dynamicData&&Object.keys(a.dynamicData).length>0&&(a.formData=JSON.stringify(a.dynamicData));delete a.dynamicData,console.log(\"submitForm - formData.formData:\",a.formData),null!=e.form.demandId?f(a).then((function(){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):u(a).then((function(){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()}))}})):this.$modal.msgError(\"\".concat(a,\"不能为空\"))},handleDetail:function(e){var t=this;if(console.log(\"查看详情 - 原始数据:\",e),this.$set(this,\"detailForm\",Object(r[\"a\"])(Object(r[\"a\"])({},e),{},{dockingLoading:!0,dockingList:[],formDataList:[]})),e.formData)try{var a=JSON.parse(e.formData);this.$set(this.detailForm,\"formDataList\",this.parseFormDataForDisplay(a))}catch(i){console.error(\"解析表单数据失败:\",i),this.$set(this.detailForm,\"formDataList\",[])}console.log(\"详情表单数据:\",this.detailForm),this.detailDialogVisible=!0,Object(c[\"a\"])({url:\"/miniapp/demand/\".concat(e.demandId,\"/dockings\"),method:\"get\"}).then((function(e){console.log(\"对接记录响应:\",e),200===e.code?t.$set(t.detailForm,\"dockingList\",e.data||[]):(t.$set(t.detailForm,\"dockingList\",[]),console.error(\"获取对接记录失败:\",e.msg))})).catch((function(e){console.error(\"获取对接记录异常:\",e),t.$set(t.detailForm,\"dockingList\",[])})).finally((function(){t.$set(t.detailForm,\"dockingLoading\",!1)}))},handleContactRecord:function(e){console.log(\"打开联系记录弹窗，对接记录数据:\",e),this.contactForm={dockingId:e.dockingId,userName:e.userName,userPhone:e.userPhone,isContacted:e.isContacted||\"0\",contactResult:e.contactResult||\"\",contactNotes:e.contactNotes||\"\",contactTime:e.contactTime||\"\"},console.log(\"联系表单数据:\",this.contactForm),this.contactDialogVisible=!0},submitContactForm:function(){var e=this;this.$refs[\"contactForm\"].validate((function(t){if(t){\"1\"!==e.contactForm.isContacted||e.contactForm.contactTime||(e.contactForm.contactTime=e.parseTime(new Date,\"{y}-{m}-{d} {h}:{i}:{s}\"));var a=e.contactForm.dockingId,i=e.contactForm.isContacted,n=e.contactForm.contactResult,o=e.contactForm.contactNotes,l=e.contactForm.contactTime;y(e.contactForm).then((function(t){if(console.log(\"联系状态更新成功:\",t),e.$modal.msgSuccess(\"联系记录更新成功\"),e.contactDialogVisible=!1,e.detailDialogVisible&&e.detailForm.demandId&&e.detailForm.dockingList){console.log(\"开始更新本地对接记录...\");var s=e.detailForm.dockingList.find((function(e){return e.dockingId===a}));console.log(\"找到的对接记录:\",s),console.log(\"要更新的联系状态:\",i),console.log(\"当前dockingList:\",e.detailForm.dockingList),s?(console.log(\"更新前的联系状态:\",s.isContacted),e.$set(s,\"isContacted\",i),e.$set(s,\"contactResult\",n),e.$set(s,\"contactNotes\",o),e.$set(s,\"contactTime\",l),console.log(\"更新后的联系状态:\",s.isContacted),e.tableRefreshKey++,console.log(\"强制刷新表格，新key:\",e.tableRefreshKey)):(console.error(\"未找到对应的对接记录，dockingId:\",a),console.error(\"所有对接记录的ID:\",e.detailForm.dockingList.map((function(e){return e.dockingId})))),console.log(\"开始刷新详情中的对接记录...\"),e.refreshDetailDockingList()}e.getList()})).catch((function(t){console.error(\"联系状态更新失败:\",t),e.$modal.msgError(\"更新失败，请稍后重试\")}))}}))},refreshDetailDockingList:function(){var e=this;this.$set(this.detailForm,\"dockingLoading\",!0),Object(c[\"a\"])({url:\"/miniapp/demand/\".concat(this.detailForm.demandId,\"/dockings\"),method:\"get\"}).then((function(t){console.log(\"刷新对接记录响应:\",t),200===t.code?(e.$set(e.detailForm,\"dockingList\",t.data||[]),console.log(\"更新后的对接记录:\",e.detailForm.dockingList),e.tableRefreshKey++,console.log(\"服务器数据刷新后，强制刷新表格，新key:\",e.tableRefreshKey),e.debugDockingData()):(e.$set(e.detailForm,\"dockingList\",[]),console.error(\"获取对接记录失败:\",t.msg))})).catch((function(t){console.error(\"获取对接记录异常:\",t),e.$set(e.detailForm,\"dockingList\",[])})).finally((function(){e.$set(e.detailForm,\"dockingLoading\",!1)}))},parseFormDataForDisplay:function(e){var t=[];try{Array.isArray(e)&&e.length>0&&e[0].fields?e.forEach((function(e){e.fields&&Array.isArray(e.fields)&&e.fields.forEach((function(e){\"static\"!==e.type&&e.name&&void 0!==e.value&&null!==e.value&&\"\"!==e.value&&t.push({label:e.label||e.name,value:e.value,type:e.type||\"input\"})}))})):\"object\"===Object(s[\"a\"])(e)&&null!==e&&Object.keys(e).forEach((function(a){var i=e[a];void 0!==i&&null!==i&&\"\"!==i&&t.push({label:a,value:i,type:\"input\"})}))}catch(a){console.error(\"解析表单数据失败:\",a)}return t},getFileNameFromUrl:function(e){if(!e)return\"未知文件\";var t=e.split(\"/\");return t[t.length-1]||\"未知文件\"},getContactResultType:function(e){var t={\"联系成功\":\"success\",\"已有合作\":\"success\",\"无人接听\":\"warning\",\"稍后联系\":\"warning\",\"号码错误\":\"danger\",\"拒绝沟通\":\"danger\",\"不感兴趣\":\"info\",\"其他\":\"info\"};return t[e]||\"info\"},debugDockingData:function(){console.log(\"=== 调试对接记录数据 ===\"),console.log(\"detailForm.dockingList:\",this.detailForm.dockingList),this.detailForm.dockingList&&this.detailForm.dockingList.length>0&&this.detailForm.dockingList.forEach((function(e,t){console.log(\"记录\".concat(t+1,\":\"),{dockingId:e.dockingId,userName:e.userName,isContacted:e.isContacted,isContactedType:Object(s[\"a\"])(e.isContacted)})})),console.log(\"tableRefreshKey:\",this.tableRefreshKey),console.log(\"========================\")},handleDelete:function(e){var t=this,a=e?[e.demandId]:this.ids,i=e?'是否确认删除需求编号为\"'.concat(e.demandId,'\"的数据项？'):\"是否确认删除选中的\".concat(this.ids.length,\"条数据项？\");this.$modal.confirm(i).then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/demand/export\",Object(r[\"a\"])({},this.queryParams),\"需求数据_\".concat((new Date).getTime(),\".xlsx\"))},handleToggleTop:function(e){var t=this,a=\"1\"===e.isTop?\"取消置顶\":\"置顶\",i=\"1\"===e.isTop?\"0\":\"1\";this.$modal.confirm('确认要\"'+a+'\"需求\"'+e.demandTitle+'\"吗？').then((function(){var t={demandId:e.demandId,isTop:i};return f(t)})).then((function(){t.getList(),t.$modal.msgSuccess(a+\"成功\")})).catch((function(){}))},handleOffShelf:function(e){var t=this;this.$modal.confirm('确认要下架需求\"'+e.demandTitle+'\"吗？').then((function(){return h(e.demandId)})).then((function(){t.getList(),t.$modal.msgSuccess(\"下架成功\")})).catch((function(){}))},handleOnShelf:function(e){var t=this;this.$modal.confirm('确认要上架需求\"'+e.demandTitle+'\"吗？').then((function(){return g(e.demandId)})).then((function(){t.getList(),t.$modal.msgSuccess(\"上架成功\")})).catch((function(){}))},onCategoryChange:function(e){if(console.log(\"onCategoryChange - categoryId:\",e),this.form.dynamicData={},this.categoryFieldsData=[],!e)return this.dynamicFields=[],void(this.selectedCategoryName=\"\");var t=this.categoryList.find((function(t){return t.categoryId===e}));if(console.log(\"onCategoryChange - found category:\",t),t&&t.formFields)try{var a=JSON.parse(t.formFields);console.log(\"onCategoryChange - formConfig:\",a),Array.isArray(a)&&a.length>0&&a[0].fields?(this.processCategoryFieldsData(a),console.log(\"onCategoryChange - using new format, categoryFieldsData:\",this.categoryFieldsData)):(this.loadDynamicFields(e),console.log(\"onCategoryChange - using old format, dynamicFields:\",this.dynamicFields))}catch(i){console.error(\"解析表单配置失败:\",i),this.loadDynamicFields(e)}else console.log(\"onCategoryChange - no category or formFields found\"),this.dynamicFields=[],this.selectedCategoryName=\"\"},loadDynamicFields:function(e){var t=this;if(!e)return this.dynamicFields=[],void(this.selectedCategoryName=\"\");var a=this.categoryList.find((function(t){return t.categoryId===e}));if(a)if(this.selectedCategoryName=a.categoryName,a.formFields)try{var i=JSON.parse(a.formFields);this.dynamicFields=[],Array.isArray(i)&&i.length>0&&(i[0].fields?i.forEach((function(e){e.fields&&Array.isArray(e.fields)&&e.fields.forEach((function(a){\"static\"!==a.type&&a.name&&t.dynamicFields.push(Object(r[\"a\"])(Object(r[\"a\"])({},a),{},{moduleTitle:e.name}))}))})):this.dynamicFields=i),this.dynamicFields.forEach((function(e){if(e.name){if(\"checkbox\"===e.type)t.$set(t.form.dynamicData,e.name,Array.isArray(t.form.dynamicData[e.name])?t.form.dynamicData[e.name]:[]);else if(\"file\"===e.type){var a=t.form.dynamicData[e.name];if(\"string\"===typeof a&&\"\"!==a.trim()){var i=a.split(\"/\").pop()||\"下载文件\";t.$set(t.form.dynamicData,e.name,[{name:i,url:a}])}else Array.isArray(a)?t.$set(t.form.dynamicData,e.name,a):t.$set(t.form.dynamicData,e.name,[])}else\"number\"===e.type||\"date\"===e.type||\"time\"===e.type?t.$set(t.form.dynamicData,e.name,void 0!==t.form.dynamicData[e.name]?t.form.dynamicData[e.name]:null):t.$set(t.form.dynamicData,e.name,void 0!==t.form.dynamicData[e.name]?t.form.dynamicData[e.name]:\"\");if(e.required){var n=\"dynamicData.\".concat(e.name);t.$set(t.rules,n,[{required:!0,message:\"\".concat(e.label,\"不能为空\"),trigger:\"checkbox\"===e.type?\"change\":\"blur\"}])}}}))}catch(n){console.error(\"解析表单字段配置失败:\",n),this.dynamicFields=[]}else this.dynamicFields=[]},getFieldOptions:function(e){return e.options?e.options.split(\",\").map((function(e){return e.trim()})).filter((function(e){return e})):[]},handleFileSuccess:function(e,t,a,i){if(console.log(\"handleFileSuccess - response:\",e,\"file:\",t,\"field:\",i.name),200===e.code){var n=e.url||e.fileName||e.data;this.handleFieldInput(i,n),console.log(\"handleFileSuccess - 文件上传成功，设置URL:\",n),console.log(\"handleFileSuccess - field.value after update:\",i.value)}else this.$modal.msgError(e.msg||\"文件上传失败\")},handleFileRemove:function(e,t,a){this.handleFieldInput(a,\"\"),console.log(\"handleFileRemove - 文件已删除，清空字段值\")},getCheckboxValue:function(e){var t=this.form.dynamicData[e];return Array.isArray(t)?t:[]},updateCheckboxValue:function(e,t){this.$set(this.form.dynamicData,e,Array.isArray(t)?t:[])},getFileList:function(e){var t=this,a=e.value;return console.log(\"getFileList - field:\",e.name,\"value:\",a),\"string\"===typeof a&&\"\"!==a.trim()?[{name:this.getFileNameFromUrl(a),url:a,uid:\"\".concat(e.name,\"-0\"),status:\"success\"}]:Array.isArray(a)?a.map((function(a,i){return{name:a.name||t.getFileNameFromUrl(a.url||a),url:a.url||a,uid:\"\".concat(e.name,\"-\").concat(i),status:\"success\"}})):(console.log(\"getFileList - 无有效文件数据，返回空数组\"),[])},getUploadedFiles:function(e){var t=e.value;return Array.isArray(t)?t:[]},handleFilePreview:function(e){e.url&&window.open(e.url,\"_blank\")},downloadFile:function(e,t){var a=document.createElement(\"a\");a.href=e,a.download=t||\"下载文件\",a.target=\"_blank\",document.body.appendChild(a),a.click(),document.body.removeChild(a)},removeUploadedFile:function(e,t){if(e.value&&Array.isArray(e.value)){var a=Object(l[\"a\"])(e.value);a.splice(t,1),this.handleFieldInput(e,a)}},removeFileUrl:function(e){this.handleFieldInput(e,\"\")}},\"getFileNameFromUrl\",(function(e){if(!e)return\"未知文件\";var t=e.split(\"/\"),a=t[t.length-1],i=a.match(/.*_\\d+A\\d+\\.(.*)/);return i?\"文件.\".concat(i[1]):a||\"未知文件\"})),\"handleFieldInput\",(function(e,t){e.value=t,this.$set(this.form.dynamicData,e.name,t),console.log(\"handleFieldInput - field:\",e.name,\"value:\",t)})),\"updateFieldValue\",(function(e){this.$set(this.form.dynamicData,e.name,e.value)})),\"getCategoryName\",(function(){return this.categoryFieldsData&&this.categoryFieldsData.length>0?this.categoryFieldsData[0].name||\"专属字段\":this.selectedCategoryName||\"专属字段\"})),\"processCategoryFieldsData\",(function(e){var t=this;if(\"string\"===typeof e)try{this.categoryFieldsData=JSON.parse(e)}catch(a){console.error(\"解析分类字段数据失败:\",a),this.categoryFieldsData=[]}else Array.isArray(e)?this.categoryFieldsData=e:this.categoryFieldsData=[];this.categoryFieldsData.forEach((function(e){e.fields&&e.fields.forEach((function(e){void 0!==e.value&&null!==e.value||(\"file\"===e.type||\"checkbox\"===e.type?e.value=[]:e.value=\"\"),t.form.dynamicData&&void 0!==t.form.dynamicData[e.name]?e.value=t.form.dynamicData[e.name]:t.$set(t.form.dynamicData,e.name,e.value)}))}))})),\"testNewDataFormat\",(function(){}))},F=b,_=(a(\"e38a\"),a(\"3fd6\"),a(\"f74b\"),a(\"2877\")),k=Object(_[\"a\"])(F,i,n,!1,null,\"369868a7\",null);t[\"default\"]=k.exports},\"3fd6\":function(e,t,a){\"use strict\";a(\"16bb\")},\"85e3\":function(e,t,a){},\"8cd4\":function(e,t,a){\"use strict\";a.d(t,\"e\",(function(){return n})),a.d(t,\"c\",(function(){return o})),a.d(t,\"a\",(function(){return l})),a.d(t,\"f\",(function(){return s})),a.d(t,\"b\",(function(){return r})),a.d(t,\"d\",(function(){return c}));var i=a(\"b775\");function n(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/list\",method:\"post\",data:e})}function o(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/getInfo\",method:\"post\",data:e})}function l(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/add\",method:\"post\",data:e})}function s(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/edit\",method:\"post\",data:e})}function r(e){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/remove\",method:\"post\",data:e})}function c(){return Object(i[\"a\"])({url:\"/miniapp/demandcategory/app/getEnabledList\",method:\"post\"})}},d89b:function(e,t,a){},e38a:function(e,t,a){\"use strict\";a(\"d89b\")},f74b:function(e,t,a){\"use strict\";a(\"85e3\")}}]);", "extractedComments": []}