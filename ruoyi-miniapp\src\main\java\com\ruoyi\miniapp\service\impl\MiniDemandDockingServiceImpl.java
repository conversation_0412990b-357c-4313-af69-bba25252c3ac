package com.ruoyi.miniapp.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.miniapp.domain.MiniContact;
import com.ruoyi.miniapp.service.IMiniContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.miniapp.mapper.MiniDemandDockingMapper;
import com.ruoyi.miniapp.domain.MiniDemandDocking;
import com.ruoyi.miniapp.domain.MiniDemand;
import com.ruoyi.miniapp.service.IMiniDemandDockingService;
import com.ruoyi.miniapp.service.IMiniDemandService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 需求对接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class MiniDemandDockingServiceImpl implements IMiniDemandDockingService 
{
    private static final Logger logger = LoggerFactory.getLogger(MiniDemandDockingServiceImpl.class);

    @Autowired
    private MiniDemandDockingMapper miniDemandDockingMapper;

    @Autowired
    private IMiniDemandService miniDemandService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IMiniContactService iMiniContactService;

    /**
     * 查询需求对接
     * 
     * @param dockingId 需求对接主键
     * @return 需求对接
     */
    @Override
    public MiniDemandDocking selectMiniDemandDockingByDockingId(Long dockingId)
    {
        return miniDemandDockingMapper.selectMiniDemandDockingByDockingId(dockingId);
    }

    /**
     * 查询需求对接列表
     * 
     * @param miniDemandDocking 需求对接
     * @return 需求对接
     */
    @Override
    public List<MiniDemandDocking> selectMiniDemandDockingList(MiniDemandDocking miniDemandDocking)
    {
        return miniDemandDockingMapper.selectMiniDemandDockingList(miniDemandDocking);
    }

    /**
     * 新增需求对接
     * 
     * @param miniDemandDocking 需求对接
     * @return 结果
     */
    @Override
    public int insertMiniDemandDocking(MiniDemandDocking miniDemandDocking)
    {
        return miniDemandDockingMapper.insertMiniDemandDocking(miniDemandDocking);
    }

    /**
     * 修改需求对接
     * 
     * @param miniDemandDocking 需求对接
     * @return 结果
     */
    @Override
    public int updateMiniDemandDocking(MiniDemandDocking miniDemandDocking)
    {
        return miniDemandDockingMapper.updateMiniDemandDocking(miniDemandDocking);
    }

    /**
     * 批量删除需求对接
     * 
     * @param dockingIds 需要删除的需求对接主键
     * @return 结果
     */
    @Override
    public int deleteMiniDemandDockingByDockingIds(Long[] dockingIds)
    {
        return miniDemandDockingMapper.deleteMiniDemandDockingByDockingIds(dockingIds);
    }

    /**
     * 删除需求对接信息
     * 
     * @param dockingId 需求对接主键
     * @return 结果
     */
    @Override
    public int deleteMiniDemandDockingByDockingId(Long dockingId)
    {
        return miniDemandDockingMapper.deleteMiniDemandDockingByDockingId(dockingId);
    }

    /**
     * 用户对接需求（我要对接）
     * 
     * @param demandId 需求ID
     * @param userId 用户ID
     * @return 对接结果，包含联系方式信息
     */
    @Override
    @Transactional
    public Map<String, Object> dockDemand(Long demandId, Long userId)
    {
        try
        {
            // 检查参数
            if (demandId == null || userId == null)
            {
                throw new ServiceException("参数不能为空");
            }

            // 检查需求是否存在
            MiniDemand demand = miniDemandService.selectMiniDemandByDemandId(demandId);
            if (demand == null)
            {
                throw new ServiceException("需求不存在");
            }

            // 检查需求状态
            if (!"0".equals(demand.getDemandStatus()))
            {
                throw new ServiceException("该需求已下架，无法对接");
            }

            // 检查是否为需求发布者本人
            if (userId.equals(demand.getUserId()))
            {
                throw new ServiceException("不能对接自己发布的需求");
            }

            // 检查用户信息
            SysUser user = userService.selectUserById(userId);
            if (user == null || !"01".equals(user.getUserType()))
            {
                throw new ServiceException("用户信息不存在或非小程序用户");
            }

            // 检查是否已经对接过
            MiniDemandDocking existingDocking = miniDemandDockingMapper.selectDockingRecord(demandId, userId);
            if (existingDocking != null && "0".equals(existingDocking.getStatus()))
            {
                // 已经对接过，确保需求状态为已对接，然后返回联系方式
                // updateDemandStatusToDocked(demandId);

                Map<String, String> contactInfo = getContactInfo(demand);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("contactName", contactInfo.get("contactName"));
                resultMap.put("contactPhone", contactInfo.get("contactPhone"));
                resultMap.put("contactSource", contactInfo.get("contactSource"));
                resultMap.put("qrCodeUrl", contactInfo.get("qrCodeUrl"));
                resultMap.put("dockingId", existingDocking.getDockingId());

                logger.info("用户{}重复对接需求{}，确保需求状态为已对接并返回联系方式", userId, demandId);
                return resultMap;
            }

            // 获取联系方式
            Map<String, String> contactInfo = getContactInfo(demand);
            String contactName = contactInfo.get("contactName");
            String contactPhone = contactInfo.get("contactPhone");
            String contactSource = contactInfo.get("contactSource");
            String qrCodeUrl = contactInfo.get("qrCodeUrl");

            // 创建对接记录
            MiniDemandDocking docking = new MiniDemandDocking();
            docking.setDemandId(demandId);
            docking.setUserId(userId);
            docking.setUserName(StringUtils.isNotEmpty(user.getRealName()) ? user.getRealName() : user.getNickName());
            docking.setUserPhone(user.getPhonenumber());
            docking.setUserCompany(user.getCurrentCompany());
            docking.setUserPosition(user.getPositionTitle());
            docking.setDockingTime(new Date());
            docking.setContactName(contactName);
            docking.setContactPhone(contactPhone);
            docking.setContactSource(contactSource);
            docking.setQrCodeUrl(qrCodeUrl);
            docking.setStatus("0");
            docking.setCreateBy(String.valueOf(userId));
            docking.setCreateTime(new Date());

            // 如果之前有取消的记录，更新状态；否则新增记录
            int result;
            if (existingDocking != null)
            {
                docking.setDockingId(existingDocking.getDockingId());
                result = miniDemandDockingMapper.updateMiniDemandDocking(docking);
            }
            else
            {
                result = miniDemandDockingMapper.insertMiniDemandDocking(docking);
            }

            if (result > 0)
            {
                // 立即更新需求状态为"已对接",不需要了，改为自己修改状态
                // updateDemandStatusToDocked(demandId);

                // 构建返回结果
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("contactName", contactName);
                resultMap.put("contactPhone", contactPhone);
                resultMap.put("contactSource", contactSource);
                resultMap.put("qrCodeUrl", qrCodeUrl);
                resultMap.put("dockingId", docking.getDockingId());

                logger.info("用户{}成功获取需求{}的联系方式", userId, demandId);
                return resultMap;
            }
            else
            {
                throw new ServiceException("对接失败");
            }
        }
        catch (ServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            logger.error("需求对接失败", e);
            throw new ServiceException("需求对接失败：" + e.getMessage());
        }
    }

    /**
     * 获取联系方式信息
     */
    private Map<String, String> getContactInfo(MiniDemand demand)
    {
        Map<String, String> contactInfo = new HashMap<>();

        // 检查是否使用后台配置的联系方式
        String useAdminContact = configService.selectConfigByKey("demand.contact.use.admin");
        boolean useAdmin = "true".equalsIgnoreCase(useAdminContact);

        if (useAdmin)
        {
            // 使用后台配置的联系方式，通过ContactCode获取
            MiniContact officialContact = getContactByCode("DEFAULT");

            if (officialContact != null)
            {
                // 从联系人管理中获取数据
                contactInfo.put("contactName", StringUtils.isNotEmpty(officialContact.getContactName()) ? officialContact.getContactName() : "客服");
                contactInfo.put("contactPhone", StringUtils.isNotEmpty(officialContact.getContactPhone()) ? officialContact.getContactPhone() : "");
                contactInfo.put("qrCodeUrl", StringUtils.isNotEmpty(officialContact.getQrCodeUrl()) ? officialContact.getQrCodeUrl() : "");
                contactInfo.put("contactSource", "0"); // 后台配置
            }
            else
            {
                // 如果联系人管理中没有数据，回退到系统配置
                String adminName = configService.selectConfigByKey("demand.contact.admin.name");
                String adminPhone = configService.selectConfigByKey("demand.contact.admin.phone");
                contactInfo.put("contactName", StringUtils.isNotEmpty(adminName) ? adminName : "客服");
                contactInfo.put("contactPhone", StringUtils.isNotEmpty(adminPhone) ? adminPhone : "");
                contactInfo.put("qrCodeUrl", ""); // 系统配置没有二维码
                contactInfo.put("contactSource", "0"); // 后台配置

                logger.warn("未找到ContactCode为DEFAULT的联系人，使用系统配置作为回退方案");
            }
        }
        else
        {
            // 使用需求发布人的联系方式
            contactInfo.put("contactName", StringUtils.isNotEmpty(demand.getContactName()) ? demand.getContactName() : "");
            contactInfo.put("contactPhone", StringUtils.isNotEmpty(demand.getContactPhone()) ? demand.getContactPhone() : "");
            contactInfo.put("qrCodeUrl", ""); // 需求发布人没有二维码
            contactInfo.put("contactSource", "1"); // 需求发布人
        }

        return contactInfo;
    }

    /**
     * 根据ContactCode获取联系方式数据
     *
     * @param contactCode 联系人编码
     * @return 联系人信息
     */
    private MiniContact getContactByCode(String contactCode)
    {
        try
        {
            return iMiniContactService.selectMiniContactByContactCode(contactCode);
        }
        catch (Exception e)
        {
            logger.error("根据ContactCode获取联系方式失败: {}", contactCode, e);
            return null;
        }
    }

    /**
     * 检查用户是否已对接某需求
     * 
     * @param demandId 需求ID
     * @param userId 用户ID
     * @return 是否已对接
     */
    @Override
    public boolean isDocked(Long demandId, Long userId)
    {
        if (demandId == null || userId == null)
        {
            return false;
        }

        MiniDemandDocking docking = miniDemandDockingMapper.selectDockingRecord(demandId, userId);
        return docking != null && "0".equals(docking.getStatus());
    }

    /**
     * 获取用户的对接列表（我的对接）
     * 
     * @param userId 用户ID
     * @return 对接列表
     */
    @Override
    public List<MiniDemandDocking> getMyDockingList(Long userId)
    {
        return miniDemandDockingMapper.selectMyDockingList(userId);
    }

    /**
     * 获取需求的对接列表（后台管理用）
     * 
     * @param demandId 需求ID
     * @return 对接列表
     */
    @Override
    public List<MiniDemandDocking> getDockingListByDemandId(Long demandId)
    {
        return miniDemandDockingMapper.selectDockingListByDemandId(demandId);
    }

    /**
     * 获取需求的对接详情列表（包含用户信息）
     * 
     * @param demandId 需求ID
     * @return 对接详情列表
     */
    @Override
    public List<MiniDemandDocking> getDockingDetailListByDemandId(Long demandId)
    {
        return miniDemandDockingMapper.selectDockingDetailListByDemandId(demandId);
    }

    /**
     * 获取需求对接统计信息
     * 
     * @param demandId 需求ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getDockingStats(Long demandId)
    {
        Map<String, Object> stats = new HashMap<>();
        
        int dockingCount = miniDemandDockingMapper.countDockingByDemandId(demandId);
        stats.put("dockingCount", dockingCount);
        
        return stats;
    }



    /**
     * 批量查询用户对接状态
     * 
     * @param userId 用户ID
     * @param demandIds 需求ID列表
     * @return 对接状态列表
     */
    @Override
    public List<MiniDemandDocking> batchGetDockingStatus(Long userId, List<Long> demandIds)
    {
        if (userId == null || demandIds == null || demandIds.isEmpty())
        {
            return null;
        }
        return miniDemandDockingMapper.batchSelectDockingStatus(userId, demandIds);
    }

    /**
     * 更新需求状态为已对接
     *
     * @param demandId 需求ID
     */
    private void updateDemandStatusToDocked(Long demandId)
    {
        try
        {
            MiniDemand demand = new MiniDemand();
            demand.setDemandId(demandId);
            demand.setDemandStatus("1"); // 1表示已对接
            demand.setUpdateTime(new Date());

            int updateResult = miniDemandService.updateMiniDemand(demand);
            if (updateResult > 0)
            {
                logger.info("需求{}状态已更新为已对接", demandId);
            }
            else
            {
                logger.warn("需求{}状态更新失败", demandId);
            }
        }
        catch (Exception e)
        {
            logger.error("更新需求{}状态为已对接时发生异常", demandId, e);
            // 不抛出异常，避免影响对接流程
        }
    }



}
