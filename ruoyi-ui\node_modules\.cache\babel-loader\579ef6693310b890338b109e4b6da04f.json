{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\activity-config\\index.vue", "mtime": 1753944331124}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_activityConfig", "require", "name", "data", "loading", "formConfigOpen", "previewDialogVisible", "formFields", "activityId", "created", "loadFormConfig", "methods", "_this", "getActivityConfig", "then", "response", "formConfig", "JSON", "parse", "e", "catch", "handleFormConfig", "addFormField", "defaultName", "generateUniqueFieldName", "push", "label", "type", "required", "options", "removeFormField", "index", "splice", "moveField", "direction", "newIndex", "length", "temp", "$set", "updateFieldName", "field", "pinyin", "baseName", "toLowerCase", "replace", "uniqueName", "counter", "isFieldNameExists", "concat", "some", "getFieldIcon", "icons", "input", "textarea", "number", "email", "tel", "radio", "checkbox", "select", "radio_other", "checkbox_other", "select_other", "date", "file", "handleTemplateCommand", "command", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "$message", "success", "templates", "basic", "roadshow", "templateFields", "map", "_objectSpread2", "default", "previewForm", "warning", "saveFormConfig", "_this3", "fieldNames", "i", "error", "includes", "configData", "stringify", "updateData", "updateActivityConfig", "$modal", "msgSuccess", "getFieldTypeName", "typeNames"], "sources": ["src/views/miniapp/xiqing/activity-config/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-config-container\">\r\n      <div class=\"config-header\">\r\n        <h3>路演活动报名表单配置</h3>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-setting\"\r\n          @click=\"handleFormConfig\"\r\n          v-hasPermi=\"['miniapp:xiqing:activity:edit']\"\r\n        >配置表单</el-button>\r\n      </div>\r\n\r\n      <div class=\"config-content\" v-loading=\"loading\">\r\n        <div v-if=\"formFields.length > 0\" class=\"form-preview\">\r\n          <h4>当前表单字段预览：</h4>\r\n          <div class=\"field-list\">\r\n            <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"field-item\">\r\n              <div class=\"field-info\">\r\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                <span class=\"field-label\">{{ field.label }}</span>\r\n                <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\r\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n                <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-else class=\"empty-form\">\r\n          <i class=\"el-icon-document-add\"></i>\r\n          <p>暂未配置表单字段，点击\"配置表单\"开始设置</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 表单配置对话框 -->\r\n    <el-dialog title=\"报名表单配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\r\n      <div class=\"form-fields-config\">\r\n        <!-- 工具栏 -->\r\n        <div class=\"form-fields-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\r\n              添加字段\r\n            </el-button>\r\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\r\n              <el-button size=\"small\">\r\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"roadshow\">路演报名模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <el-button size=\"small\" @click=\"previewForm\" icon=\"el-icon-view\">\r\n              预览表单\r\n            </el-button>\r\n            <el-button type=\"success\" size=\"small\" @click=\"saveFormConfig\" icon=\"el-icon-check\">\r\n              保存配置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 字段配置区域 -->\r\n        <div class=\"form-fields-list\" v-if=\"formFields.length > 0\">\r\n          <div\r\n            v-for=\"(field, index) in formFields\"\r\n            :key=\"index\"\r\n            class=\"form-field-item\"\r\n          >\r\n            <div class=\"field-header\">\r\n              <div class=\"field-info\">\r\n                <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                <span class=\"field-label\">{{ field.label || '未命名字段' }}</span>\r\n                <el-tag size=\"mini\" type=\"success\">{{ field.name || 'unnamed' }}</el-tag>\r\n                <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n              </div>\r\n              <div class=\"field-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"moveField(index, -1)\"\r\n                  :disabled=\"index === 0\"\r\n                  icon=\"el-icon-arrow-up\"\r\n                ></el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"moveField(index, 1)\"\r\n                  :disabled=\"index === formFields.length - 1\"\r\n                  icon=\"el-icon-arrow-down\"\r\n                ></el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  @click=\"removeFormField(index)\"\r\n                  icon=\"el-icon-delete\"\r\n                  class=\"danger-btn\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"field-content\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段标签</label>\r\n                    <el-input\r\n                      v-model=\"field.label\"\r\n                      placeholder=\"显示给用户的标签，如：姓名、电话等\"\r\n                      size=\"small\"\r\n                      @input=\"updateFieldName(field, $event)\"\r\n                    />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段名称 (唯一标识)</label>\r\n                    <el-input\r\n                      v-model=\"field.name\"\r\n                      placeholder=\"字段的唯一标识符\"\r\n                      size=\"small\"\r\n                      readonly\r\n                      style=\"background-color: #f5f7fa;\"\r\n                    />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <div class=\"field-item\">\r\n                    <label>字段类型</label>\r\n                    <el-select v-model=\"field.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\r\n                      <el-option label=\"📝 文本输入\" value=\"input\" />\r\n                      <el-option label=\"📄 多行文本\" value=\"textarea\" />\r\n                      <el-option label=\"🔢 数字输入\" value=\"number\" />\r\n                      <el-option label=\"📧 邮箱\" value=\"email\" />\r\n                      <el-option label=\"📞 电话\" value=\"tel\" />\r\n                      <el-option label=\"🔘 单选\" value=\"radio\" />\r\n                      <el-option label=\"☑️ 多选\" value=\"checkbox\" />\r\n                      <el-option label=\"📋 下拉选择\" value=\"select\" />\r\n                      <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\r\n                      <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\r\n                      <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\r\n                      <el-option label=\"📅 日期\" value=\"date\" />\r\n                      <el-option label=\"📎 文件上传\" value=\"file\" />\r\n                    </el-select>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n                <el-col :span=\"4\">\r\n                  <div class=\"field-item\">\r\n                    <label>是否必填</label>\r\n                    <el-switch v-model=\"field.required\" />\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"20\" v-if=\"['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type)\">\r\n                  <div class=\"field-item\">\r\n                    <label>选项配置</label>\r\n                    <el-input\r\n                      v-model=\"field.options\"\r\n                      placeholder=\"用逗号分隔选项，如：选项1,选项2,选项3\"\r\n                      size=\"small\"\r\n                    />\r\n                    <div class=\"options-preview\" v-if=\"field.options\">\r\n                      <el-tag\r\n                        v-for=\"(option, optIndex) in field.options.split(',')\"\r\n                        :key=\"optIndex\"\r\n                        size=\"mini\"\r\n                        style=\"margin-right: 5px; margin-top: 5px;\"\r\n                      >\r\n                        {{ option.trim() }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div class=\"empty-state\" v-else>\r\n          <i class=\"el-icon-document-add\"></i>\r\n          <p>暂无表单字段，点击\"添加字段\"开始配置</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"formConfigOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"form-preview\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ currentActivity ? currentActivity.activityTitle : '路演报名表' }}</h3>\r\n          <p>请填写以下信息完成报名</p>\r\n        </div>\r\n        <div class=\"preview-form\">\r\n          <div v-for=\"(field, index) in formFields\" :key=\"index\" class=\"preview-field\">\r\n            <label class=\"preview-label\">\r\n              {{ field.label }}\r\n              <span v-if=\"field.required\" class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"preview-input\">\r\n              <el-input\r\n                v-if=\"field.type === 'input' || field.type === 'email' || field.type === 'tel'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input\r\n                v-else-if=\"field.type === 'textarea'\"\r\n                type=\"textarea\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input-number\r\n                v-else-if=\"field.type === 'number'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n                style=\"width: 100%\"\r\n              />\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\r\n                <el-radio\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                >\r\n                  {{ option.trim() }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\r\n                <el-checkbox\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                >\r\n                  {{ option.trim() }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"(option, optIndex) in (field.options || '').split(',')\"\r\n                  :key=\"optIndex\"\r\n                  :label=\"option.trim()\"\r\n                  :value=\"option.trim()\"\r\n                />\r\n              </el-select>\r\n              <el-date-picker\r\n                v-else-if=\"field.type === 'date'\"\r\n                type=\"date\"\r\n                :placeholder=\"'请选择' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n                style=\"width: 100%\"\r\n              />\r\n              <el-upload\r\n                v-else-if=\"field.type === 'file'\"\r\n                class=\"upload-demo\"\r\n                action=\"#\"\r\n                :disabled=\"true\"\r\n                :show-file-list=\"false\"\r\n              >\r\n                <el-button size=\"small\" type=\"primary\" disabled>\r\n                  <i class=\"el-icon-upload\"></i> 选择文件\r\n                </el-button>\r\n                <div slot=\"tip\" class=\"el-upload__tip\">支持上传PDF、Word、Excel等格式文件</div>\r\n              </el-upload>\r\n              <!-- 单选+其他 -->\r\n              <div v-else-if=\"field.type === 'radio_other'\">\r\n                <el-radio-group disabled>\r\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-radio :label=\"option.trim()\">{{ option.trim() }}</el-radio>\r\n                  </div>\r\n                  <div style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-radio label=\"其他\">\r\n                      其他\r\n                      <el-input\r\n                        placeholder=\"请输入其他内容\"\r\n                        size=\"small\"\r\n                        disabled\r\n                        style=\"width: 200px; margin-left: 10px;\"\r\n                      />\r\n                    </el-radio>\r\n                  </div>\r\n                </el-radio-group>\r\n              </div>\r\n              <!-- 多选+其他 -->\r\n              <div v-else-if=\"field.type === 'checkbox_other'\">\r\n                <el-checkbox-group disabled>\r\n                  <div v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-checkbox :label=\"option.trim()\">{{ option.trim() }}</el-checkbox>\r\n                  </div>\r\n                  <div style=\"display: block; margin-bottom: 8px;\">\r\n                    <el-checkbox label=\"其他\">\r\n                      其他\r\n                      <el-input\r\n                        placeholder=\"请输入其他内容\"\r\n                        size=\"small\"\r\n                        disabled\r\n                        style=\"width: 200px; margin-left: 10px;\"\r\n                      />\r\n                    </el-checkbox>\r\n                  </div>\r\n                </el-checkbox-group>\r\n              </div>\r\n              <!-- 下拉+其他 -->\r\n              <div v-else-if=\"field.type === 'select_other'\">\r\n                <el-select disabled placeholder=\"请选择\" size=\"small\" style=\"width: 100%; margin-bottom: 8px;\">\r\n                  <el-option v-for=\"(option, optIndex) in field.options.split(',')\" :key=\"optIndex\" :label=\"option.trim()\" :value=\"option.trim()\" />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n                <el-input\r\n                  placeholder=\"选择'其他'时请在此输入具体内容\"\r\n                  size=\"small\"\r\n                  disabled\r\n                  style=\"width: 100%;\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityConfig, updateActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\r\n\r\nexport default {\r\n  name: \"XiqingActivityConfig\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 是否显示表单配置弹出层\r\n      formConfigOpen: false,\r\n      // 是否显示表单预览弹出层\r\n      previewDialogVisible: false,\r\n      // 表单字段配置\r\n      formFields: [],\r\n      // 活动ID（固定为1，因为只有一个路演活动配置）\r\n      activityId: 1\r\n    };\r\n  },\r\n  created() {\r\n    this.loadFormConfig();\r\n  },\r\n  methods: {\r\n    /** 加载表单配置 */\r\n    loadFormConfig() {\r\n      this.loading = true;\r\n      getActivityConfig(this.activityId).then(response => {\r\n        if (response.data && response.data.formConfig) {\r\n          try {\r\n            this.formFields = JSON.parse(response.data.formConfig);\r\n          } catch (e) {\r\n            this.formFields = [];\r\n          }\r\n        } else {\r\n          this.formFields = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.formFields = [];\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig() {\r\n      this.formConfigOpen = true;\r\n    },\r\n    /** 添加表单字段 */\r\n    addFormField() {\r\n      // 生成默认的唯一字段名\r\n      const defaultName = this.generateUniqueFieldName('field');\r\n\r\n      this.formFields.push({\r\n        name: defaultName,\r\n        label: '',\r\n        type: 'input',\r\n        required: false,\r\n        options: ''\r\n      });\r\n    },\r\n    /** 删除表单字段 */\r\n    removeFormField(index) {\r\n      this.formFields.splice(index, 1);\r\n    },\r\n    /** 移动字段位置 */\r\n    moveField(index, direction) {\r\n      const newIndex = index + direction;\r\n      if (newIndex >= 0 && newIndex < this.formFields.length) {\r\n        const temp = this.formFields[index];\r\n        this.$set(this.formFields, index, this.formFields[newIndex]);\r\n        this.$set(this.formFields, newIndex, temp);\r\n      }\r\n    },\r\n    /** 更新字段名称 */\r\n    updateFieldName(field, label) {\r\n      if (!field.name || field.name === '') {\r\n        field.name = this.generateUniqueFieldName(label);\r\n      }\r\n    },\r\n    /** 生成唯一字段名称 */\r\n    generateUniqueFieldName(label) {\r\n      const pinyin = {\r\n        '姓名': 'name',\r\n        '联系电话': 'phone',\r\n        '电话': 'phone',\r\n        '邮箱': 'email',\r\n        '邮箱地址': 'email',\r\n        '公司': 'company',\r\n        '项目名称': 'project_name',\r\n        '项目描述': 'project_description',\r\n        '团队规模': 'team_size'\r\n      };\r\n\r\n      // 生成基础名称\r\n      let baseName = pinyin[label] || label.toLowerCase().replace(/[\\s\\u4e00-\\u9fa5]+/g, '_').replace(/[^\\w_]/g, '');\r\n\r\n      // 确保名称不为空\r\n      if (!baseName) {\r\n        baseName = 'field';\r\n      }\r\n\r\n      // 检查名称是否已存在，如果存在则添加数字后缀\r\n      let uniqueName = baseName;\r\n      let counter = 1;\r\n\r\n      while (this.isFieldNameExists(uniqueName)) {\r\n        uniqueName = `${baseName}_${counter}`;\r\n        counter++;\r\n      }\r\n\r\n      return uniqueName;\r\n    },\r\n    /** 检查字段名称是否已存在 */\r\n    isFieldNameExists(name) {\r\n      return this.formFields.some(field => field.name === name);\r\n    },\r\n    /** 获取字段图标 */\r\n    getFieldIcon(type) {\r\n      const icons = {\r\n        input: 'el-icon-edit',\r\n        textarea: 'el-icon-document',\r\n        number: 'el-icon-s-data',\r\n        email: 'el-icon-message',\r\n        tel: 'el-icon-phone',\r\n        radio: 'el-icon-success',\r\n        checkbox: 'el-icon-check',\r\n        select: 'el-icon-arrow-down',\r\n        radio_other: 'el-icon-circle-plus',\r\n        checkbox_other: 'el-icon-square-plus',\r\n        select_other: 'el-icon-plus',\r\n        date: 'el-icon-date',\r\n        file: 'el-icon-upload'\r\n      };\r\n      return icons[type] || 'el-icon-edit';\r\n    },\r\n    /** 处理模板命令 */\r\n    handleTemplateCommand(command) {\r\n      if (command === 'clear') {\r\n        this.$confirm('确定要清空所有字段吗？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.formFields = [];\r\n          this.$message.success('已清空所有字段');\r\n        });\r\n        return;\r\n      }\r\n\r\n      const templates = {\r\n        basic: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' }\r\n        ],\r\n        roadshow: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\r\n          { label: '公司/团队', name: '', type: 'input', required: true, options: '' },\r\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\r\n          { label: '项目来源', name: '', type: 'radio_other', required: true, options: '社会,高校,科研院所,企业内部' },\r\n          { label: '项目阶段', name: '', type: 'radio', required: true, options: '概念阶段,开发阶段,测试阶段,上线阶段' },\r\n          { label: '团队规模', name: '', type: 'select', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\r\n          { label: '融资需求', name: '', type: 'input', required: false, options: '' },\r\n          { label: '商业计划书', name: '', type: 'file', required: true, options: '' },\r\n          { label: '项目描述', name: '', type: 'textarea', required: true, options: '' }\r\n        ]\r\n      };\r\n\r\n      if (templates[command]) {\r\n        // 清空现有字段\r\n        this.formFields = [];\r\n\r\n        // 为每个模板字段生成唯一的name\r\n        const templateFields = templates[command].map(field => ({\r\n          ...field,\r\n          name: this.generateUniqueFieldName(field.label)\r\n        }));\r\n\r\n        this.formFields = templateFields;\r\n        this.$message.success('模板应用成功');\r\n      }\r\n    },\r\n    /** 预览表单 */\r\n    previewForm() {\r\n      if (this.formFields.length === 0) {\r\n        this.$message.warning('请先添加表单字段');\r\n        return;\r\n      }\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (this.formFields.length === 0) {\r\n        this.$message.warning('请至少添加一个表单字段');\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置\r\n      const fieldNames = [];\r\n      for (let i = 0; i < this.formFields.length; i++) {\r\n        const field = this.formFields[i];\r\n\r\n        // 验证标签不能为空\r\n        if (!field.label) {\r\n          this.$message.error(`第${i + 1}个字段的标签不能为空`);\r\n          return;\r\n        }\r\n\r\n        // 验证字段名称不能为空\r\n        if (!field.name) {\r\n          this.$message.error(`第${i + 1}个字段的名称不能为空`);\r\n          return;\r\n        }\r\n\r\n        // 验证字段名称唯一性\r\n        if (fieldNames.includes(field.name)) {\r\n          this.$message.error(`字段名称\"${field.name}\"重复，请确保每个字段名称唯一`);\r\n          return;\r\n        }\r\n        fieldNames.push(field.name);\r\n\r\n        // 验证选项配置\r\n        if (['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other'].includes(field.type) && !field.options) {\r\n          this.$message.error(`字段\"${field.label}\"需要配置选项`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const configData = JSON.stringify(this.formFields);\r\n      const updateData = {\r\n        activityId: this.activityId,\r\n        formConfig: configData\r\n      };\r\n\r\n      updateActivityConfig(updateData).then(() => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.loadFormConfig();\r\n      });\r\n    },\r\n    /** 获取字段类型名称 */\r\n    getFieldTypeName(type) {\r\n      const typeNames = {\r\n        input: '文本输入',\r\n        textarea: '多行文本',\r\n        number: '数字输入',\r\n        email: '邮箱',\r\n        tel: '电话',\r\n        radio: '单选',\r\n        checkbox: '多选',\r\n        select: '下拉选择',\r\n        radio_other: '单选+其他',\r\n        checkbox_other: '多选+其他',\r\n        select_other: '下拉+其他',\r\n        date: '日期',\r\n        file: '文件上传'\r\n      };\r\n      return typeNames[type] || '未知类型';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  max-width: 800px;\r\n  margin: 20px auto;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 300px;\r\n}\r\n\r\n.form-preview h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n  min-width: 100px;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n}\r\n\r\n.empty-form {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.form-fields-config {\r\n  min-height: 400px;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.toolbar-left, .toolbar-right {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.form-fields-list {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-field-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 15px;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.form-field-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.field-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.field-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.danger-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.field-content {\r\n  padding: 16px;\r\n}\r\n\r\n.field-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.field-item label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.options-preview {\r\n  margin-top: 5px;\r\n}\r\n\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.form-preview {\r\n  padding: 20px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #303133;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 2px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAuVA,IAAAA,eAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,cAAA;MACA;MACAC,oBAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,aACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,iCAAA,OAAAL,UAAA,EAAAM,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAZ,IAAA,IAAAY,QAAA,CAAAZ,IAAA,CAAAa,UAAA;UACA;YACAJ,KAAA,CAAAL,UAAA,GAAAU,IAAA,CAAAC,KAAA,CAAAH,QAAA,CAAAZ,IAAA,CAAAa,UAAA;UACA,SAAAG,CAAA;YACAP,KAAA,CAAAL,UAAA;UACA;QACA;UACAK,KAAA,CAAAL,UAAA;QACA;QACAK,KAAA,CAAAR,OAAA;MACA,GAAAgB,KAAA;QACAR,KAAA,CAAAL,UAAA;QACAK,KAAA,CAAAR,OAAA;MACA;IACA;IACA,eACAiB,gBAAA,WAAAA,iBAAA;MACA,KAAAhB,cAAA;IACA;IACA,aACAiB,YAAA,WAAAA,aAAA;MACA;MACA,IAAAC,WAAA,QAAAC,uBAAA;MAEA,KAAAjB,UAAA,CAAAkB,IAAA;QACAvB,IAAA,EAAAqB,WAAA;QACAG,KAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;MACA;IACA;IACA,aACAC,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAAxB,UAAA,CAAAyB,MAAA,CAAAD,KAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAF,KAAA,EAAAG,SAAA;MACA,IAAAC,QAAA,GAAAJ,KAAA,GAAAG,SAAA;MACA,IAAAC,QAAA,SAAAA,QAAA,QAAA5B,UAAA,CAAA6B,MAAA;QACA,IAAAC,IAAA,QAAA9B,UAAA,CAAAwB,KAAA;QACA,KAAAO,IAAA,MAAA/B,UAAA,EAAAwB,KAAA,OAAAxB,UAAA,CAAA4B,QAAA;QACA,KAAAG,IAAA,MAAA/B,UAAA,EAAA4B,QAAA,EAAAE,IAAA;MACA;IACA;IACA,aACAE,eAAA,WAAAA,gBAAAC,KAAA,EAAAd,KAAA;MACA,KAAAc,KAAA,CAAAtC,IAAA,IAAAsC,KAAA,CAAAtC,IAAA;QACAsC,KAAA,CAAAtC,IAAA,QAAAsB,uBAAA,CAAAE,KAAA;MACA;IACA;IACA,eACAF,uBAAA,WAAAA,wBAAAE,KAAA;MACA,IAAAe,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA,IAAAC,QAAA,GAAAD,MAAA,CAAAf,KAAA,KAAAA,KAAA,CAAAiB,WAAA,GAAAC,OAAA,6BAAAA,OAAA;;MAEA;MACA,KAAAF,QAAA;QACAA,QAAA;MACA;;MAEA;MACA,IAAAG,UAAA,GAAAH,QAAA;MACA,IAAAI,OAAA;MAEA,YAAAC,iBAAA,CAAAF,UAAA;QACAA,UAAA,MAAAG,MAAA,CAAAN,QAAA,OAAAM,MAAA,CAAAF,OAAA;QACAA,OAAA;MACA;MAEA,OAAAD,UAAA;IACA;IACA,kBACAE,iBAAA,WAAAA,kBAAA7C,IAAA;MACA,YAAAK,UAAA,CAAA0C,IAAA,WAAAT,KAAA;QAAA,OAAAA,KAAA,CAAAtC,IAAA,KAAAA,IAAA;MAAA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAvB,IAAA;MACA,IAAAwB,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACA,OAAAb,KAAA,CAAAxB,IAAA;IACA;IACA,aACAsC,qBAAA,WAAAA,sBAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAAE,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACA3C,IAAA;QACA,GAAAb,IAAA;UACAqD,MAAA,CAAA5D,UAAA;UACA4D,MAAA,CAAAI,QAAA,CAAAC,OAAA;QACA;QACA;MACA;MAEA,IAAAC,SAAA;QACAC,KAAA,GACA;UAAAhD,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,EACA;QACA8C,QAAA,GACA;UAAAjD,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,KAAA;UAAAxB,IAAA;UAAAyB,IAAA;UAAAC,QAAA;UAAAC,OAAA;QAAA;MAEA;MAEA,IAAA4C,SAAA,CAAAP,OAAA;QACA;QACA,KAAA3D,UAAA;;QAEA;QACA,IAAAqE,cAAA,GAAAH,SAAA,CAAAP,OAAA,EAAAW,GAAA,WAAArC,KAAA;UAAA,WAAAsC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAvC,KAAA;YACAtC,IAAA,EAAAiE,MAAA,CAAA3C,uBAAA,CAAAgB,KAAA,CAAAd,KAAA;UAAA;QAAA,CACA;QAEA,KAAAnB,UAAA,GAAAqE,cAAA;QACA,KAAAL,QAAA,CAAAC,OAAA;MACA;IACA;IACA,WACAQ,WAAA,WAAAA,YAAA;MACA,SAAAzE,UAAA,CAAA6B,MAAA;QACA,KAAAmC,QAAA,CAAAU,OAAA;QACA;MACA;MACA,KAAA3E,oBAAA;IACA;IACA,aACA4E,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,SAAA5E,UAAA,CAAA6B,MAAA;QACA,KAAAmC,QAAA,CAAAU,OAAA;QACA;MACA;;MAEA;MACA,IAAAG,UAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAA9E,UAAA,CAAA6B,MAAA,EAAAiD,CAAA;QACA,IAAA7C,KAAA,QAAAjC,UAAA,CAAA8E,CAAA;;QAEA;QACA,KAAA7C,KAAA,CAAAd,KAAA;UACA,KAAA6C,QAAA,CAAAe,KAAA,UAAAtC,MAAA,CAAAqC,CAAA;UACA;QACA;;QAEA;QACA,KAAA7C,KAAA,CAAAtC,IAAA;UACA,KAAAqE,QAAA,CAAAe,KAAA,UAAAtC,MAAA,CAAAqC,CAAA;UACA;QACA;;QAEA;QACA,IAAAD,UAAA,CAAAG,QAAA,CAAA/C,KAAA,CAAAtC,IAAA;UACA,KAAAqE,QAAA,CAAAe,KAAA,8BAAAtC,MAAA,CAAAR,KAAA,CAAAtC,IAAA;UACA;QACA;QACAkF,UAAA,CAAA3D,IAAA,CAAAe,KAAA,CAAAtC,IAAA;;QAEA;QACA,qFAAAqF,QAAA,CAAA/C,KAAA,CAAAb,IAAA,MAAAa,KAAA,CAAAX,OAAA;UACA,KAAA0C,QAAA,CAAAe,KAAA,kBAAAtC,MAAA,CAAAR,KAAA,CAAAd,KAAA;UACA;QACA;MACA;MAEA,IAAA8D,UAAA,GAAAvE,IAAA,CAAAwE,SAAA,MAAAlF,UAAA;MACA,IAAAmF,UAAA;QACAlF,UAAA,OAAAA,UAAA;QACAQ,UAAA,EAAAwE;MACA;MAEA,IAAAG,oCAAA,EAAAD,UAAA,EAAA5E,IAAA;QACAqE,MAAA,CAAAS,MAAA,CAAAC,UAAA;QACAV,MAAA,CAAA9E,cAAA;QACA8E,MAAA,CAAAzE,cAAA;MACA;IACA;IACA,eACAoF,gBAAA,WAAAA,iBAAAnE,IAAA;MACA,IAAAoE,SAAA;QACA3C,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACA,OAAA+B,SAAA,CAAApE,IAAA;IACA;EACA;AACA", "ignoreList": []}]}