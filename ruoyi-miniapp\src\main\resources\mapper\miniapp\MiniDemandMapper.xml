<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniDemandMapper">
    
    <resultMap type="MiniDemand" id="MiniDemandResult">
        <result property="demandId"    column="demand_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="userId"    column="user_id"    />
        <result property="demandTitle"    column="demand_title"    />
        <result property="demandType"    column="demand_type"    />
        <result property="demandDesc"    column="demand_desc"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="demandStatus"    column="demand_status"    />
        <result property="isTop"    column="is_top"    />
        <result property="viewCount"    column="view_count"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryShortName"    column="category_short_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="formData"    column="form_data"    />
        <result property="hasDocking"    column="has_docking"    />
    </resultMap>

    <sql id="selectMiniDemandVo">
        select d.demand_id, d.category_id, d.user_id, d.demand_title, d.demand_type, d.demand_desc, d.contact_name, d.contact_phone,
               d.demand_status, d.is_top, d.view_count, d.create_by, d.create_time, d.update_by, d.update_time, d.remark, d.form_data,
               c.category_code,c.category_short_name,c.category_name,
               CASE WHEN EXISTS(SELECT 1 FROM mini_demand_docking dd WHERE dd.demand_id = d.demand_id AND dd.status = '0')
                    THEN 1 ELSE 0 END as has_docking,
               COALESCE(dock_stats.docking_count, 0) as docking_count,
               COALESCE(dock_stats.contacted_count, 0) as contacted_count,
               COALESCE(dock_stats.uncontacted_count, 0) as uncontacted_count
        from mini_demand d
        left join mini_demand_category c on d.category_id = c.category_id
        left join (
            SELECT dd.demand_id,
                   COUNT(dd.docking_id) as docking_count,
                   COUNT(CASE WHEN dd.is_contacted = '1' THEN 1 END) as contacted_count,
                   COUNT(CASE WHEN dd.is_contacted = '0' THEN 1 END) as uncontacted_count
            FROM mini_demand_docking dd
            WHERE dd.status = '0'
            GROUP BY dd.demand_id
        ) dock_stats ON d.demand_id = dock_stats.demand_id
    </sql>

    <select id="selectMiniDemandList" parameterType="MiniDemand" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        <where>
            <if test="categoryId != null "> and d.category_id = #{categoryId}</if>
            <if test="userId != null "> and d.user_id = #{userId}</if>
            <if test="demandTitle != null  and demandTitle != ''"> and d.demand_title like concat('%', #{demandTitle}, '%')</if>
            <if test="demandType != null  and demandType != ''"> and d.demand_type = #{demandType}</if>
            <if test="demandDesc != null  and demandDesc != ''"> and d.demand_desc like concat('%', #{demandDesc}, '%')</if>
            <if test="contactName != null  and contactName != ''"> and d.contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and d.contact_phone = #{contactPhone}</if>
            <if test="demandStatus != null  and demandStatus != ''"> and d.demand_status = #{demandStatus}</if>
            <if test="isTop != null  and isTop != ''"> and d.is_top = #{isTop}</if>
            <!-- 对接状态筛选条件 -->
            <if test="hasDocking != null">
                <choose>
                    <when test="hasDocking == 1 or hasDocking == true">
                        and EXISTS(SELECT 1 FROM mini_demand_docking dd WHERE dd.demand_id = d.demand_id AND dd.status = '0')
                    </when>
                    <when test="hasDocking == 0 or hasDocking == false">
                        and NOT EXISTS(SELECT 1 FROM mini_demand_docking dd WHERE dd.demand_id = d.demand_id AND dd.status = '0')
                    </when>
                </choose>
            </if>
            <!-- 时间筛选条件 -->
            <if test="timeFilter != null and timeFilter != ''">
                <choose>
                    <when test="timeFilter == 'week_within'">
                        and d.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                    </when>
                    <when test="timeFilter == 'week_over'">
                        and d.create_time &lt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
                    </when>
                    <when test="timeFilter == 'month_over'">
                        and d.create_time &lt;= DATE_SUB(NOW(), INTERVAL 1 MONTH)
                    </when>
                    <when test="timeFilter == 'year_over'">
                        and d.create_time &lt;= DATE_SUB(NOW(), INTERVAL 1 YEAR)
                    </when>
                </choose>
            </if>
        </where>
        order by d.is_top desc, d.create_time desc
    </select>
    
    <select id="selectMiniDemandByDemandId" parameterType="Long" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        where d.demand_id = #{demandId}
    </select>

    <select id="selectMiniDemandListByCategoryId" parameterType="Long" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        where d.category_id = #{categoryId} and d.demand_status = '0'
        order by d.is_top desc, d.create_time desc
    </select>

    <select id="selectMiniDemandListByUserId" parameterType="Long" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        where d.user_id = #{userId}
        order by d.create_time desc
    </select>
        
    <insert id="insertMiniDemand" parameterType="MiniDemand" useGeneratedKeys="true" keyProperty="demandId">
        insert into mini_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="demandTitle != null and demandTitle != ''">demand_title,</if>
            <if test="demandType != null">demand_type,</if>
            <if test="demandDesc != null">demand_desc,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="demandStatus != null">demand_status,</if>
            <if test="isTop != null">is_top,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="formData != null">form_data,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="demandTitle != null and demandTitle != ''">#{demandTitle},</if>
            <if test="demandType != null">#{demandType},</if>
            <if test="demandDesc != null">#{demandDesc},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="demandStatus != null">#{demandStatus},</if>
            <if test="isTop != null">#{isTop},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="formData != null">#{formData},</if>
         </trim>
    </insert>

    <update id="updateMiniDemand" parameterType="MiniDemand">
        update mini_demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="demandTitle != null and demandTitle != ''">demand_title = #{demandTitle},</if>
            <if test="demandType != null">demand_type = #{demandType},</if>
            <if test="demandDesc != null">demand_desc = #{demandDesc},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="demandStatus != null">demand_status = #{demandStatus},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="formData != null">form_data = #{formData},</if>
        </trim>
        where demand_id = #{demandId}
    </update>

    <update id="increaseViewCount" parameterType="Long">
        update mini_demand set view_count = view_count + 1 where demand_id = #{demandId}
    </update>

    <delete id="deleteMiniDemandByDemandId" parameterType="Long">
        delete from mini_demand where demand_id = #{demandId}
    </delete>

    <delete id="deleteMiniDemandByDemandIds" parameterType="String">
        delete from mini_demand where demand_id in 
        <foreach item="demandId" collection="array" open="(" separator="," close=")">
            #{demandId}
        </foreach>
    </delete>

    <select id="selectMiniDemandsByCategoryId" parameterType="Long" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        where d.category_id = #{categoryId} and d.demand_status = '0'
        order by d.is_top desc, d.create_time desc
    </select>

    <select id="selectRecommendedMiniDemandList" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        where d.demand_status = '0'
        order by d.is_top desc, d.create_time desc
        limit 10
    </select>

    <select id="selectEnabledMiniDemandList" resultMap="MiniDemandResult">
        <include refid="selectMiniDemandVo"/>
        where d.demand_status = '0'
        order by d.is_top desc, d.create_time desc
    </select>





</mapper>