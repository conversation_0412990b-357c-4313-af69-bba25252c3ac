{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demand\\index.vue", "mtime": 1753760187026}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVtYW5kLCBnZXREZW1hbmQsIGRlbERlbWFuZCwgYWRkRGVtYW5kLCB1cGRhdGVEZW1hbmQsIG9mZlNoZWxmRGVtYW5kLCBvblNoZWxmRGVtYW5kLCB1cGRhdGVDb250YWN0U3RhdHVzIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9kZW1hbmQiOw0KaW1wb3J0IHsgZ2V0RW5hYmxlZERlbWFuZENhdGVnb3J5TGlzdCB9IGZyb20gIkAvYXBpL21pbmlhcHAvZGVtYW5kY2F0ZWdvcnkiOw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTWluaURlbWFuZCIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6ZyA5rGC6KGo5qC85pWw5o2uDQogICAgICBkZW1hbmRMaXN0OiBbXSwNCiAgICAgIC8vIOiBlOezu+iusOW9leW8ueeqlw0KICAgICAgY29udGFjdERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6IGU57O76K6w5b2V6KGo5Y2VDQogICAgICBjb250YWN0Rm9ybTogew0KICAgICAgICBkb2NraW5nSWQ6IG51bGwsDQogICAgICAgIHVzZXJOYW1lOiAnJywNCiAgICAgICAgdXNlclBob25lOiAnJywNCiAgICAgICAgaXNDb250YWN0ZWQ6ICcwJywNCiAgICAgICAgY29udGFjdFJlc3VsdDogJycsDQogICAgICAgIGNvbnRhY3ROb3RlczogJycsDQogICAgICAgIGNvbnRhY3RUaW1lOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOiBlOezu+iusOW9leihqOWNlemqjOivgQ0KICAgICAgY29udGFjdFJ1bGVzOiB7DQogICAgICAgIGlzQ29udGFjdGVkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaYr+WQpuW3suiBlOezuyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOivpuaDheW8ueeqlw0KICAgICAgZGV0YWlsRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDor6bmg4XmlbDmja4NCiAgICAgIGRldGFpbEZvcm06IHsNCiAgICAgICAgZG9ja2luZ0xpc3Q6IFtdLA0KICAgICAgICBmb3JtRGF0YUxpc3Q6IFtdDQogICAgICB9LA0KICAgICAgLy8g6KGo5qC85Yi35pawa2V5DQogICAgICB0YWJsZVJlZnJlc2hLZXk6IDAsDQogICAgICAvLyDpnIDmsYLnsbvlnovliJfooagNCiAgICAgIGNhdGVnb3J5TGlzdDogW10sDQogICAgICAvLyDliqjmgIHooajljZXlrZfmrrUNCiAgICAgIGR5bmFtaWNGaWVsZHM6IFtdLA0KICAgICAgLy8g6YCJ5Lit55qE57G75Z6L5ZCN56ewDQogICAgICBzZWxlY3RlZENhdGVnb3J5TmFtZTogJycsDQogICAgICAvLyDliIbnsbvlrZfmrrXmlbDmja7vvIjmlrDmoLzlvI/vvIkNCiAgICAgIGNhdGVnb3J5RmllbGRzRGF0YTogW10sDQogICAgICAvLyDkuIrkvKDor7fmsYLlpLQNCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsNCiAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbg0KICAgICAgfSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBkZW1hbmRUaXRsZTogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlJZDogbnVsbCwNCiAgICAgICAgZGVtYW5kU3RhdHVzOiBudWxsLA0KICAgICAgICBoYXNEb2NraW5nOiBudWxsLA0KICAgICAgICB0aW1lRmlsdGVyOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY2F0ZWdvcnlJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpnIDmsYLnsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIGRlbWFuZFRpdGxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguagh+mimOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGRlbWFuZERlc2M6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6ZyA5rGC5o+P6L+w5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6IGU57O75Lq65aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdFBob25lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+S6uueUteivneS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgcGF0dGVybjogL14xWzN8NHw1fDZ8N3w4fDldWzAtOV1cZHs4fSQvLCBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgZGVtYW5kU3RhdHVzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxgueKtuaAgeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLyoqIOaMieaooeWdl+WIhue7hOeahOWKqOaAgeWtl+autSAqLw0KICAgIGdyb3VwZWREeW5hbWljRmllbGRzKCkgew0KICAgICAgY29uc3QgZ3JvdXBlZCA9IHt9Ow0KICAgICAgdGhpcy5keW5hbWljRmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICBjb25zdCBtb2R1bGVUaXRsZSA9IGZpZWxkLm1vZHVsZVRpdGxlIHx8ICflhbbku5blrZfmrrUnOw0KICAgICAgICBpZiAoIWdyb3VwZWRbbW9kdWxlVGl0bGVdKSB7DQogICAgICAgICAgZ3JvdXBlZFttb2R1bGVUaXRsZV0gPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBncm91cGVkW21vZHVsZVRpdGxlXS5wdXNoKGZpZWxkKTsNCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIGdyb3VwZWQ7DQogICAgfSwNCg0KICAgIC8qKiDlronlhajnmoTliqjmgIHmlbDmja7orr/pl67lmaggKi8NCiAgICBzYWZlRHluYW1pY0RhdGEoKSB7DQogICAgICBjb25zdCBzYWZlRGF0YSA9IHsgLi4udGhpcy5mb3JtLmR5bmFtaWNEYXRhIH07DQogICAgICB0aGlzLmR5bmFtaWNGaWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgIGlmIChmaWVsZC5uYW1lKSB7DQogICAgICAgICAgaWYgKGZpZWxkLnR5cGUgPT09ICdjaGVja2JveCcgJiYgIUFycmF5LmlzQXJyYXkoc2FmZURhdGFbZmllbGQubmFtZV0pKSB7DQogICAgICAgICAgICBzYWZlRGF0YVtmaWVsZC5uYW1lXSA9IFtdOw0KICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2ZpbGUnICYmICFBcnJheS5pc0FycmF5KHNhZmVEYXRhW2ZpZWxkLm5hbWVdKSkgew0KICAgICAgICAgICAgc2FmZURhdGFbZmllbGQubmFtZV0gPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHNhZmVEYXRhOw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmdldENhdGVnb3J5TGlzdCgpOw0KICAgIC8vIOa1i+ivleaWsOeahOaVsOaNruagvOW8jw0KICAgIHRoaXMudGVzdE5ld0RhdGFGb3JtYXQoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LpnIDmsYLliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3REZW1hbmQodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZGVtYW5kTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumcgOaxguWIl+ihqOWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W6ZyA5rGC5YiX6KGo5aSx6LSlIik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bpnIDmsYLnsbvlnovliJfooaggKi8NCiAgICBnZXRDYXRlZ29yeUxpc3QoKSB7DQogICAgICBnZXRFbmFibGVkRGVtYW5kQ2F0ZWdvcnlMaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2F0ZWdvcnlMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6ZyA5rGC57G75Z6L5YiX6KGo5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPlumcgOaxguexu+Wei+WIl+ihqOWksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCg0KDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgZGVtYW5kSWQ6IG51bGwsDQogICAgICAgIGNhdGVnb3J5SWQ6IG51bGwsDQogICAgICAgIGRlbWFuZFRpdGxlOiAiIiwNCiAgICAgICAgZGVtYW5kRGVzYzogIiIsDQogICAgICAgIGNvbnRhY3ROYW1lOiAiIiwNCiAgICAgICAgY29udGFjdFBob25lOiAiIiwNCiAgICAgICAgZGVtYW5kU3RhdHVzOiAiMCIsDQogICAgICAgIGlzVG9wOiAiMCIsDQogICAgICAgIHJlbWFyazogIiIsDQogICAgICAgIGR5bmFtaWNEYXRhOiB7fQ0KICAgICAgfTsNCg0KICAgICAgLy8g5riF6Zmk5Yqo5oCB5a2X5q6155qE6aqM6K+B6KeE5YiZDQogICAgICBPYmplY3Qua2V5cyh0aGlzLnJ1bGVzKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgIGlmIChrZXkuc3RhcnRzV2l0aCgnZHluYW1pY0RhdGEuJykpIHsNCiAgICAgICAgICB0aGlzLiRkZWxldGUodGhpcy5ydWxlcywga2V5KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOmHjee9ruWKqOaAgeWtl+autQ0KICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICB0aGlzLnNlbGVjdGVkQ2F0ZWdvcnlOYW1lID0gJyc7DQoNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmRlbWFuZElkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDpnIDmsYIiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIC8vIOWFiOa4heeQhueKtuaAge+8jOS9huS4jemHjee9ruihqOWNlQ0KICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICB0aGlzLnNlbGVjdGVkQ2F0ZWdvcnlOYW1lID0gJyc7DQoNCiAgICAgIGNvbnN0IGRlbWFuZElkID0gcm93LmRlbWFuZElkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0RGVtYW5kKGRlbWFuZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgLy8g5L2/55SoJHNldOadpeS/neaMgeWTjeW6lOW8jw0KICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2RlbWFuZElkJywgZGF0YS5kZW1hbmRJZCk7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdjYXRlZ29yeUlkJywgZGF0YS5jYXRlZ29yeUlkKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2RlbWFuZFRpdGxlJywgZGF0YS5kZW1hbmRUaXRsZSB8fCAiIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdkZW1hbmREZXNjJywgZGF0YS5kZW1hbmREZXNjIHx8ICIiKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2NvbnRhY3ROYW1lJywgZGF0YS5jb250YWN0TmFtZSB8fCAiIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdjb250YWN0UGhvbmUnLCBkYXRhLmNvbnRhY3RQaG9uZSB8fCAiIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdkZW1hbmRTdGF0dXMnLCBkYXRhLmRlbWFuZFN0YXR1cyB8fCAiMCIpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnaXNUb3AnLCBkYXRhLmlzVG9wIHx8ICIwIik7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdyZW1hcmsnLCBkYXRhLnJlbWFyayB8fCAiIik7DQoNCiAgICAgICAgLy8g6Kej5p6Q5Yqo5oCB6KGo5Y2V5pWw5o2uDQogICAgICAgIGlmIChkYXRhLmZvcm1EYXRhKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gSlNPTi5wYXJzZShkYXRhLmZvcm1EYXRhKTsNCg0KICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv5paw5qC85byP55qE5pWw5o2u77yI5YyF5ZCrZmllbGRz5pWw57uE55qE5a+56LGh77yJDQogICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtRGF0YSkgJiYgZm9ybURhdGEubGVuZ3RoID4gMCAmJiBmb3JtRGF0YVswXS5maWVsZHMpIHsNCiAgICAgICAgICAgICAgLy8g5paw5qC85byP77ya5YWI6K6+572u6KGo5Y2V5pWw5o2u77yM5YaN5aSE55CG5YiG57G75a2X5q615pWw5o2uDQogICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdkeW5hbWljRGF0YScsIHt9KTsNCg0KICAgICAgICAgICAgICAvLyDku45maWVsZHPkuK3mj5Dlj5bmlbDmja7liLBkeW5hbWljRGF0YQ0KICAgICAgICAgICAgICBmb3JtRGF0YS5mb3JFYWNoKGNhdGVnb3J5RGF0YSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGNhdGVnb3J5RGF0YS5maWVsZHMpIHsNCiAgICAgICAgICAgICAgICAgIGNhdGVnb3J5RGF0YS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZC52YWx1ZSAhPT0gdW5kZWZpbmVkICYmIGZpZWxkLnZhbHVlICE9PSBudWxsICYmIGZpZWxkLnZhbHVlICE9PSAnJykgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uZHluYW1pY0RhdGEsIGZpZWxkLm5hbWUsIGZpZWxkLnZhbHVlKTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgICAvLyDlpITnkIbliIbnsbvlrZfmrrXmlbDmja4NCiAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzQ2F0ZWdvcnlGaWVsZHNEYXRhKGZvcm1EYXRhKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIC8vIOaXp+agvOW8j++8muebtOaOpeS9v+eUqGZvcm1EYXRh5L2c5Li6ZHluYW1pY0RhdGENCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2R5bmFtaWNEYXRhJywgZm9ybURhdGEpOw0KICAgICAgICAgICAgICB0aGlzLmxvYWREeW5hbWljRmllbGRzKHRoaXMuZm9ybS5jYXRlZ29yeUlkKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDliqjmgIHooajljZXmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnZHluYW1pY0RhdGEnLCB7fSk7DQogICAgICAgICAgICB0aGlzLmxvYWREeW5hbWljRmllbGRzKHRoaXMuZm9ybS5jYXRlZ29yeUlkKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2R5bmFtaWNEYXRhJywge30pOw0KICAgICAgICAgIHRoaXMubG9hZER5bmFtaWNGaWVsZHModGhpcy5mb3JtLmNhdGVnb3J5SWQpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5Zyo5LiL5LiA5LiqdGlja+S4rea4hemZpOihqOWNlemqjOivgeeKtuaAgQ0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuJHJlZnMuZm9ybSkgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5mb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KDQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56ZyA5rGCIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICAvLyDlhYjpqozor4HliqjmgIHlrZfmrrUNCiAgICAgIGxldCBkeW5hbWljRmllbGRzVmFsaWQgPSB0cnVlOw0KICAgICAgbGV0IGZpcnN0RXJyb3JGaWVsZCA9IG51bGw7DQoNCiAgICAgIC8vIOmqjOivgeaWsOagvOW8j+eahOWIhuexu+Wtl+auteaVsOaNrg0KICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhICYmIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEuZm9yRWFjaChjYXRlZ29yeURhdGEgPT4gew0KICAgICAgICAgIGlmIChjYXRlZ29yeURhdGEuZmllbGRzKSB7DQogICAgICAgICAgICBjYXRlZ29yeURhdGEuZmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgICAgICBpZiAoZmllbGQucmVxdWlyZWQgJiYgZmllbGQubmFtZSAmJiBmaWVsZC50eXBlICE9PSAnc3RhdGljJykgew0KICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdOw0KICAgICAgICAgICAgICAgIGxldCBpc0VtcHR5ID0gZmFsc2U7DQoNCiAgICAgICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94JyB8fCBmaWVsZC50eXBlID09PSAnZmlsZScpIHsNCiAgICAgICAgICAgICAgICAgIGlzRW1wdHkgPSAhQXJyYXkuaXNBcnJheSh2YWx1ZSkgfHwgdmFsdWUubGVuZ3RoID09PSAwOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBpc0VtcHR5ID0gdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJyc7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgaWYgKGlzRW1wdHkpIHsNCiAgICAgICAgICAgICAgICAgIGR5bmFtaWNGaWVsZHNWYWxpZCA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgaWYgKCFmaXJzdEVycm9yRmllbGQpIHsNCiAgICAgICAgICAgICAgICAgICAgZmlyc3RFcnJvckZpZWxkID0gZmllbGQubGFiZWw7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g6aqM6K+B5pen5qC85byP55qE5Yqo5oCB5a2X5q61DQogICAgICAgIHRoaXMuZHluYW1pY0ZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICBpZiAoZmllbGQucmVxdWlyZWQgJiYgZmllbGQubmFtZSkgew0KICAgICAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV07DQogICAgICAgICAgICBsZXQgaXNFbXB0eSA9IGZhbHNlOw0KDQogICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94JyB8fCBmaWVsZC50eXBlID09PSAnZmlsZScpIHsNCiAgICAgICAgICAgICAgaXNFbXB0eSA9ICFBcnJheS5pc0FycmF5KHZhbHVlKSB8fCB2YWx1ZS5sZW5ndGggPT09IDA7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBpc0VtcHR5ID0gdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gJyc7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGlmIChpc0VtcHR5KSB7DQogICAgICAgICAgICAgIGR5bmFtaWNGaWVsZHNWYWxpZCA9IGZhbHNlOw0KICAgICAgICAgICAgICBpZiAoIWZpcnN0RXJyb3JGaWVsZCkgew0KICAgICAgICAgICAgICAgIGZpcnN0RXJyb3JGaWVsZCA9IGZpZWxkLmxhYmVsOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgaWYgKCFkeW5hbWljRmllbGRzVmFsaWQpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYCR7Zmlyc3RFcnJvckZpZWxkfeS4jeiDveS4uuepumApOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0geyAuLi50aGlzLmZvcm0gfTsNCg0KICAgICAgICAgIC8vIOaehOW7uuWMheWQq3ZhbHVl55qE5a6M5pW05a2X5q615pWw5o2u5qC85byPDQogICAgICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhICYmIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIC8vIOaWsOagvOW8j++8muS9v+eUqOWIhuexu+Wtl+auteaVsOaNru+8jOW5tuabtOaWsOavj+S4quWtl+auteeahHZhbHVlDQogICAgICAgICAgICBjb25zdCBjYXRlZ29yeURhdGFXaXRoVmFsdWVzID0gdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEubWFwKGNhdGVnb3J5RGF0YSA9PiAoew0KICAgICAgICAgICAgICAuLi5jYXRlZ29yeURhdGEsDQogICAgICAgICAgICAgIGZpZWxkczogY2F0ZWdvcnlEYXRhLmZpZWxkcy5tYXAoZmllbGQgPT4gKHsNCiAgICAgICAgICAgICAgICAuLi5maWVsZCwNCiAgICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdIHx8IGZpZWxkLnZhbHVlIHx8IChmaWVsZC50eXBlID09PSAnY2hlY2tib3gnIHx8IGZpZWxkLnR5cGUgPT09ICdmaWxlJyA/IFtdIDogJycpDQogICAgICAgICAgICAgIH0pKQ0KICAgICAgICAgICAgfSkpOw0KICAgICAgICAgICAgZm9ybURhdGEuZm9ybURhdGEgPSBKU09OLnN0cmluZ2lmeShjYXRlZ29yeURhdGFXaXRoVmFsdWVzKTsNCiAgICAgICAgICB9IGVsc2UgaWYgKGZvcm1EYXRhLmR5bmFtaWNEYXRhICYmIE9iamVjdC5rZXlzKGZvcm1EYXRhLmR5bmFtaWNEYXRhKS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDml6fmoLzlvI/vvJrnm7TmjqXkvb/nlKhkeW5hbWljRGF0YQ0KICAgICAgICAgICAgZm9ybURhdGEuZm9ybURhdGEgPSBKU09OLnN0cmluZ2lmeShmb3JtRGF0YS5keW5hbWljRGF0YSk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgZGVsZXRlIGZvcm1EYXRhLmR5bmFtaWNEYXRhOyAvLyDliKDpmaTkuLTml7blrZfmrrUNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCdzdWJtaXRGb3JtIC0gZm9ybURhdGEuZm9ybURhdGE6JywgZm9ybURhdGEuZm9ybURhdGEpOw0KDQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5kZW1hbmRJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZW1hbmQoZm9ybURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERlbWFuZChmb3JtRGF0YSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5p+l55yL6K+m5oOFICovDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgY29uc29sZS5sb2coJ+afpeeci+ivpuaDhSAtIOWOn+Wni+aVsOaNrjonLCByb3cpOw0KDQogICAgICAvLyDkvb/nlKhWdWUuc2V056Gu5L+d5ZON5bqU5byPDQogICAgICB0aGlzLiRzZXQodGhpcywgJ2RldGFpbEZvcm0nLCB7DQogICAgICAgIC4uLnJvdywNCiAgICAgICAgZG9ja2luZ0xvYWRpbmc6IHRydWUsDQogICAgICAgIGRvY2tpbmdMaXN0OiBbXSwNCiAgICAgICAgZm9ybURhdGFMaXN0OiBbXQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOino+aekOihqOWNleaVsOaNrg0KICAgICAgaWYgKHJvdy5mb3JtRGF0YSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gSlNPTi5wYXJzZShyb3cuZm9ybURhdGEpOw0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdmb3JtRGF0YUxpc3QnLCB0aGlzLnBhcnNlRm9ybURhdGFGb3JEaXNwbGF5KGZvcm1EYXRhKSk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDooajljZXmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGV0YWlsRm9ybSwgJ2Zvcm1EYXRhTGlzdCcsIFtdKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygn6K+m5oOF6KGo5Y2V5pWw5o2uOicsIHRoaXMuZGV0YWlsRm9ybSk7DQoNCiAgICAgIC8vIOaJk+W8gOW8ueeqlw0KICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCg0KICAgICAgLy8g5Yqg6L295a+55o6l6K6w5b2VDQogICAgICByZXF1ZXN0KHsNCiAgICAgICAgdXJsOiBgL21pbmlhcHAvZGVtYW5kLyR7cm93LmRlbWFuZElkfS9kb2NraW5nc2AsDQogICAgICAgIG1ldGhvZDogJ2dldCcNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygn5a+55o6l6K6w5b2V5ZON5bqUOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTGlzdCcsIHJlc3BvbnNlLmRhdGEgfHwgW10pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTGlzdCcsIFtdKTsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blr7nmjqXorrDlvZXlpLHotKU6JywgcmVzcG9uc2UubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blr7nmjqXorrDlvZXlvILluLg6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5kZXRhaWxGb3JtLCAnZG9ja2luZ0xpc3QnLCBbXSk7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGV0YWlsRm9ybSwgJ2RvY2tpbmdMb2FkaW5nJywgZmFsc2UpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDogZTns7vorrDlvZXmk43kvZwgKi8NCiAgICBoYW5kbGVDb250YWN0UmVjb3JkKGRvY2tpbmdSb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmiZPlvIDogZTns7vorrDlvZXlvLnnqpfvvIzlr7nmjqXorrDlvZXmlbDmja46JywgZG9ja2luZ1Jvdyk7DQogICAgICB0aGlzLmNvbnRhY3RGb3JtID0gew0KICAgICAgICBkb2NraW5nSWQ6IGRvY2tpbmdSb3cuZG9ja2luZ0lkLA0KICAgICAgICB1c2VyTmFtZTogZG9ja2luZ1Jvdy51c2VyTmFtZSwNCiAgICAgICAgdXNlclBob25lOiBkb2NraW5nUm93LnVzZXJQaG9uZSwNCiAgICAgICAgaXNDb250YWN0ZWQ6IGRvY2tpbmdSb3cuaXNDb250YWN0ZWQgfHwgJzAnLA0KICAgICAgICBjb250YWN0UmVzdWx0OiBkb2NraW5nUm93LmNvbnRhY3RSZXN1bHQgfHwgJycsDQogICAgICAgIGNvbnRhY3ROb3RlczogZG9ja2luZ1Jvdy5jb250YWN0Tm90ZXMgfHwgJycsDQogICAgICAgIGNvbnRhY3RUaW1lOiBkb2NraW5nUm93LmNvbnRhY3RUaW1lIHx8ICcnDQogICAgICB9Ow0KICAgICAgY29uc29sZS5sb2coJ+iBlOezu+ihqOWNleaVsOaNrjonLCB0aGlzLmNvbnRhY3RGb3JtKTsNCiAgICAgIHRoaXMuY29udGFjdERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk6IGU57O76K6w5b2V6KGo5Y2VICovDQogICAgc3VibWl0Q29udGFjdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJjb250YWN0Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g5aaC5p6c6YCJ5oup5bey6IGU57O75L2G5rKh5pyJ6K6+572u6IGU57O75pe26Ze077yM5L2/55So5b2T5YmN5pe26Ze0DQogICAgICAgICAgaWYgKHRoaXMuY29udGFjdEZvcm0uaXNDb250YWN0ZWQgPT09ICcxJyAmJiAhdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZSkgew0KICAgICAgICAgICAgdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZSA9IHRoaXMucGFyc2VUaW1lKG5ldyBEYXRlKCksICd7eX0te219LXtkfSB7aH06e2l9OntzfScpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS/neWtmOW9k+WJjeiBlOezu+eKtuaAge+8jOeUqOS6juebtOaOpeabtOaWsOacrOWcsOaVsOaNrg0KICAgICAgICAgIGNvbnN0IGRvY2tpbmdJZCA9IHRoaXMuY29udGFjdEZvcm0uZG9ja2luZ0lkOw0KICAgICAgICAgIGNvbnN0IG5ld0lzQ29udGFjdGVkID0gdGhpcy5jb250YWN0Rm9ybS5pc0NvbnRhY3RlZDsNCiAgICAgICAgICBjb25zdCBuZXdDb250YWN0UmVzdWx0ID0gdGhpcy5jb250YWN0Rm9ybS5jb250YWN0UmVzdWx0Ow0KICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3ROb3RlcyA9IHRoaXMuY29udGFjdEZvcm0uY29udGFjdE5vdGVzOw0KICAgICAgICAgIGNvbnN0IG5ld0NvbnRhY3RUaW1lID0gdGhpcy5jb250YWN0Rm9ybS5jb250YWN0VGltZTsNCg0KICAgICAgICAgIHVwZGF0ZUNvbnRhY3RTdGF0dXModGhpcy5jb250YWN0Rm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfogZTns7vnirbmgIHmm7TmlrDmiJDlip86JywgcmVzcG9uc2UpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6IGU57O76K6w5b2V5pu05paw5oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLmNvbnRhY3REaWFsb2dWaXNpYmxlID0gZmFsc2U7DQoNCiAgICAgICAgICAgIC8vIOWmguaenOivpuaDheW8ueeql+aYr+aJk+W8gOeahO+8jOWFiOebtOaOpeabtOaWsOacrOWcsOaVsOaNru+8jOWGjeWIt+aWsOivpuaDheS4reeahOWvueaOpeiusOW9lQ0KICAgICAgICAgICAgaWYgKHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSAmJiB0aGlzLmRldGFpbEZvcm0uZGVtYW5kSWQgJiYgdGhpcy5kZXRhaWxGb3JtLmRvY2tpbmdMaXN0KSB7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vmm7TmlrDmnKzlnLDlr7nmjqXorrDlvZUuLi4nKTsNCg0KICAgICAgICAgICAgICAvLyDlhYjnm7TmjqXmm7TmlrDmnKzlnLDmlbDmja7vvIznq4vljbPlj43mmKDlj5jljJYNCiAgICAgICAgICAgICAgY29uc3QgZG9ja2luZ0l0ZW0gPSB0aGlzLmRldGFpbEZvcm0uZG9ja2luZ0xpc3QuZmluZChpdGVtID0+IGl0ZW0uZG9ja2luZ0lkID09PSBkb2NraW5nSWQpOw0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5om+5Yiw55qE5a+55o6l6K6w5b2VOicsIGRvY2tpbmdJdGVtKTsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+imgeabtOaWsOeahOiBlOezu+eKtuaAgTonLCBuZXdJc0NvbnRhY3RlZCk7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflvZPliY1kb2NraW5nTGlzdDonLCB0aGlzLmRldGFpbEZvcm0uZG9ja2luZ0xpc3QpOw0KDQogICAgICAgICAgICAgIGlmIChkb2NraW5nSXRlbSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmm7TmlrDliY3nmoTogZTns7vnirbmgIE6JywgZG9ja2luZ0l0ZW0uaXNDb250YWN0ZWQpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHNldChkb2NraW5nSXRlbSwgJ2lzQ29udGFjdGVkJywgbmV3SXNDb250YWN0ZWQpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHNldChkb2NraW5nSXRlbSwgJ2NvbnRhY3RSZXN1bHQnLCBuZXdDb250YWN0UmVzdWx0KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRzZXQoZG9ja2luZ0l0ZW0sICdjb250YWN0Tm90ZXMnLCBuZXdDb250YWN0Tm90ZXMpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHNldChkb2NraW5nSXRlbSwgJ2NvbnRhY3RUaW1lJywgbmV3Q29udGFjdFRpbWUpOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmm7TmlrDlkI7nmoTogZTns7vnirbmgIE6JywgZG9ja2luZ0l0ZW0uaXNDb250YWN0ZWQpOw0KDQogICAgICAgICAgICAgICAgLy8g5by65Yi25Yi35paw6KGo5qC8DQogICAgICAgICAgICAgICAgdGhpcy50YWJsZVJlZnJlc2hLZXkrKzsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5by65Yi25Yi35paw6KGo5qC877yM5pawa2V5OicsIHRoaXMudGFibGVSZWZyZXNoS2V5KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmnKrmib7liLDlr7nlupTnmoTlr7nmjqXorrDlvZXvvIxkb2NraW5nSWQ6JywgZG9ja2luZ0lkKTsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmiYDmnInlr7nmjqXorrDlvZXnmoRJRDonLCB0aGlzLmRldGFpbEZvcm0uZG9ja2luZ0xpc3QubWFwKGl0ZW0gPT4gaXRlbS5kb2NraW5nSWQpKTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOeEtuWQjuWGjeS7juacjeWKoeWZqOWIt+aWsOWujOaVtOaVsOaNrg0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5byA5aeL5Yi35paw6K+m5oOF5Lit55qE5a+55o6l6K6w5b2VLi4uJyk7DQogICAgICAgICAgICAgIHRoaXMucmVmcmVzaERldGFpbERvY2tpbmdMaXN0KCk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWIt+aWsOS4u+WIl+ihqOS7peabtOaWsOe7n+iuoeaVsOaNrg0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfogZTns7vnirbmgIHmm7TmlrDlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuabtOaWsOWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOWIt+aWsOivpuaDheS4reeahOWvueaOpeiusOW9lSAqLw0KICAgIHJlZnJlc2hEZXRhaWxEb2NraW5nTGlzdCgpIHsNCiAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTG9hZGluZycsIHRydWUpOw0KICAgICAgcmVxdWVzdCh7DQogICAgICAgIHVybDogYC9taW5pYXBwL2RlbWFuZC8ke3RoaXMuZGV0YWlsRm9ybS5kZW1hbmRJZH0vZG9ja2luZ3NgLA0KICAgICAgICBtZXRob2Q6ICdnZXQnDQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ+WIt+aWsOWvueaOpeiusOW9leWTjeW6lDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kZXRhaWxGb3JtLCAnZG9ja2luZ0xpc3QnLCByZXNwb25zZS5kYXRhIHx8IFtdKTsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pu05paw5ZCO55qE5a+55o6l6K6w5b2VOicsIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdCk7DQogICAgICAgICAgLy8g5by65Yi25Yi35paw6KGo5qC8DQogICAgICAgICAgdGhpcy50YWJsZVJlZnJlc2hLZXkrKzsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pyN5Yqh5Zmo5pWw5o2u5Yi35paw5ZCO77yM5by65Yi25Yi35paw6KGo5qC877yM5pawa2V5OicsIHRoaXMudGFibGVSZWZyZXNoS2V5KTsNCiAgICAgICAgICAvLyDosIPor5XmlbDmja4NCiAgICAgICAgICB0aGlzLmRlYnVnRG9ja2luZ0RhdGEoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5kZXRhaWxGb3JtLCAnZG9ja2luZ0xpc3QnLCBbXSk7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a+55o6l6K6w5b2V5aSx6LSlOicsIHJlc3BvbnNlLm1zZyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a+55o6l6K6w5b2V5byC5bi4OicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZGV0YWlsRm9ybSwgJ2RvY2tpbmdMaXN0JywgW10pOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmRldGFpbEZvcm0sICdkb2NraW5nTG9hZGluZycsIGZhbHNlKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog6Kej5p6Q6KGo5Y2V5pWw5o2u5Li65pi+56S65qC85byPICovDQogICAgcGFyc2VGb3JtRGF0YUZvckRpc3BsYXkoZm9ybURhdGEpIHsNCiAgICAgIGNvbnN0IGRpc3BsYXlMaXN0ID0gW107DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+aWsOagvOW8j+eahOaVsOaNru+8iOWMheWQq2ZpZWxkc+aVsOe7hOeahOWvueixoe+8iQ0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtRGF0YSkgJiYgZm9ybURhdGEubGVuZ3RoID4gMCAmJiBmb3JtRGF0YVswXS5maWVsZHMpIHsNCiAgICAgICAgICAvLyDmlrDmoLzlvI/vvJrpgY3ljobmiYDmnInliIbnsbvlkozlrZfmrrUNCiAgICAgICAgICBmb3JtRGF0YS5mb3JFYWNoKGNhdGVnb3J5RGF0YSA9PiB7DQogICAgICAgICAgICBpZiAoY2F0ZWdvcnlEYXRhLmZpZWxkcyAmJiBBcnJheS5pc0FycmF5KGNhdGVnb3J5RGF0YS5maWVsZHMpKSB7DQogICAgICAgICAgICAgIGNhdGVnb3J5RGF0YS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgLy8g6Lez6L+H6Z2Z5oCB5bGV56S65a2X5q61DQogICAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgIT09ICdzdGF0aWMnICYmIGZpZWxkLm5hbWUgJiYgZmllbGQudmFsdWUgIT09IHVuZGVmaW5lZCAmJiBmaWVsZC52YWx1ZSAhPT0gbnVsbCAmJiBmaWVsZC52YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICAgIGRpc3BsYXlMaXN0LnB1c2goew0KICAgICAgICAgICAgICAgICAgICBsYWJlbDogZmllbGQubGFiZWwgfHwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGZpZWxkLnZhbHVlLA0KICAgICAgICAgICAgICAgICAgICB0eXBlOiBmaWVsZC50eXBlIHx8ICdpbnB1dCcNCiAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIGZvcm1EYXRhID09PSAnb2JqZWN0JyAmJiBmb3JtRGF0YSAhPT0gbnVsbCkgew0KICAgICAgICAgIC8vIOaXp+agvOW8j++8muebtOaOpemBjeWOhuWvueixoeWxnuaApw0KICAgICAgICAgIE9iamVjdC5rZXlzKGZvcm1EYXRhKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgICAgICBjb25zdCB2YWx1ZSA9IGZvcm1EYXRhW2tleV07DQogICAgICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgZGlzcGxheUxpc3QucHVzaCh7DQogICAgICAgICAgICAgICAgbGFiZWw6IGtleSwNCiAgICAgICAgICAgICAgICB2YWx1ZTogdmFsdWUsDQogICAgICAgICAgICAgICAgdHlwZTogJ2lucHV0JyAvLyDpu5jorqTnsbvlnosNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6KGo5Y2V5pWw5o2u5aSx6LSlOicsIGUpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gZGlzcGxheUxpc3Q7DQogICAgfSwNCg0KICAgIC8qKiDku45VUkzkuK3mj5Dlj5bmlofku7blkI0gKi8NCiAgICBnZXRGaWxlTmFtZUZyb21VcmwodXJsKSB7DQogICAgICBpZiAoIXVybCkgcmV0dXJuICfmnKrnn6Xmlofku7YnOw0KICAgICAgY29uc3QgcGFydHMgPSB1cmwuc3BsaXQoJy8nKTsNCiAgICAgIHJldHVybiBwYXJ0c1twYXJ0cy5sZW5ndGggLSAxXSB8fCAn5pyq55+l5paH5Lu2JzsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluiBlOezu+e7k+aenOagh+etvuexu+WeiyAqLw0KICAgIGdldENvbnRhY3RSZXN1bHRUeXBlKHJlc3VsdCkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgJ+iBlOezu+aIkOWKnyc6ICdzdWNjZXNzJywNCiAgICAgICAgJ+W3suacieWQiOS9nCc6ICdzdWNjZXNzJywNCiAgICAgICAgJ+aXoOS6uuaOpeWQrCc6ICd3YXJuaW5nJywNCiAgICAgICAgJ+eojeWQjuiBlOezuyc6ICd3YXJuaW5nJywNCiAgICAgICAgJ+WPt+eggemUmeivryc6ICdkYW5nZXInLA0KICAgICAgICAn5ouS57ud5rKf6YCaJzogJ2RhbmdlcicsDQogICAgICAgICfkuI3mhJ/lhbTotqMnOiAnaW5mbycsDQogICAgICAgICflhbbku5YnOiAnaW5mbycNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFtyZXN1bHRdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLyoqIOiwg+ivle+8muajgOafpeW9k+WJjeWvueaOpeiusOW9leaVsOaNriAqLw0KICAgIGRlYnVnRG9ja2luZ0RhdGEoKSB7DQogICAgICBjb25zb2xlLmxvZygnPT09IOiwg+ivleWvueaOpeiusOW9leaVsOaNriA9PT0nKTsNCiAgICAgIGNvbnNvbGUubG9nKCdkZXRhaWxGb3JtLmRvY2tpbmdMaXN0OicsIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdCk7DQogICAgICBpZiAodGhpcy5kZXRhaWxGb3JtLmRvY2tpbmdMaXN0ICYmIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZGV0YWlsRm9ybS5kb2NraW5nTGlzdC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKGDorrDlvZUke2luZGV4ICsgMX06YCwgew0KICAgICAgICAgICAgZG9ja2luZ0lkOiBpdGVtLmRvY2tpbmdJZCwNCiAgICAgICAgICAgIHVzZXJOYW1lOiBpdGVtLnVzZXJOYW1lLA0KICAgICAgICAgICAgaXNDb250YWN0ZWQ6IGl0ZW0uaXNDb250YWN0ZWQsDQogICAgICAgICAgICBpc0NvbnRhY3RlZFR5cGU6IHR5cGVvZiBpdGVtLmlzQ29udGFjdGVkDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coJ3RhYmxlUmVmcmVzaEtleTonLCB0aGlzLnRhYmxlUmVmcmVzaEtleSk7DQogICAgICBjb25zb2xlLmxvZygnPT09PT09PT09PT09PT09PT09PT09PT09Jyk7DQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBkZW1hbmRJZHMgPSByb3cgPyBbcm93LmRlbWFuZElkXSA6IHRoaXMuaWRzOw0KICAgICAgY29uc3QgY29uZmlybVRleHQgPSByb3cNCiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5Yig6Zmk6ZyA5rGC57yW5Y+35Li6IiR7cm93LmRlbWFuZElkfSLnmoTmlbDmja7pobnvvJ9gDQogICAgICAgIDogYOaYr+WQpuehruiupOWIoOmZpOmAieS4reeahCR7dGhpcy5pZHMubGVuZ3RofeadoeaVsOaNrumhue+8n2A7DQoNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oY29uZmlybVRleHQpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxEZW1hbmQoZGVtYW5kSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC9kZW1hbmQvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBg6ZyA5rGC5pWw5o2uXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgLyoqIOe9rumhti/lj5bmtojnva7pobYgKi8NCiAgICBoYW5kbGVUb2dnbGVUb3Aocm93KSB7DQogICAgICBjb25zdCB0ZXh0ID0gcm93LmlzVG9wID09PSAiMSIgPyAi5Y+W5raI572u6aG2IiA6ICLnva7pobYiOw0KICAgICAgY29uc3QgaXNUb3AgPSByb3cuaXNUb3AgPT09ICIxIiA/ICIwIiA6ICIxIjsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICci6ZyA5rGCIicgKyByb3cuZGVtYW5kVGl0bGUgKyAnIuWQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7DQogICAgICAgICAgZGVtYW5kSWQ6IHJvdy5kZW1hbmRJZCwNCiAgICAgICAgICBpc1RvcDogaXNUb3ANCiAgICAgICAgfTsNCiAgICAgICAgcmV0dXJuIHVwZGF0ZURlbWFuZCh1cGRhdGVEYXRhKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5LiL5p626ZyA5rGCICovDQogICAgaGFuZGxlT2ZmU2hlbGYocm93KSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoHkuIvmnrbpnIDmsYIiJyArIHJvdy5kZW1hbmRUaXRsZSArICci5ZCX77yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIG9mZlNoZWxmRGVtYW5kKHJvdy5kZW1hbmRJZCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS4i+aetuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5LiK5p626ZyA5rGCICovDQogICAgaGFuZGxlT25TaGVsZihyb3cpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgeS4iuaetumcgOaxgiInICsgcm93LmRlbWFuZFRpdGxlICsgJyLlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gb25TaGVsZkRlbWFuZChyb3cuZGVtYW5kSWQpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkuIrmnrbmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLyoqIOmcgOaxguexu+Wei+WPmOWMluS6i+S7tiAqLw0KICAgIG9uQ2F0ZWdvcnlDaGFuZ2UoY2F0ZWdvcnlJZCkgew0KICAgICAgY29uc29sZS5sb2coJ29uQ2F0ZWdvcnlDaGFuZ2UgLSBjYXRlZ29yeUlkOicsIGNhdGVnb3J5SWQpOw0KDQogICAgICAvLyDmuIXnqbrliqjmgIHooajljZXmlbDmja4NCiAgICAgIHRoaXMuZm9ybS5keW5hbWljRGF0YSA9IHt9Ow0KICAgICAgLy8g5riF56m65YiG57G75a2X5q615pWw5o2uDQogICAgICB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YSA9IFtdOw0KDQogICAgICBpZiAoIWNhdGVnb3J5SWQpIHsNCiAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeU5hbWUgPSAnJzsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcnlMaXN0LmZpbmQoY2F0ID0+IGNhdC5jYXRlZ29yeUlkID09PSBjYXRlZ29yeUlkKTsNCiAgICAgIGNvbnNvbGUubG9nKCdvbkNhdGVnb3J5Q2hhbmdlIC0gZm91bmQgY2F0ZWdvcnk6JywgY2F0ZWdvcnkpOw0KDQogICAgICBpZiAoY2F0ZWdvcnkgJiYgY2F0ZWdvcnkuZm9ybUZpZWxkcykgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IGZvcm1Db25maWcgPSBKU09OLnBhcnNlKGNhdGVnb3J5LmZvcm1GaWVsZHMpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCdvbkNhdGVnb3J5Q2hhbmdlIC0gZm9ybUNvbmZpZzonLCBmb3JtQ29uZmlnKTsNCg0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+aWsOagvOW8j+eahOaVsOaNru+8iOWMheWQq2ZpZWxkc+aVsOe7hOeahOWvueixoe+8iQ0KICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGZvcm1Db25maWcpICYmIGZvcm1Db25maWcubGVuZ3RoID4gMCAmJiBmb3JtQ29uZmlnWzBdLmZpZWxkcykgew0KICAgICAgICAgICAgLy8g5paw5qC85byP77ya5L2/55So5YiG57G75a2X5q615pWw5o2uDQogICAgICAgICAgICB0aGlzLnByb2Nlc3NDYXRlZ29yeUZpZWxkc0RhdGEoZm9ybUNvbmZpZyk7DQogICAgICAgICAgICBjb25zb2xlLmxvZygnb25DYXRlZ29yeUNoYW5nZSAtIHVzaW5nIG5ldyBmb3JtYXQsIGNhdGVnb3J5RmllbGRzRGF0YTonLCB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaXp+agvOW8j++8muS9v+eUqOS8oOe7n+eahOWKqOaAgeWtl+auteWKoOi9vQ0KICAgICAgICAgICAgdGhpcy5sb2FkRHluYW1pY0ZpZWxkcyhjYXRlZ29yeUlkKTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdvbkNhdGVnb3J5Q2hhbmdlIC0gdXNpbmcgb2xkIGZvcm1hdCwgZHluYW1pY0ZpZWxkczonLCB0aGlzLmR5bmFtaWNGaWVsZHMpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOihqOWNlemFjee9ruWksei0pTonLCBlKTsNCiAgICAgICAgICB0aGlzLmxvYWREeW5hbWljRmllbGRzKGNhdGVnb3J5SWQpOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmxvZygnb25DYXRlZ29yeUNoYW5nZSAtIG5vIGNhdGVnb3J5IG9yIGZvcm1GaWVsZHMgZm91bmQnKTsNCiAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeU5hbWUgPSAnJzsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWKoOi9veWKqOaAgeihqOWNleWtl+autSAqLw0KICAgIGxvYWREeW5hbWljRmllbGRzKGNhdGVnb3J5SWQpIHsNCiAgICAgIGlmICghY2F0ZWdvcnlJZCkgew0KICAgICAgICB0aGlzLmR5bmFtaWNGaWVsZHMgPSBbXTsNCiAgICAgICAgdGhpcy5zZWxlY3RlZENhdGVnb3J5TmFtZSA9ICcnOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5jYXRlZ29yeUxpc3QuZmluZChjYXQgPT4gY2F0LmNhdGVnb3J5SWQgPT09IGNhdGVnb3J5SWQpOw0KICAgICAgaWYgKGNhdGVnb3J5KSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRDYXRlZ29yeU5hbWUgPSBjYXRlZ29yeS5jYXRlZ29yeU5hbWU7DQoNCiAgICAgICAgaWYgKGNhdGVnb3J5LmZvcm1GaWVsZHMpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgY29uc3QgZm9ybUNvbmZpZyA9IEpTT04ucGFyc2UoY2F0ZWdvcnkuZm9ybUZpZWxkcyk7DQogICAgICAgICAgICB0aGlzLmR5bmFtaWNGaWVsZHMgPSBbXTsNCg0KICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv5paw55qE5qih5Z2X5YyW57uT5p6EDQogICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtQ29uZmlnKSAmJiBmb3JtQ29uZmlnLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgaWYgKGZvcm1Db25maWdbMF0uZmllbGRzKSB7DQogICAgICAgICAgICAgICAgLy8g5paw55qE5qih5Z2X5YyW57uT5p6E77ya5o+Q5Y+W5omA5pyJ5qih5Z2X5Lit55qE5a2X5q61DQogICAgICAgICAgICAgICAgZm9ybUNvbmZpZy5mb3JFYWNoKG1vZHVsZSA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAobW9kdWxlLmZpZWxkcyAmJiBBcnJheS5pc0FycmF5KG1vZHVsZS5maWVsZHMpKSB7DQogICAgICAgICAgICAgICAgICAgIG1vZHVsZS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g6Lez6L+H6Z2Z5oCB5bGV56S65a2X5q61DQogICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgIT09ICdzdGF0aWMnICYmIGZpZWxkLm5hbWUpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHluYW1pY0ZpZWxkcy5wdXNoKHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZmllbGQsDQogICAgICAgICAgICAgICAgICAgICAgICAgIG1vZHVsZVRpdGxlOiBtb2R1bGUubmFtZSAvLyDmt7vliqDmqKHlnZfmoIfpopjnlKjkuo7liIbnu4TmmL7npLoNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g5pen55qE5omB5bmz57uT5p6E77ya55u05o6l5L2/55SoDQogICAgICAgICAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gZm9ybUNvbmZpZzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDliJ3lp4vljJbliqjmgIHmlbDmja7lr7nosaHlkozpqozor4Hop4TliJkNCiAgICAgICAgICAgIHRoaXMuZHluYW1pY0ZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUpIHsNCiAgICAgICAgICAgICAgICAvLyDnoa7kv53lrZfmrrXmgLvmmK/mnInmraPnoa7nmoTliJ3lp4vlgLwNCiAgICAgICAgICAgICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94Jykgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheSh0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0pID8gdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdIDogW10pOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2ZpbGUnKSB7DQogICAgICAgICAgICAgICAgICAvLyDlpITnkIbmlofku7blrZfmrrXnmoTmlbDmja7ovazmjaINCiAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGVEYXRhID0gdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdOw0KICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBmaWxlRGF0YSA9PT0gJ3N0cmluZycgJiYgZmlsZURhdGEudHJpbSgpICE9PSAnJykgew0KICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/lrZfnrKbkuLJVUkzvvIzovazmjaLkuLrlr7nosaHmlbDnu4TmoLzlvI8NCiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBmaWxlRGF0YS5zcGxpdCgnLycpLnBvcCgpIHx8ICfkuIvovb3mlofku7YnOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmR5bmFtaWNEYXRhLCBmaWVsZC5uYW1lLCBbew0KICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICAgICAgICAgIHVybDogZmlsZURhdGENCiAgICAgICAgICAgICAgICAgICAgfV0pOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KGZpbGVEYXRhKSkgew0KICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzlt7Lnu4/mmK/mlbDnu4TvvIzkv53mjIHkuI3lj5gNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgZmlsZURhdGEpOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgLy8g5YW25LuW5oOF5Ya16K6+5Li656m65pWw57uEDQogICAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uZHluYW1pY0RhdGEsIGZpZWxkLm5hbWUsIFtdKTsNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLnR5cGUgPT09ICdudW1iZXInKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLmR5bmFtaWNEYXRhLCBmaWVsZC5uYW1lLA0KICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0gIT09IHVuZGVmaW5lZCA/IHRoaXMuZm9ybS5keW5hbWljRGF0YVtmaWVsZC5uYW1lXSA6IG51bGwpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2RhdGUnIHx8IGZpZWxkLnR5cGUgPT09ICd0aW1lJykgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdICE9PSB1bmRlZmluZWQgPyB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0gOiBudWxsKTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdICE9PSB1bmRlZmluZWQgPyB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV0gOiAnJyk7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgLy8g5re75Yqg5Yqo5oCB5a2X5q6155qE6aqM6K+B6KeE5YiZDQogICAgICAgICAgICAgICAgaWYgKGZpZWxkLnJlcXVpcmVkKSB7DQogICAgICAgICAgICAgICAgICBjb25zdCBydWxlTmFtZSA9IGBkeW5hbWljRGF0YS4ke2ZpZWxkLm5hbWV9YDsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVzLCBydWxlTmFtZSwgWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7ZmllbGQubGFiZWx95LiN6IO95Li656m6YCwNCiAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiBmaWVsZC50eXBlID09PSAnY2hlY2tib3gnID8gJ2NoYW5nZScgOiAnYmx1cicNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgXSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDooajljZXlrZfmrrXphY3nva7lpLHotKU6JywgZSk7DQogICAgICAgICAgICB0aGlzLmR5bmFtaWNGaWVsZHMgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5keW5hbWljRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluWtl+autemAiemhuSAqLw0KICAgIGdldEZpZWxkT3B0aW9ucyhmaWVsZCkgew0KICAgICAgaWYgKCFmaWVsZC5vcHRpb25zKSByZXR1cm4gW107DQogICAgICByZXR1cm4gZmllbGQub3B0aW9ucy5zcGxpdCgnLCcpLm1hcChvcHRpb24gPT4gb3B0aW9uLnRyaW0oKSkuZmlsdGVyKG9wdGlvbiA9PiBvcHRpb24pOw0KICAgIH0sDQoNCiAgICAvKiog5paH5Lu25LiK5Lyg5oiQ5Yqf5Zue6LCDICovDQogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0LCBmaWVsZCkgew0KICAgICAgY29uc29sZS5sb2coJ2hhbmRsZUZpbGVTdWNjZXNzIC0gcmVzcG9uc2U6JywgcmVzcG9uc2UsICdmaWxlOicsIGZpbGUsICdmaWVsZDonLCBmaWVsZC5uYW1lKTsNCg0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICBjb25zdCBmaWxlVXJsID0gcmVzcG9uc2UudXJsIHx8IHJlc3BvbnNlLmZpbGVOYW1lIHx8IHJlc3BvbnNlLmRhdGE7DQoNCiAgICAgICAgLy8g5a+55LqO5paH5Lu257G75Z6L5a2X5q6177yMdmFsdWXnm7TmjqXlrZjlgqhVUkzpk77mjqXvvIzkuI3lrZjlgqjmlofku7blkI3miJblr7nosaHnu5PmnoQNCiAgICAgICAgdGhpcy5oYW5kbGVGaWVsZElucHV0KGZpZWxkLCBmaWxlVXJsKTsNCg0KICAgICAgICBjb25zb2xlLmxvZygnaGFuZGxlRmlsZVN1Y2Nlc3MgLSDmlofku7bkuIrkvKDmiJDlip/vvIzorr7nva5VUkw6JywgZmlsZVVybCk7DQogICAgICAgIGNvbnNvbGUubG9nKCdoYW5kbGVGaWxlU3VjY2VzcyAtIGZpZWxkLnZhbHVlIGFmdGVyIHVwZGF0ZTonLCBmaWVsZC52YWx1ZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5paH5Lu25Yig6Zmk5Zue6LCDICovDQogICAgaGFuZGxlRmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCwgZmllbGQpIHsNCiAgICAgIC8vIOaWh+S7tuWIoOmZpOaXtu+8jOebtOaOpea4heepunZhbHVl5a2X5q61DQogICAgICB0aGlzLmhhbmRsZUZpZWxkSW5wdXQoZmllbGQsICcnKTsNCiAgICAgIGNvbnNvbGUubG9nKCdoYW5kbGVGaWxlUmVtb3ZlIC0g5paH5Lu25bey5Yig6Zmk77yM5riF56m65a2X5q615YC8Jyk7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5blpJrpgInmoYbnmoTlronlhajlgLwgKi8NCiAgICBnZXRDaGVja2JveFZhbHVlKGZpZWxkTmFtZSkgew0KICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGROYW1lXTsNCiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlIDogW107DQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDlpJrpgInmoYbnmoTlgLwgKi8NCiAgICB1cGRhdGVDaGVja2JveFZhbHVlKGZpZWxkTmFtZSwgdmFsdWUpIHsNCiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0uZHluYW1pY0RhdGEsIGZpZWxkTmFtZSwgQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZSA6IFtdKTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluaWh+S7tuWIl+ihqO+8iOeUqOS6jmVsLXVwbG9hZOe7hOS7tu+8iSAqLw0KICAgIGdldEZpbGVMaXN0KGZpZWxkKSB7DQogICAgICBjb25zdCBmaWxlcyA9IGZpZWxkLnZhbHVlOw0KICAgICAgY29uc29sZS5sb2coJ2dldEZpbGVMaXN0IC0gZmllbGQ6JywgZmllbGQubmFtZSwgJ3ZhbHVlOicsIGZpbGVzKTsNCg0KICAgICAgLy8g5aaC5p6c5piv5a2X56ym5LiyVVJM5LiU5LiN5Li656m677yM6L2s5o2i5Li65paH5Lu25YiX6KGo5qC85byP5pi+56S65ZyodXBsb2Fk57uE5Lu25LitDQogICAgICBpZiAodHlwZW9mIGZpbGVzID09PSAnc3RyaW5nJyAmJiBmaWxlcy50cmltKCkgIT09ICcnKSB7DQogICAgICAgIHJldHVybiBbew0KICAgICAgICAgIG5hbWU6IHRoaXMuZ2V0RmlsZU5hbWVGcm9tVXJsKGZpbGVzKSwNCiAgICAgICAgICB1cmw6IGZpbGVzLA0KICAgICAgICAgIHVpZDogYCR7ZmllbGQubmFtZX0tMGAsDQogICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgfV07DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYr+aVsOe7hOagvOW8j++8iOWFvOWuueaXp+aVsOaNru+8iQ0KICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmlsZXMpKSB7DQogICAgICAgIHJldHVybiBmaWxlcy5tYXAoKGZpbGUsIGluZGV4KSA9PiAoew0KICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSB8fCB0aGlzLmdldEZpbGVOYW1lRnJvbVVybChmaWxlLnVybCB8fCBmaWxlKSwNCiAgICAgICAgICB1cmw6IGZpbGUudXJsIHx8IGZpbGUsDQogICAgICAgICAgdWlkOiBgJHtmaWVsZC5uYW1lfS0ke2luZGV4fWAsDQogICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycNCiAgICAgICAgfSkpOw0KICAgICAgfQ0KDQogICAgICAvLyDlhbbku5bmg4XlhrXov5Tlm57nqbrmlbDnu4QNCiAgICAgIGNvbnNvbGUubG9nKCdnZXRGaWxlTGlzdCAtIOaXoOacieaViOaWh+S7tuaVsOaNru+8jOi/lOWbnuepuuaVsOe7hCcpOw0KICAgICAgcmV0dXJuIFtdOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5bey5LiK5Lyg55qE5paH5Lu25YiX6KGo77yI55So5LqO5pi+56S677yJICovDQogICAgZ2V0VXBsb2FkZWRGaWxlcyhmaWVsZCkgew0KICAgICAgY29uc3QgZmlsZXMgPSBmaWVsZC52YWx1ZTsNCiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KGZpbGVzKSA/IGZpbGVzIDogW107DQogICAgfSwNCg0KICAgIC8qKiDmlofku7bpooTop4ggKi8NCiAgICBoYW5kbGVGaWxlUHJldmlldyhmaWxlKSB7DQogICAgICBpZiAoZmlsZS51cmwpIHsNCiAgICAgICAgd2luZG93Lm9wZW4oZmlsZS51cmwsICdfYmxhbmsnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOS4i+i9veaWh+S7tiAqLw0KICAgIGRvd25sb2FkRmlsZSh1cmwsIGZpbGVOYW1lKSB7DQogICAgICAvLyDliJvlu7rkuIDkuKrkuLTml7bnmoRh5qCH562+5p2l6Kem5Y+R5LiL6L29DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gdXJsOw0KICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lIHx8ICfkuIvovb3mlofku7YnOw0KICAgICAgbGluay50YXJnZXQgPSAnX2JsYW5rJzsNCiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7DQogICAgICBsaW5rLmNsaWNrKCk7DQogICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5bey5LiK5Lyg55qE5paH5Lu2ICovDQogICAgcmVtb3ZlVXBsb2FkZWRGaWxlKGZpZWxkLCBpbmRleCkgew0KICAgICAgaWYgKGZpZWxkLnZhbHVlICYmIEFycmF5LmlzQXJyYXkoZmllbGQudmFsdWUpKSB7DQogICAgICAgIGNvbnN0IG5ld1ZhbHVlID0gWy4uLmZpZWxkLnZhbHVlXTsNCiAgICAgICAgbmV3VmFsdWUuc3BsaWNlKGluZGV4LCAxKTsNCiAgICAgICAgdGhpcy5oYW5kbGVGaWVsZElucHV0KGZpZWxkLCBuZXdWYWx1ZSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmlofku7ZVUkwgKi8NCiAgICByZW1vdmVGaWxlVXJsKGZpZWxkKSB7DQogICAgICB0aGlzLmhhbmRsZUZpZWxkSW5wdXQoZmllbGQsICcnKTsNCiAgICB9LA0KDQogICAgLyoqIOS7jlVSTOS4reaPkOWPluaWh+S7tuWQjSAqLw0KICAgIGdldEZpbGVOYW1lRnJvbVVybCh1cmwpIHsNCiAgICAgIGlmICghdXJsKSByZXR1cm4gJ+acquefpeaWh+S7tic7DQogICAgICBjb25zdCBwYXJ0cyA9IHVybC5zcGxpdCgnLycpOw0KICAgICAgY29uc3QgZmlsZU5hbWUgPSBwYXJ0c1twYXJ0cy5sZW5ndGggLSAxXTsNCiAgICAgIC8vIOWmguaenOaWh+S7tuWQjeWMheWQq+aXtumXtOaIs+etie+8jOWwneivleaPkOWPluWOn+Wni+aWh+S7tuWQjQ0KICAgICAgY29uc3QgbWF0Y2ggPSBmaWxlTmFtZS5tYXRjaCgvLipfXGQrQVxkK1wuKC4qKS8pOw0KICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgIHJldHVybiBg5paH5Lu2LiR7bWF0Y2hbMV19YDsNCiAgICAgIH0NCiAgICAgIHJldHVybiBmaWxlTmFtZSB8fCAn5pyq55+l5paH5Lu2JzsNCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhuWtl+autei+k+WFpSAqLw0KICAgIGhhbmRsZUZpZWxkSW5wdXQoZmllbGQsIHZhbHVlKSB7DQogICAgICAvLyDmm7TmlrDlrZfmrrXnmoR2YWx1ZQ0KICAgICAgZmllbGQudmFsdWUgPSB2YWx1ZTsNCiAgICAgIC8vIOWQjOatpeWIsOihqOWNleaVsOaNrg0KICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgdmFsdWUpOw0KICAgICAgY29uc29sZS5sb2coJ2hhbmRsZUZpZWxkSW5wdXQgLSBmaWVsZDonLCBmaWVsZC5uYW1lLCAndmFsdWU6JywgdmFsdWUpOw0KICAgIH0sDQoNCiAgICAvKiog5pu05paw5a2X5q615YC85Yiw6KGo5Y2V5pWw5o2uICovDQogICAgdXBkYXRlRmllbGRWYWx1ZShmaWVsZCkgew0KICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgZmllbGQudmFsdWUpOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5YiG57G75ZCN56ewICovDQogICAgZ2V0Q2F0ZWdvcnlOYW1lKCkgew0KICAgICAgaWYgKHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhICYmIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhWzBdLm5hbWUgfHwgJ+S4k+WxnuWtl+autSc7DQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZENhdGVnb3J5TmFtZSB8fCAn5LiT5bGe5a2X5q61JzsNCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhuWIhuexu+Wtl+auteaVsOaNriAqLw0KICAgIHByb2Nlc3NDYXRlZ29yeUZpZWxkc0RhdGEoZGF0YSkgew0KICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJykgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuY2F0ZWdvcnlGaWVsZHNEYXRhID0gSlNPTi5wYXJzZShkYXRhKTsNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOWIhuexu+Wtl+auteaVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgICB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YSA9IFtdOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkpIHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEgPSBkYXRhOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUZpZWxkc0RhdGEgPSBbXTsNCiAgICAgIH0NCg0KICAgICAgLy8g5Yid5aeL5YyW5a2X5q615YC85Yiw6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmNhdGVnb3J5RmllbGRzRGF0YS5mb3JFYWNoKGNhdGVnb3J5RGF0YSA9PiB7DQogICAgICAgIGlmIChjYXRlZ29yeURhdGEuZmllbGRzKSB7DQogICAgICAgICAgY2F0ZWdvcnlEYXRhLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgIC8vIOehruS/neWtl+auteacieWIneWni+WAvA0KICAgICAgICAgICAgaWYgKGZpZWxkLnZhbHVlID09PSB1bmRlZmluZWQgfHwgZmllbGQudmFsdWUgPT09IG51bGwpIHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgPT09ICdmaWxlJykgew0KICAgICAgICAgICAgICAgIGZpZWxkLnZhbHVlID0gW107DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2NoZWNrYm94Jykgew0KICAgICAgICAgICAgICAgIGZpZWxkLnZhbHVlID0gW107DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgZmllbGQudmFsdWUgPSAnJzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDku47ooajljZXmlbDmja7kuK3mgaLlpI3lrZfmrrXlgLzvvIjlpoLmnpzlrZjlnKjvvIkNCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uZHluYW1pY0RhdGEgJiYgdGhpcy5mb3JtLmR5bmFtaWNEYXRhW2ZpZWxkLm5hbWVdICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZmllbGQudmFsdWUgPSB0aGlzLmZvcm0uZHluYW1pY0RhdGFbZmllbGQubmFtZV07DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAvLyDorr7nva7liLDooajljZXmlbDmja4NCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybS5keW5hbWljRGF0YSwgZmllbGQubmFtZSwgZmllbGQudmFsdWUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOa1i+ivleaWsOeahOaVsOaNruagvOW8jyAqLw0KICAgIHRlc3ROZXdEYXRhRm9ybWF0KCkgew0KICAgICAgLy8g5L2/55So5oKo5o+Q5L6b55qE5a6e6ZmFSlNPTuaVsOaNruagvOW8j+i/m+ihjOa1i+ivlQ0KICAgICAgY29uc3QgdGVzdERhdGEgPSBbDQogICAgICAgIHsNCiAgICAgICAgICAibmFtZSI6ICLln7rnoYDkv6Hmga8iLA0KICAgICAgICAgICJkZXNjcmlwdGlvbiI6ICIiLA0KICAgICAgICAgICJmaWVsZHMiOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgICJsYWJlbCI6ICLkvIHkuJrlhajnp7AiLA0KICAgICAgICAgICAgICAibmFtZSI6ICJmaWVsZF82NTI0MDgiLA0KICAgICAgICAgICAgICAidHlwZSI6ICJpbnB1dCIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IHRydWUsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIiIsDQogICAgICAgICAgICAgICJwbGFjZWhvbGRlciI6ICLor7fovpPlhaUiLA0KICAgICAgICAgICAgICAic3RhdGljQ29udGVudCI6ICIiLA0KICAgICAgICAgICAgICAidmFsdWUiOiAi5rWL6K+V5LyB5Lia5pyJ6ZmQ5YWs5Y+4Ig0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgImxhYmVsIjogIuihjOS4muagh+etviIsDQogICAgICAgICAgICAgICJuYW1lIjogImZpZWxkXzcyMDk0NCIsDQogICAgICAgICAgICAgICJ0eXBlIjogInNlbGVjdCIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IHRydWUsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIuaWsOiDvea6kCznoaznp5HmioAiLA0KICAgICAgICAgICAgICAicGxhY2Vob2xkZXIiOiAi6K+36YCJ5oupIiwNCiAgICAgICAgICAgICAgInN0YXRpY0NvbnRlbnQiOiAiIiwNCiAgICAgICAgICAgICAgInZhbHVlIjogIuaWsOiDvea6kCINCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgICJsYWJlbCI6ICLogZTns7vkuroiLA0KICAgICAgICAgICAgICAibmFtZSI6ICJjb250YWN0X25hbWUiLA0KICAgICAgICAgICAgICAidHlwZSI6ICJpbnB1dCIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IHRydWUsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIiIsDQogICAgICAgICAgICAgICJwbGFjZWhvbGRlciI6ICLor7fovpPlhaUiLA0KICAgICAgICAgICAgICAic3RhdGljQ29udGVudCI6ICIiLA0KICAgICAgICAgICAgICAidmFsdWUiOiAi5byg5LiJIg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgImxhYmVsIjogIueUteivnSIsDQogICAgICAgICAgICAgICJuYW1lIjogInBob25lIiwNCiAgICAgICAgICAgICAgInR5cGUiOiAidGVsIiwNCiAgICAgICAgICAgICAgInJlcXVpcmVkIjogdHJ1ZSwNCiAgICAgICAgICAgICAgIm9wdGlvbnMiOiAiIiwNCiAgICAgICAgICAgICAgInBsYWNlaG9sZGVyIjogIuivt+i+k+WFpSIsDQogICAgICAgICAgICAgICJzdGF0aWNDb250ZW50IjogIiIsDQogICAgICAgICAgICAgICJ2YWx1ZSI6ICIxMzgwMDEzODAwMCINCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgICJpY29uIjogImh0dHA6Ly8xOTIuMTY4LjIuODM6ODA4MC9wcm9maWxlL3VwbG9hZC8yMDI1LzA3LzIyLzIwMjUwNzIyLTEwMDIyOF8yMDI1MDcyMjEwMDQwM0EwMDQucG5nIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgIm5hbWUiOiAi5YW25LuW5p2Q5paZ6KGl5YWFIiwNCiAgICAgICAgICAiZGVzY3JpcHRpb24iOiAiIiwNCiAgICAgICAgICAiZmllbGRzIjogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICAibGFiZWwiOiAi5LiK5Lyg6ZmE5Lu2IiwNCiAgICAgICAgICAgICAgIm5hbWUiOiAiZmllbGRfOTg5MjIyIiwNCiAgICAgICAgICAgICAgInR5cGUiOiAiZmlsZSIsDQogICAgICAgICAgICAgICJyZXF1aXJlZCI6IGZhbHNlLA0KICAgICAgICAgICAgICAib3B0aW9ucyI6ICIiLA0KICAgICAgICAgICAgICAicGxhY2Vob2xkZXIiOiAi5pyq6YCJ5oup5Lu75L2V5paH5Lu2IiwNCiAgICAgICAgICAgICAgInN0YXRpY0NvbnRlbnQiOiAiIiwNCiAgICAgICAgICAgICAgInZhbHVlIjogImh0dHA6Ly8xOTIuMTY4LjIuODM6ODA4MC9wcm9maWxlL3VwbG9hZC8yMDI1LzA3LzIzL3hodUZ3YTBxdWxQUzAzOTExYzM1MzI5ZjY5NTg0OGZiNjU5YTI0ZjZmMTU5XzIwMjUwNzIzMTgzMjIwQTAwMS5wbmciDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICAibGFiZWwiOiAi6YKu5Lu25o+Q5Lqk6IezIiwNCiAgICAgICAgICAgICAgIm5hbWUiOiAiZmllbGRfMjI3OTY5IiwNCiAgICAgICAgICAgICAgInR5cGUiOiAic3RhdGljIiwNCiAgICAgICAgICAgICAgInJlcXVpcmVkIjogZmFsc2UsDQogICAgICAgICAgICAgICJvcHRpb25zIjogIiIsDQogICAgICAgICAgICAgICJwbGFjZWhvbGRlciI6ICLor7fovpPlhaUiLA0KICAgICAgICAgICAgICAic3RhdGljQ29udGVudCI6ICJ5YW5neHVleXVlQGh0Y3lzdC5jb20o5paH5Lu25ZCN77ya44CQ5LyB5Lia5pud5YWJ55Sz6K+344CRLeS8geS4mi/pobnnm67lkI3vvIkiLA0KICAgICAgICAgICAgICAidmFsdWUiOiAiIg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgImljb24iOiAiaHR0cDovLzE5Mi4xNjguMi44Mzo4MDgwL3Byb2ZpbGUvdXBsb2FkLzIwMjUvMDcvMjIvMjAyNTA3MjItMTAwMjQ3XzIwMjUwNzIyMTAwNDU5QTAwOS5wbmciDQogICAgICAgIH0NCiAgICAgIF07DQoNCiAgICAgIC8vIOW9k+eCueWHu+S/ruaUueaMiemSruaXtu+8jOWPr+S7peiwg+eUqOi/meS4quaWueazleadpeiuvue9rua1i+ivleaVsOaNrg0KICAgICAgLy8gdGhpcy5wcm9jZXNzQ2F0ZWdvcnlGaWVsZHNEYXRhKHRlc3REYXRhKTsNCiAgICB9LA0KDQoNCg0KDQogIH0NCn07DQo="}, null]}