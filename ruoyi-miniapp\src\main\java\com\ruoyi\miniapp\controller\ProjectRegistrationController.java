package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.ProjectRegistration;
import com.ruoyi.miniapp.service.IProjectRegistrationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 项目报名Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "创赛路演-项目报名管理")
@RestController
@RequestMapping("/miniapp/haitang/project")
public class ProjectRegistrationController extends BaseController
{
    @Autowired
    private IProjectRegistrationService projectRegistrationService;

    @Autowired
    private ISysConfigService configService;

    // 赞助商图片配置键名
    private static final String SPONSOR_IMAGE_CONFIG_KEY = "miniapp.project.sponsor.image";

    /**
     * 查询项目报名列表
     */
    @ApiOperation("查询项目报名列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") ProjectRegistration projectRegistration)
    {
        startPage();
        List<ProjectRegistration> list = projectRegistrationService.selectProjectRegistrationList(projectRegistration);
        return getDataTable(list);
    }

    /**
     * 导出项目报名列表
     */
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:export')")
    @Log(title = "项目报名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectRegistration projectRegistration)
    {
        List<ProjectRegistration> list = projectRegistrationService.selectProjectRegistrationList(projectRegistration);
        ExcelUtil<ProjectRegistration> util = new ExcelUtil<ProjectRegistration>(ProjectRegistration.class);
        util.exportExcel(response, list, "项目报名数据");
    }

    /**
     * 获取赞助商图片
     */
    @ApiOperation("获取赞助商图片")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:query')")
    @GetMapping("/config/sponsor")
    public AjaxResult getSponsorImage()
    {
        String sponsorImageUrl = configService.selectConfigByKey(SPONSOR_IMAGE_CONFIG_KEY);
        // 确保返回的是字符串，如果为null则返回空字符串
        return AjaxResult.success(sponsorImageUrl != null ? sponsorImageUrl : "");
    }

    /**
     * 更新赞助商图片
     */
    @ApiOperation("更新赞助商图片")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:edit')")
    @Log(title = "赞助商图片", businessType = BusinessType.UPDATE)
    @PutMapping("/config/sponsor")
    public AjaxResult updateSponsorImage(@RequestBody java.util.Map<String, String> params)
    {
        String sponsorUnit = params.get("sponsorUnit");

        // 查找是否已存在该配置
        SysConfig existingConfig = new SysConfig();
        existingConfig.setConfigKey(SPONSOR_IMAGE_CONFIG_KEY);
        List<SysConfig> configList = configService.selectConfigList(existingConfig);

        if (configList != null && !configList.isEmpty()) {
            // 更新现有配置
            SysConfig config = configList.get(0);
            config.setConfigValue(sponsorUnit);
            configService.updateConfig(config);
        } else {
            // 创建新配置
            SysConfig newConfig = new SysConfig();
            newConfig.setConfigName("项目报名赞助商图片");
            newConfig.setConfigKey(SPONSOR_IMAGE_CONFIG_KEY);
            newConfig.setConfigValue(sponsorUnit);
            newConfig.setConfigType("N"); // N表示非系统内置
            newConfig.setRemark("项目报名模块的赞助商图片URL");
            configService.insertConfig(newConfig);
        }

        return AjaxResult.success("赞助商图片更新成功");
    }

    /**
     * 获取项目报名详细信息
     */
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectRegistrationService.selectProjectRegistrationById(id));
    }

    /**
     * 删除项目报名
     */
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:remove')")
    @Log(title = "项目报名", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectRegistrationService.deleteProjectRegistrationByIds(ids));
    }

    /**
     * 审核项目报名
     */
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:audit')")
    @Log(title = "项目报名审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody ProjectRegistration projectRegistration)
    {
        return toAjax(projectRegistrationService.auditProjectRegistration(projectRegistration));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 小程序端提交项目报名
     */
    @ApiOperation("小程序端提交项目报名")
    @PostMapping("/app/register")
    public AjaxResult register(@ApiParam("项目报名信息") @RequestBody ProjectRegistration projectRegistration)
    {
        // 验证必填字段
        if (projectRegistration.getProjectName() == null || projectRegistration.getProjectName().trim().isEmpty()) {
            return AjaxResult.error("项目名称不能为空");
        }
        if (projectRegistration.getTeamSize() == null) {
            return AjaxResult.error("团队规模不能为空");
        }
        if (projectRegistration.getCity() == null || projectRegistration.getCity().trim().isEmpty()) {
            return AjaxResult.error("城市不能为空");
        }
        if (projectRegistration.getCompetitionArea() == null || projectRegistration.getCompetitionArea().trim().isEmpty()) {
            return AjaxResult.error("赛区不能为空");
        }
        if (projectRegistration.getIndustry() == null || projectRegistration.getIndustry().trim().isEmpty()) {
            return AjaxResult.error("行业不能为空");
        }
        if (projectRegistration.getIsTjuAlumni() == null) {
            return AjaxResult.error("是否天津大学校友不能为空");
        }
        if (projectRegistration.getProjectDescription() == null || projectRegistration.getProjectDescription().trim().isEmpty()) {
            return AjaxResult.error("项目描述不能为空");
        }
        if (projectRegistration.getHasCompany() == null) {
            return AjaxResult.error("是否有公司不能为空");
        }
        if (projectRegistration.getContactName() == null || projectRegistration.getContactName().trim().isEmpty()) {
            return AjaxResult.error("联系人姓名不能为空");
        }
        if (projectRegistration.getContactPhone() == null || projectRegistration.getContactPhone().trim().isEmpty()) {
            return AjaxResult.error("联系人电话不能为空");
        }
        if (projectRegistration.getContactWechat() == null || projectRegistration.getContactWechat().trim().isEmpty()) {
            return AjaxResult.error("联系人微信不能为空");
        }
        if (projectRegistration.getContactPosition() == null || projectRegistration.getContactPosition().trim().isEmpty()) {
            return AjaxResult.error("联系人职位不能为空");
        }

        // 设置报名时间
        projectRegistration.setRegistrationTime(new java.util.Date());
        // 设置默认状态为待审核
        projectRegistration.setStatus(1);

        return toAjax(projectRegistrationService.insertProjectRegistration(projectRegistration));
    }

    /**
     * 小程序端获取赞助商图片
     */
    @ApiOperation("小程序端获取赞助商图片")
    @GetMapping("/app/config/sponsor")
    public AjaxResult getSponsorImageForApp()
    {
        String sponsorImageUrl = configService.selectConfigByKey(SPONSOR_IMAGE_CONFIG_KEY);
        // 确保返回的是字符串，如果为null则返回空字符串
        return AjaxResult.success(sponsorImageUrl != null ? sponsorImageUrl : "");
    }
}