{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1753694616742}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}