(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0be726"],{"2ffb":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"联系人",prop:"contactName"}},[a("el-input",{attrs:{placeholder:"请输入联系人姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.contactName,callback:function(t){e.$set(e.queryParams,"contactName",t)},expression:"queryParams.contactName"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"启用",value:"1"}}),a("el-option",{attrs:{label:"禁用",value:"0"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:competition:add"],expression:"['miniapp:competition:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:competition:edit"],expression:"['miniapp:competition:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:competition:remove"],expression:"['miniapp:competition:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:competition:export"],expression:"['miniapp:competition:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.competitionList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"介绍ID",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"顶部图片",align:"center",prop:"topImageUrl",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("image-preview",{attrs:{src:e.row.topImageUrl,width:50,height:50}})]}}])}),a("el-table-column",{attrs:{label:"联系人",align:"center",prop:"contactName"}}),a("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"contactPhone"}}),a("el-table-column",{attrs:{label:"联系微信",align:"center",prop:"contactWechat"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createdAt",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createdAt,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:competition:edit"],expression:"['miniapp:competition:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:competition:remove"],expression:"['miniapp:competition:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"顶部图片",prop:"topImageUrl"}},[a("image-upload",{model:{value:e.form.topImageUrl,callback:function(t){e.$set(e.form,"topImageUrl",t)},expression:"form.topImageUrl"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"大赛描述",prop:"descriptionContent"}},[a("editor",{attrs:{"min-height":192},model:{value:e.form.descriptionContent,callback:function(t){e.$set(e.form,"descriptionContent",t)},expression:"form.descriptionContent"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"赛程安排",prop:"scheduleContent"}},[a("editor",{attrs:{"min-height":192},model:{value:e.form.scheduleContent,callback:function(t){e.$set(e.form,"scheduleContent",t)},expression:"form.scheduleContent"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"报名条件",prop:"registrationConditions"}},[a("editor",{attrs:{"min-height":192},model:{value:e.form.registrationConditions,callback:function(t){e.$set(e.form,"registrationConditions",t)},expression:"form.registrationConditions"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"常见问题",prop:"faqContent"}},[a("editor",{attrs:{"min-height":192},model:{value:e.form.faqContent,callback:function(t){e.$set(e.form,"faqContent",t)},expression:"form.faqContent"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商务合作",prop:"businessCooperation"}},[a("editor",{attrs:{"min-height":192},model:{value:e.form.businessCooperation,callback:function(t){e.$set(e.form,"businessCooperation",t)},expression:"form.businessCooperation"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"赞助商图片",prop:"sponsorImageUrl"}},[a("image-upload",{model:{value:e.form.sponsorImageUrl,callback:function(t){e.$set(e.form,"sponsorImageUrl",t)},expression:"form.sponsorImageUrl"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系人姓名",prop:"contactName"}},[a("el-input",{attrs:{placeholder:"请输入联系人姓名"},model:{value:e.form.contactName,callback:function(t){e.$set(e.form,"contactName",t)},expression:"form.contactName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系电话",prop:"contactPhone"}},[a("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.contactPhone,callback:function(t){e.$set(e.form,"contactPhone",t)},expression:"form.contactPhone"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系微信",prop:"contactWechat"}},[a("el-input",{attrs:{placeholder:"请输入联系微信"},model:{value:e.form.contactWechat,callback:function(t){e.$set(e.form,"contactWechat",t)},expression:"form.contactWechat"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:0}},[e._v("启用")]),a("el-radio",{attrs:{label:1}},[e._v("禁用")])],1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],i=a("5530"),r=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function l(e){return Object(r["a"])({url:"/miniapp/competition/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/miniapp/competition/"+e,method:"get"})}function c(e){return Object(r["a"])({url:"/miniapp/competition",method:"post",data:e})}function m(e){return Object(r["a"])({url:"/miniapp/competition",method:"put",data:e})}function p(e){return Object(r["a"])({url:"/miniapp/competition/"+e,method:"delete"})}var u={name:"Competition",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,competitionList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,contactName:null,status:null},form:{},rules:{contactName:[{required:!0,message:"联系人姓名不能为空",trigger:"blur"}],contactPhone:[{required:!0,message:"联系电话不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.competitionList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,topImageUrl:null,descriptionContent:null,scheduleContent:null,registrationConditions:null,faqContent:null,businessCooperation:null,sponsorImageUrl:null,contactName:null,contactPhone:null,contactWechat:null,status:0,createdAt:null,updatedAt:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加大赛介绍"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改大赛介绍"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除大赛介绍编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/competition/export",Object(i["a"])({},this.queryParams),"大赛介绍_".concat((new Date).getTime(),".xlsx"))}}},d=u,h=a("2877"),f=Object(h["a"])(d,o,n,!1,null,null,null);t["default"]=f.exports}}]);