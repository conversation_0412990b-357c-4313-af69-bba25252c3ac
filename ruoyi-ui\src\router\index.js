import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  // 小程序管理模块路由
  {
    path: '/miniapp',
    component: Layout,
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'Miniapp',
    meta: {
      title: '小程序管理',
      icon: 'phone',
      permissions: ['miniapp:view']
    },
    children: [
      {
        path: 'statistics',
        component: () => import('@/views/miniapp/statistics/index'),
        name: 'MiniappStatistics',
        meta: {
          title: '数据统计',
          icon: 'chart',
          permissions: ['miniapp:statistics:view']
        }
      }
    ]
  },
  // 小程序内容管理
  {
    path: '/miniapp/content',
    component: Layout,
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'MiniappContent',
    meta: {
      title: '内容管理',
      icon: 'edit',
      permissions: ['miniapp:content:view']
    },
    children: [
      {
        path: 'banner',
        component: () => import('@/views/miniapp/content/banner/index'),
        name: 'MiniappBanner',
        meta: {
          title: '轮播图管理',
          icon: 'star',
          permissions: ['miniapp:banner:list']
        }
      },
      {
        path: 'notice',
        component: () => import('@/views/miniapp/content/notice/index'),
        name: 'MiniappNotice',
        meta: {
          title: '通知管理',
          icon: 'message',
          permissions: ['miniapp:notice:list']
        }
      },
      {
        path: 'barrage',
        component: () => import('@/views/miniapp/content/barrage/index'),
        name: 'MiniappBarrage',
        meta: {
          title: '弹幕管理',
          icon: 'message',
          permissions: ['miniapp:barrage:list']
        }
      },
      {
        path: 'contact',
        component: () => import('@/views/miniapp/content/contact/index'),
        name: 'MiniappContact',
        meta: {
          title: '联系人管理',
          icon: 'phone',
          permissions: ['miniapp:contact:list']
        }
      }
    ]
  },
  // 小程序业务管理
  {
    path: '/miniapp/business',
    component: Layout,
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'MiniappBusiness',
    meta: {
      title: '业务管理',
      icon: 'skill',
      permissions: ['miniapp:business:view']
    },
    children: [
      {
        path: 'activity',
        component: () => import('@/views/miniapp/business/activity/index'),
        name: 'MiniappActivity',
        meta: {
          title: '精彩活动',
          icon: 'star',
          permissions: ['miniapp:activity:list']
        }
      },
      {
        path: 'event',
        component: () => import('@/views/miniapp/business/event/index'),
        name: 'MiniappEvent',
        meta: {
          title: '活动报名管理',
          icon: 'form',
          permissions: ['miniapp:event:list']
        }
      },
      {
        path: 'registration',
        component: () => import('@/views/miniapp/registration/index'),
        name: 'MiniappRegistration',
        meta: {
          title: '用户报名记录',
          icon: 'peoples',
          permissions: ['miniapp:registration:list']
        }
      },
      {
        path: 'demand',
        component: () => import('@/views/miniapp/business/demand/index'),
        name: 'MiniappDemand',
        meta: {
          title: '需求对接',
          icon: 'link',
          permissions: ['miniapp:demand:list']
        }
      },
      {
        path: 'job',
        component: () => import('@/views/miniapp/business/job/index'),
        name: 'MiniappJob',
        meta: {
          title: '招聘信息',
          icon: 'user',
          permissions: ['miniapp:job:list']
        }
      },
      {
        path: 'techstar',
        component: () => import('@/views/miniapp/business/techstar/index'),
        name: 'MiniappTechStar',
        meta: {
          title: '科技之星',
          icon: 'star',
          permissions: ['miniapp:techstar:list']
        }
      }
    ]
  },
  // 小程序配置管理
  {
    path: '/miniapp/config',
    component: Layout,
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'MiniappConfig',
    meta: {
      title: '配置管理',
      icon: 'system',
      permissions: ['miniapp:config:view']
    },
    children: [
      {
        path: 'industry',
        component: () => import('@/views/miniapp/industry/index'),
        name: 'MiniappIndustry',
        meta: {
          title: '行业管理',
          icon: 'tree',
          permissions: ['miniapp:industry:list']
        }
      },
      {
        path: 'jobType',
        component: () => import('@/views/miniapp/jobType/index'),
        name: 'MiniappJobType',
        meta: {
          title: '职位类型管理',
          icon: 'post',
          permissions: ['miniapp:jobType:list']
        }
      },
      {
        path: 'enterprise',
        component: () => import('@/views/miniapp/config/enterprise/index'),
        name: 'MiniappEnterprise',
        meta: {
          title: '企业管理',
          icon: 'company',
          permissions: ['miniapp:enterprise:list']
        }
      },
      {
        path: 'page',
        component: () => import('@/views/miniapp/config/page/index'),
        name: 'MiniappPage',
        meta: {
          title: '页面内容',
          icon: 'documentation',
          permissions: ['miniapp:page:list']
        }
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push
let routerReplace = Router.prototype.replace
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
