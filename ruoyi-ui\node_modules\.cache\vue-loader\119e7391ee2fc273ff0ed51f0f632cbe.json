{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue?vue&type=style&index=0&id=58dd3dcc&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\park\\index.vue", "mtime": 1753847579546}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOWbvueJh+mihOiniOagt+W8jyAqLw0KLmltYWdlLXByZXZpZXctY29udGFpbmVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5wcmV2aWV3LWltYWdlIHsNCiAgbWF4LXdpZHRoOiAxMDAlOw0KICBtYXgtaGVpZ2h0OiA1MDBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQogIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7DQp9DQoNCi5wcmV2aWV3LWltYWdlOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTsNCn0NCg0KLyog57yp55Wl5Zu+5oKs5YGc5pWI5p6cICovDQouaW1hZ2UtcHJldmlldy1jb250YWluZXIgaW1nW2FsdD0i5Zut5Yy6566A5LuL5Zu+54mH57yp55Wl5Zu+Il06aG92ZXIgew0KICBvcGFjaXR5OiAwLjg7DQogIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+hBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/park", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"园区名称\" prop=\"parkName\">\r\n        <el-input\r\n          v-model=\"queryParams.parkName\"\r\n          placeholder=\"请输入园区名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"园区编码\" prop=\"parkCode\">\r\n        <el-input\r\n          v-model=\"queryParams.parkCode\"\r\n          placeholder=\"请输入园区编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"园区简介\" prop=\"description\">\r\n        <el-input\r\n          v-model=\"queryParams.description\"\r\n          placeholder=\"请输入园区简介\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:park:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:park:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:park:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:park:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-picture\"\r\n          size=\"mini\"\r\n          @click=\"handleIntroImageUpload\"\r\n          v-hasPermi=\"['miniapp:park:edit']\"\r\n        >园区简介图片</el-button>\r\n      </el-col>\r\n\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"parkList\" \r\n      @selection-change=\"handleSelectionChange\"\r\n      row-key=\"parkId\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"园区ID\" align=\"center\" prop=\"parkId\" width=\"80\" />\r\n      <el-table-column label=\"园区名称\" align=\"center\" prop=\"parkName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"园区编码\" align=\"center\" prop=\"parkCode\" width=\"120\" />\r\n      <el-table-column label=\"园区简介\" align=\"center\" prop=\"description\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImage\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.coverImage\" :width=\"80\" :height=\"50\" v-if=\"scope.row.coverImage\"/>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input-number \r\n            v-model=\"scope.row.sortOrder\" \r\n            :min=\"0\" \r\n            size=\"mini\"\r\n            :controls=\"false\"\r\n            @change=\"handleSortChange(scope.row)\"\r\n            style=\"width: 70px;\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"160\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:park:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:park:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改园区管理对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"园区名称\" prop=\"parkName\">\r\n              <el-input v-model=\"form.parkName\" placeholder=\"请输入园区名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"园区编码\" prop=\"parkCode\">\r\n              <el-input v-model=\"form.parkCode\" placeholder=\"请输入园区编码\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"园区简介\" prop=\"description\">\r\n          <el-input v-model=\"form.description\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入园区简介\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"封面图片\" prop=\"coverImage\">\r\n          <image-upload v-model=\"form.coverImage\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"详细内容\" prop=\"content\">\r\n          <editor v-model=\"form.content\" :min-height=\"300\"/>\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 园区简介图片上传对话框 -->\r\n    <el-dialog title=\"园区简介图片管理\" :visible.sync=\"introImageOpen\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"introImageForm\" :model=\"introImageForm\" label-width=\"120px\">\r\n        <el-form-item label=\"当前简介图片\">\r\n          <!-- 加载状态 -->\r\n          <div v-if=\"introImageLoading\" style=\"padding: 20px; text-align: center;\">\r\n            <i class=\"el-icon-loading\" style=\"margin-right: 5px;\"></i>\r\n            <span>正在加载图片信息...</span>\r\n          </div>\r\n\r\n          <!-- 已设置图片 -->\r\n          <div v-else-if=\"introImageForm.introImageUrl && introImageForm.introImageUrl.trim() !== ''\" style=\"padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n              <div>\r\n                <i class=\"el-icon-success\" style=\"color: #67c23a; margin-right: 5px;\"></i>\r\n                <span style=\"color: #409eff;\">已设置园区简介图片</span>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"previewIntroImage\"\r\n              >\r\n                预览图片\r\n              </el-button>\r\n            </div>\r\n            <!-- 图片预览缩略图 -->\r\n            <div style=\"margin-top: 10px;\">\r\n              <img\r\n                :src=\"introImageForm.introImageUrl\"\r\n                style=\"width: 120px; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer; border: 1px solid #dcdfe6;\"\r\n                @click=\"previewIntroImage\"\r\n                @error=\"handleImageError\"\r\n                alt=\"园区简介图片缩略图\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 未设置图片 -->\r\n          <div v-else style=\"padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-warning\" style=\"color: #e6a23c; margin-right: 5px;\"></i>\r\n            <span style=\"color: #e6a23c;\">暂未设置园区简介图片</span>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传新图片\">\r\n          <image-upload v-model=\"introImageForm.introImageUrl\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitIntroImageForm\">确 定</el-button>\r\n        <el-button @click=\"cancelIntroImage\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      title=\"园区简介图片预览\"\r\n      :visible.sync=\"previewVisible\"\r\n      width=\"800px\"\r\n      append-to-body\r\n      :before-close=\"closePreview\"\r\n    >\r\n      <div class=\"image-preview-container\">\r\n        <img\r\n          :src=\"previewImageUrl\"\r\n          class=\"preview-image\"\r\n          alt=\"园区简介图片\"\r\n        />\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closePreview\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"window.open(previewImageUrl, '_blank')\">\r\n          <i class=\"el-icon-zoom-in\"></i> 原始大小查看\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPark, getPark, delPark, addPark, updatePark, exportPark, getParkIntroImage, updateParkIntroImage } from \"@/api/miniapp/park\";\r\n\r\nexport default {\r\n  name: \"MiniPark\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 园区管理表格数据\r\n      parkList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 园区简介图片对话框\r\n      introImageOpen: false,\r\n      // 园区简介图片表单\r\n      introImageForm: {\r\n        introImageUrl: ''\r\n      },\r\n      // 图片加载状态\r\n      introImageLoading: false,\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        parkName: null,\r\n        parkCode: null,\r\n        description: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parkName: [\r\n          { required: true, message: \"园区名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sortOrder: [\r\n          { required: true, message: \"排序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询园区管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPark(this.queryParams).then(response => {\r\n        this.parkList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        parkId: null,\r\n        parkName: null,\r\n        parkCode: null,\r\n        description: null,\r\n        content: null,\r\n        coverImage: null,\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.parkId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加园区管理\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const parkId = row.parkId || this.ids;\r\n      getPark(parkId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改园区管理\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          if (this.form.parkId !== null && this.form.parkId !== undefined) {\r\n            updatePark(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPark(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const parkIds = row.parkId || this.ids;\r\n      this.$modal.confirm('是否确认删除园区管理编号为\"' + parkIds + '\"的数据项？').then(function() {\r\n        return delPark(parkIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有园区管理数据项？').then(() => {\r\n        this.loading = true;\r\n        return exportPark(this.queryParams);\r\n      }).then(response => {\r\n        this.$download.excel(response, '园区管理数据.xlsx');\r\n        this.loading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 排序修改 */\r\n    handleSortChange(row) {\r\n      updatePark(row).then(response => {\r\n        this.$modal.msgSuccess(\"排序修改成功\");\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 园区简介图片上传 */\r\n    handleIntroImageUpload() {\r\n      // 先重置表单数据\r\n      this.introImageForm.introImageUrl = '';\r\n      this.introImageOpen = true;\r\n      // 加载当前图片数据\r\n      this.loadIntroImage();\r\n    },\r\n    /** 加载园区简介图片 */\r\n    loadIntroImage() {\r\n      console.log('开始加载园区简介图片...');\r\n      this.introImageLoading = true;\r\n\r\n      getParkIntroImage().then(response => {\r\n        console.log('获取园区简介图片响应:', response);\r\n        if (response.code === 200) {\r\n          // 优先读取data字段，如果没有则读取msg字段（向后兼容）\r\n          this.introImageForm.introImageUrl = response.data || response.msg || '';\r\n          console.log('设置图片URL:', this.introImageForm.introImageUrl);\r\n        } else {\r\n          console.warn('获取园区简介图片失败:', response.msg);\r\n          this.introImageForm.introImageUrl = '';\r\n        }\r\n      }).catch(error => {\r\n        console.error('加载园区简介图片出错:', error);\r\n        this.introImageForm.introImageUrl = '';\r\n        this.$modal.msgError(\"加载园区简介图片失败\");\r\n      }).finally(() => {\r\n        this.introImageLoading = false;\r\n      });\r\n    },\r\n    /** 取消园区简介图片 */\r\n    cancelIntroImage() {\r\n      this.introImageOpen = false;\r\n      // 不要清空数据，保持原有数据\r\n      // this.introImageForm.introImageUrl = '';\r\n    },\r\n    /** 提交园区简介图片 */\r\n    submitIntroImageForm() {\r\n      updateParkIntroImage(this.introImageForm.introImageUrl).then(response => {\r\n        this.$modal.msgSuccess(\"园区简介图片更新成功\");\r\n        this.introImageOpen = false;\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"更新园区简介图片失败\");\r\n      });\r\n    },\r\n    /** 预览园区简介图片 */\r\n    previewIntroImage() {\r\n      if (this.introImageForm.introImageUrl && this.introImageForm.introImageUrl.trim() !== '') {\r\n        this.previewImageUrl = this.introImageForm.introImageUrl;\r\n        this.previewVisible = true;\r\n      } else {\r\n        this.$modal.msgWarning(\"暂无图片可预览\");\r\n      }\r\n    },\r\n    /** 关闭图片预览 */\r\n    closePreview() {\r\n      this.previewVisible = false;\r\n      this.previewImageUrl = '';\r\n    },\r\n    /** 图片加载错误处理 */\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', this.introImageForm.introImageUrl);\r\n      this.$modal.msgError(\"图片加载失败，请检查图片链接是否有效\");\r\n      // 可以设置一个默认的错误图片\r\n      // event.target.src = '/path/to/default-error-image.png';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 图片预览样式 */\r\n.image-preview-container {\r\n  text-align: center;\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 500px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.preview-image:hover {\r\n  transform: scale(1.02);\r\n}\r\n\r\n/* 缩略图悬停效果 */\r\n.image-preview-container img[alt=\"园区简介图片缩略图\"]:hover {\r\n  opacity: 0.8;\r\n  transform: scale(1.05);\r\n  transition: all 0.3s ease;\r\n}\r\n</style>\r\n"]}]}