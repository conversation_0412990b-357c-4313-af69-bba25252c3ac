(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4e8d99f8"],{"5c26":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"园区名称",prop:"parkName"}},[r("el-input",{attrs:{placeholder:"请输入园区名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.parkName,callback:function(t){e.$set(e.queryParams,"parkName",t)},expression:"queryParams.parkName"}})],1),r("el-form-item",{attrs:{label:"园区编码",prop:"parkCode"}},[r("el-input",{attrs:{placeholder:"请输入园区编码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.parkCode,callback:function(t){e.$set(e.queryParams,"parkCode",t)},expression:"queryParams.parkCode"}})],1),r("el-form-item",{attrs:{label:"园区简介",prop:"description"}},[r("el-input",{attrs:{placeholder:"请输入园区简介",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.description,callback:function(t){e.$set(e.queryParams,"description",t)},expression:"queryParams.description"}})],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:add"],expression:"['miniapp:park:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:edit"],expression:"['miniapp:park:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:remove"],expression:"['miniapp:park:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:export"],expression:"['miniapp:park:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:edit"],expression:"['miniapp:park:edit']"}],attrs:{type:"info",plain:"",icon:"el-icon-picture",size:"mini"},on:{click:e.handleIntroImageUpload}},[e._v("园区简介图片")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.parkList,"row-key":"parkId"},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"园区ID",align:"center",prop:"parkId",width:"80"}}),r("el-table-column",{attrs:{label:"园区名称",align:"center",prop:"parkName","show-overflow-tooltip":!0}}),r("el-table-column",{attrs:{label:"园区编码",align:"center",prop:"parkCode",width:"120"}}),r("el-table-column",{attrs:{label:"园区简介",align:"center",prop:"description","show-overflow-tooltip":!0}}),r("el-table-column",{attrs:{label:"封面图片",align:"center",prop:"coverImage",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.coverImage?r("image-preview",{attrs:{src:t.row.coverImage,width:80,height:50}}):e._e()]}}])}),r("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{width:"70px"},attrs:{min:0,size:"mini",controls:!1},on:{change:function(r){return e.handleSortChange(t.row)}},model:{value:t.row.sortOrder,callback:function(r){e.$set(t.row,"sortOrder",r)},expression:"scope.row.sortOrder"}})]}}])}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:edit"],expression:"['miniapp:park:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:park:remove"],expression:"['miniapp:park:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"园区名称",prop:"parkName"}},[r("el-input",{attrs:{placeholder:"请输入园区名称"},model:{value:e.form.parkName,callback:function(t){e.$set(e.form,"parkName",t)},expression:"form.parkName"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"园区编码",prop:"parkCode"}},[r("el-input",{attrs:{placeholder:"请输入园区编码"},model:{value:e.form.parkCode,callback:function(t){e.$set(e.form,"parkCode",t)},expression:"form.parkCode"}})],1)],1)],1),r("el-form-item",{attrs:{label:"园区简介",prop:"description"}},[r("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入园区简介"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),r("el-form-item",{attrs:{label:"封面图片",prop:"coverImage"}},[r("image-upload",{model:{value:e.form.coverImage,callback:function(t){e.$set(e.form,"coverImage",t)},expression:"form.coverImage"}})],1),r("el-form-item",{attrs:{label:"详细内容",prop:"content"}},[r("editor",{attrs:{"min-height":300},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[r("el-input-number",{attrs:{min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"园区简介图片管理",visible:e.introImageOpen,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.introImageOpen=t}}},[r("el-form",{ref:"introImageForm",attrs:{model:e.introImageForm,"label-width":"120px"}},[r("el-form-item",{attrs:{label:"当前简介图片"}},[e.introImageLoading?r("div",{staticStyle:{padding:"20px","text-align":"center"}},[r("i",{staticClass:"el-icon-loading",staticStyle:{"margin-right":"5px"}}),r("span",[e._v("正在加载图片信息...")])]):e.introImageForm.introImageUrl&&""!==e.introImageForm.introImageUrl.trim()?r("div",{staticStyle:{padding:"10px",background:"#f0f9ff",border:"1px solid #b3d8ff","border-radius":"4px","margin-bottom":"10px"}},[r("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[r("div",[r("i",{staticClass:"el-icon-success",staticStyle:{color:"#67c23a","margin-right":"5px"}}),r("span",{staticStyle:{color:"#409eff"}},[e._v("已设置园区简介图片")])]),r("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-view"},on:{click:e.previewIntroImage}},[e._v(" 预览图片 ")])],1),r("div",{staticStyle:{"margin-top":"10px"}},[r("img",{staticStyle:{width:"120px",height:"80px","object-fit":"cover","border-radius":"4px",cursor:"pointer",border:"1px solid #dcdfe6"},attrs:{src:e.introImageForm.introImageUrl,alt:"园区简介图片缩略图"},on:{click:e.previewIntroImage,error:e.handleImageError}})])]):r("div",{staticStyle:{padding:"10px",background:"#fdf6ec",border:"1px solid #f5dab1","border-radius":"4px","margin-bottom":"10px"}},[r("i",{staticClass:"el-icon-warning",staticStyle:{color:"#e6a23c","margin-right":"5px"}}),r("span",{staticStyle:{color:"#e6a23c"}},[e._v("暂未设置园区简介图片")])])]),r("el-form-item",{attrs:{label:"上传新图片"}},[r("image-upload",{attrs:{limit:1},model:{value:e.introImageForm.introImageUrl,callback:function(t){e.$set(e.introImageForm,"introImageUrl",t)},expression:"introImageForm.introImageUrl"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitIntroImageForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancelIntroImage}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"园区简介图片预览",visible:e.previewVisible,width:"800px","append-to-body":"","before-close":e.closePreview},on:{"update:visible":function(t){e.previewVisible=t}}},[r("div",{staticClass:"image-preview-container"},[r("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl,alt:"园区简介图片"}})]),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.closePreview}},[e._v("关 闭")]),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.window.open(e.previewImageUrl,"_blank")}}},[r("i",{staticClass:"el-icon-zoom-in"}),e._v(" 原始大小查看 ")])],1)])],1)},i=[],o=(r("d81d"),r("d3b7"),r("498a"),r("0643"),r("a573"),r("b775"));function n(e){return Object(o["a"])({url:"/miniapp/park/list",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/miniapp/park/"+e,method:"get"})}function s(e){return Object(o["a"])({url:"/miniapp/park",method:"post",data:e})}function m(e){return Object(o["a"])({url:"/miniapp/park",method:"put",data:e})}function c(e){return Object(o["a"])({url:"/miniapp/park/"+e,method:"delete"})}function p(e){return Object(o["a"])({url:"/miniapp/park/export",method:"post",data:e})}function u(){return Object(o["a"])({url:"/miniapp/park/config/intro",method:"get"})}function d(e){return Object(o["a"])({url:"/miniapp/park/config/intro",method:"put",data:{introImageUrl:e}})}var f={name:"MiniPark",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,parkList:[],title:"",open:!1,introImageOpen:!1,introImageForm:{introImageUrl:""},introImageLoading:!1,previewVisible:!1,previewImageUrl:"",queryParams:{pageNum:1,pageSize:10,parkName:null,parkCode:null,description:null,status:null},form:{},rules:{parkName:[{required:!0,message:"园区名称不能为空",trigger:"blur"}],sortOrder:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.parkList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={parkId:null,parkName:null,parkCode:null,description:null,content:null,coverImage:null,sortOrder:0,status:"0",remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.parkId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加园区管理"},handleUpdate:function(e){var t=this;this.reset();var r=e.parkId||this.ids;l(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改园区管理"}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){t&&(null!==e.form.parkId&&void 0!==e.form.parkId?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.parkId||this.ids;this.$modal.confirm('是否确认删除园区管理编号为"'+r+'"的数据项？').then((function(){return c(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){var e=this;this.$modal.confirm("是否确认导出所有园区管理数据项？").then((function(){return e.loading=!0,p(e.queryParams)})).then((function(t){e.$download.excel(t,"园区管理数据.xlsx"),e.loading=!1})).catch((function(){}))},handleSortChange:function(e){var t=this;m(e).then((function(e){t.$modal.msgSuccess("排序修改成功"),t.getList()}))},handleIntroImageUpload:function(){this.introImageForm.introImageUrl="",this.introImageOpen=!0,this.loadIntroImage()},loadIntroImage:function(){var e=this;console.log("开始加载园区简介图片..."),this.introImageLoading=!0,u().then((function(t){console.log("获取园区简介图片响应:",t),200===t.code?(e.introImageForm.introImageUrl=t.data||t.msg||"",console.log("设置图片URL:",e.introImageForm.introImageUrl)):(console.warn("获取园区简介图片失败:",t.msg),e.introImageForm.introImageUrl="")})).catch((function(t){console.error("加载园区简介图片出错:",t),e.introImageForm.introImageUrl="",e.$modal.msgError("加载园区简介图片失败")})).finally((function(){e.introImageLoading=!1}))},cancelIntroImage:function(){this.introImageOpen=!1},submitIntroImageForm:function(){var e=this;d(this.introImageForm.introImageUrl).then((function(t){e.$modal.msgSuccess("园区简介图片更新成功"),e.introImageOpen=!1})).catch((function(t){e.$modal.msgError("更新园区简介图片失败")}))},previewIntroImage:function(){this.introImageForm.introImageUrl&&""!==this.introImageForm.introImageUrl.trim()?(this.previewImageUrl=this.introImageForm.introImageUrl,this.previewVisible=!0):this.$modal.msgWarning("暂无图片可预览")},closePreview:function(){this.previewVisible=!1,this.previewImageUrl=""},handleImageError:function(e){console.error("图片加载失败:",this.introImageForm.introImageUrl),this.$modal.msgError("图片加载失败，请检查图片链接是否有效")}}},g=f,h=(r("7c8b"),r("2877")),b=Object(h["a"])(g,a,i,!1,null,"2cb81a28",null);t["default"]=b.exports},"7c8b":function(e,t,r){"use strict";r("b339")},b339:function(e,t,r){}}]);