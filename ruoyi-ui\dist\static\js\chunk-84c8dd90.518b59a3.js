(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-84c8dd90"],{"14ff":function(t,e,a){"use strict";a("988d")},2472:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"活动ID",prop:"activityId"}},[a("el-input",{attrs:{placeholder:"请输入活动ID",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.activityId,callback:function(e){t.$set(t.queryParams,"activityId",e)},expression:"queryParams.activityId"}})],1),a("el-form-item",{attrs:{label:"审核状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择审核状态",clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"待审核",value:"0"}}),a("el-option",{attrs:{label:"通过",value:"1"}}),a("el-option",{attrs:{label:"拒绝",value:"2"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:remove"],expression:"['miniapp:xiqing:registration:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:t.multiple},on:{click:t.handleDelete}},[t._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:export"],expression:"['miniapp:xiqing:registration:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:t.handleExport}},[t._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.registrationManageList},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"报名ID",align:"center",prop:"registrationId",width:"80"}}),a("el-table-column",{attrs:{label:"活动标题",align:"center",prop:"activityTitle","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"报名时间",align:"center",prop:"registrationTime",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.parseTime(e.row.registrationTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"审核状态",align:"center",prop:"status",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return["0"===e.row.status?a("el-tag",{attrs:{type:"warning"}},[t._v("待审核")]):"1"===e.row.status?a("el-tag",{attrs:{type:"success"}},[t._v("通过")]):"2"===e.row.status?a("el-tag",{attrs:{type:"danger"}},[t._v("拒绝")]):t._e()]}}])}),a("el-table-column",{attrs:{label:"审核人",align:"center",prop:"auditBy",width:"100"}}),a("el-table-column",{attrs:{label:"审核时间",align:"center",prop:"auditTime",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.parseTime(e.row.auditTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:query"],expression:"['miniapp:xiqing:registration:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return t.handleView(e.row)}}},[t._v("查看")]),"0"===e.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:audit"],expression:"['miniapp:xiqing:registration:audit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-check"},on:{click:function(a){return t.handleAudit(e.row)}}},[t._v("审核")]):t._e(),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:remove"],expression:"['miniapp:xiqing:registration:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("el-dialog",{attrs:{title:"报名详情",visible:t.viewOpen,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.viewOpen=e}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"报名ID"}},[t._v(t._s(t.form.registrationId))]),a("el-descriptions-item",{attrs:{label:"活动标题"}},[t._v(t._s(t.form.activityTitle))]),a("el-descriptions-item",{attrs:{label:"报名时间"}},[t._v(t._s(t.parseTime(t.form.registrationTime)))]),a("el-descriptions-item",{attrs:{label:"审核状态"}},["0"===t.form.status?a("el-tag",{attrs:{type:"warning"}},[t._v("待审核")]):"1"===t.form.status?a("el-tag",{attrs:{type:"success"}},[t._v("通过")]):"2"===t.form.status?a("el-tag",{attrs:{type:"danger"}},[t._v("拒绝")]):t._e()],1),a("el-descriptions-item",{attrs:{label:"审核人"}},[t._v(t._s(t.form.auditBy))]),a("el-descriptions-item",{attrs:{label:"审核时间"}},[t._v(t._s(t.parseTime(t.form.auditTime)))]),a("el-descriptions-item",{attrs:{label:"审核备注",span:2}},[t._v(t._s(t.form.auditRemark))])],1),a("div",{staticStyle:{"margin-top":"20px"}},[a("h4",[t._v("报名表单数据：")]),a("el-table",{staticStyle:{"margin-top":"10px"},attrs:{data:t.formDataList,border:""}},[a("el-table-column",{attrs:{prop:"key",label:"字段名",width:"200"}}),a("el-table-column",{attrs:{label:"字段值"},scopedSlots:t._u([{key:"default",fn:function(e){return["textarea"===e.row.type?a("div",{staticClass:"textarea-content"},[t._v(" "+t._s(e.row.value)+" ")]):"radio"===e.row.type||"picker"===e.row.type||"select"===e.row.type?a("el-tag",{attrs:{type:"primary",size:"small"}},[t._v(" "+t._s(e.row.value)+" ")]):"tel"===e.row.type||"phone"===e.row.type?a("span",{staticClass:"phone-number"},[t._v(" "+t._s(e.row.value)+" ")]):"date"===e.row.type?a("el-tag",{attrs:{type:"info",size:"small"}},[t._v(" "+t._s(e.row.value)+" ")]):"checkbox"===e.row.type?a("div",{staticClass:"checkbox-content"},[t._v(" "+t._s(e.row.value)+" ")]):"file"===e.row.type?a("div",{staticClass:"file-content"},[e.row.fileList&&e.row.fileList.length>0?a("div",t._l(e.row.fileList,(function(e,i){return a("div",{key:i,staticClass:"file-item"},[a("el-link",{staticClass:"file-link",attrs:{type:"primary",href:e.url,target:"_blank",download:e.name}},[a("i",{staticClass:"el-icon-document"}),t._v(" "+t._s(e.name)+" ")])],1)})),0):a("span",{staticClass:"no-file"},[t._v("未上传文件")])]):a("span",[t._v(t._s(e.row.value))])]}}])})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.viewOpen=!1}}},[t._v("关 闭")])],1)],1),a("el-dialog",{attrs:{title:"审核报名",visible:t.auditOpen,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.auditOpen=e}}},[a("el-form",{ref:"auditForm",attrs:{model:t.auditForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"审核结果",prop:"status"}},[a("el-radio-group",{model:{value:t.auditForm.status,callback:function(e){t.$set(t.auditForm,"status",e)},expression:"auditForm.status"}},[a("el-radio",{attrs:{label:"1"}},[t._v("通过")]),a("el-radio",{attrs:{label:"2"}},[t._v("拒绝")])],1)],1),a("el-form-item",{attrs:{label:"审核备注",prop:"auditRemark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入审核备注"},model:{value:t.auditForm.auditRemark,callback:function(e){t.$set(t.auditForm,"auditRemark",e)},expression:"auditForm.auditRemark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitAudit}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.auditOpen=!1}}},[t._v("取 消")])],1)],1)],1)},r=[],n=a("5530"),s=a("53ca"),o=a("c14f"),l=a("1da1"),u=(a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("b64b"),a("d3b7"),a("2ca0"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("b775"));function c(t){return Object(u["a"])({url:"/miniapp/xiqing/registration-manage/list",method:"get",params:t})}function m(t){return Object(u["a"])({url:"/miniapp/xiqing/registration-manage/"+t,method:"get"})}function p(t){return Object(u["a"])({url:"/miniapp/xiqing/registration-manage/"+t,method:"delete"})}function d(t){return Object(u["a"])({url:"/miniapp/xiqing/registration-manage/audit",method:"put",data:t})}var f=a("26dc"),h={name:"XiqingRegistrationManage",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,registrationManageList:[],viewOpen:!1,auditOpen:!1,formDataList:[],queryParams:{pageNum:1,pageSize:10,activityId:null,status:null},form:{},auditForm:{}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,c(this.queryParams).then((function(e){t.registrationManageList=e.rows,t.total=e.total,t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.registrationId})),this.multiple=!t.length},handleView:function(t){var e=this,a=t.registrationId;m(a).then(function(){var t=Object(l["a"])(Object(o["a"])().m((function t(a){return Object(o["a"])().w((function(t){while(1)switch(t.n){case 0:return e.form=a.data,t.n=1,e.parseFormData();case 1:e.viewOpen=!0;case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},parseFormData:function(){var t=this;return Object(l["a"])(Object(o["a"])().m((function e(){var a,i,r,n,l;return Object(o["a"])().w((function(e){while(1)switch(e.n){case 0:if(t.formDataList=[],!t.form.formData){e.n=6;break}if(e.p=1,a=JSON.parse(t.form.formData),!Array.isArray(a)){e.n=2;break}a.forEach((function(e){if(e.name&&void 0!==e.value&&null!==e.value&&""!==e.value){var a={key:e.label||e.name,value:t.formatFieldValue(e.value,e.type),type:e.type};"file"===e.type&&e.value&&(a.fileList=t.parseFileList(e.value)),t.formDataList.push(a)}})),e.n=4;break;case 2:if("object"!==Object(s["a"])(a)){e.n=4;break}return e.n=3,t.getActivityFormConfig();case 3:for(n in i=e.v,r={},i&&i.forEach((function(t){r[t.name]=t.label})),a)void 0!==a[n]&&null!==a[n]&&""!==a[n]&&t.formDataList.push({key:r[n]||n,value:a[n],type:"text"});case 4:e.n=6;break;case 5:e.p=5,l=e.v,console.error("解析表单数据失败:",l);case 6:return e.a(2)}}),e,null,[[1,5]])})))()},formatFieldValue:function(t,e){if(void 0===t||null===t||""===t)return"未填写";switch(e){case"checkbox":return Array.isArray(t)?t.length>0?t.join(", "):"未选择":t;case"radio":case"picker":case"select":return t||"未选择";case"textarea":return t;case"date":return t||"未选择";case"tel":case"phone":return t;case"file":return t;default:return t}},parseFileList:function(t){var e=this;if(!t)return[];try{if("string"===typeof t){if(!t.startsWith("[")&&!t.startsWith("{"))return[{name:this.getFileNameFromUrl(t),url:t}];var a=JSON.parse(t);if(Array.isArray(a))return a.map((function(t){return{name:t.name||t.fileName||"未知文件",url:t.url||t.path||t}}));if(a.url||a.path)return[{name:a.name||a.fileName||"未知文件",url:a.url||a.path}]}else{if(Array.isArray(t))return t.map((function(t){return"string"===typeof t?{name:e.getFileNameFromUrl(t),url:t}:{name:t.name||t.fileName||"未知文件",url:t.url||t.path||t}}));if("object"===Object(s["a"])(t))return[{name:t.name||t.fileName||"未知文件",url:t.url||t.path||t}]}}catch(i){console.error("解析文件列表失败:",i)}return[]},getFileNameFromUrl:function(t){if(!t)return"未知文件";var e=t.split("/"),a=e[e.length-1];return a||"未知文件"},getActivityFormConfig:function(){var t=this;return Object(l["a"])(Object(o["a"])().m((function e(){var a,i;return Object(o["a"])().w((function(e){while(1)switch(e.n){case 0:if(t.form.activityId){e.n=1;break}return e.a(2,null);case 1:return e.p=1,e.n=2,Object(f["a"])(t.form.activityId);case 2:if(a=e.v,!a.data||!a.data.formConfig){e.n=3;break}return e.a(2,JSON.parse(a.data.formConfig));case 3:e.n=5;break;case 4:e.p=4,i=e.v,console.error("获取活动表单配置失败:",i);case 5:return e.a(2,null)}}),e,null,[[1,4]])})))()},handleAudit:function(t){this.auditForm={registrationId:t.registrationId,status:"1",auditRemark:""},this.auditOpen=!0},submitAudit:function(){var t=this;d(this.auditForm).then((function(e){t.$modal.msgSuccess("审核成功"),t.auditOpen=!1,t.getList()}))},handleDelete:function(t){var e=this,a=t.registrationId||this.ids;this.$modal.confirm('是否确认删除报名编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/xiqing/registration-manage/export",Object(n["a"])({},this.queryParams),"registration_manage_".concat((new Date).getTime(),".xlsx"))}}},g=h,v=(a("14ff"),a("2877")),b=Object(v["a"])(g,i,r,!1,null,"62cf47f3",null);e["default"]=b.exports},"26dc":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var i=a("b775");function r(t){return Object(i["a"])({url:"/miniapp/xiqing/activity-config/"+t,method:"get"})}function n(t){return Object(i["a"])({url:"/miniapp/xiqing/activity-config",method:"put",data:t})}},"988d":function(t,e,a){}}]);