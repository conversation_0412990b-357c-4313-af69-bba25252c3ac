{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\main.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\main.js", "mtime": 1753760394060}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_js<PERSON><PERSON>ie", "_elementUi", "_App", "_store", "_router", "_directive", "_plugins", "_request", "_data", "_config", "_ruoyi2", "_Pagination", "_RightToolbar", "_Editor", "_FileUpload", "_ImageUpload", "_ImagePreview", "_DictTag", "_DictData", "<PERSON><PERSON>", "prototype", "getDicts", "getConfigKey", "parseTime", "resetForm", "addDateRange", "selectDictLabel", "selectDictLabels", "download", "handleTree", "component", "DictTag", "Pagination", "RightToolbar", "Editor", "FileUpload", "ImageUpload", "ImagePreview", "use", "directive", "plugins", "DictData", "install", "Element", "size", "Cookies", "get", "Dialog", "props", "closeOnClickModal", "default", "config", "productionTip", "el", "router", "store", "render", "h", "App"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\n\r\nimport Cookies from 'js-cookie'\r\n\r\nimport Element from 'element-ui'\r\nimport './assets/styles/element-variables.scss'\r\n\r\nimport '@/assets/styles/index.scss' // global css\r\nimport '@/assets/styles/ruoyi.scss' // ruoyi css\r\nimport App from './App'\r\nimport store from './store'\r\nimport router from './router'\r\nimport directive from './directive' // directive\r\nimport plugins from './plugins' // plugins\r\nimport { download } from '@/utils/request'\r\n\r\nimport './assets/icons' // icon\r\nimport './permission' // permission control\r\nimport { getDicts } from \"@/api/system/dict/data\"\r\nimport { getConfigKey } from \"@/api/system/config\"\r\nimport { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from \"@/utils/ruoyi\"\r\n// 分页组件\r\nimport Pagination from \"@/components/Pagination\"\r\n// 自定义表格工具组件\r\nimport RightToolbar from \"@/components/RightToolbar\"\r\n// 富文本组件\r\nimport Editor from \"@/components/Editor\"\r\n// 文件上传组件\r\nimport FileUpload from \"@/components/FileUpload\"\r\n// 图片上传组件\r\nimport ImageUpload from \"@/components/ImageUpload\"\r\n// 图片预览组件\r\nimport ImagePreview from \"@/components/ImagePreview\"\r\n// 字典标签组件\r\nimport DictTag from '@/components/DictTag'\r\n// 字典数据组件\r\nimport DictData from '@/components/DictData'\r\n\r\n// 全局方法挂载\r\nVue.prototype.getDicts = getDicts\r\nVue.prototype.getConfigKey = getConfigKey\r\nVue.prototype.parseTime = parseTime\r\nVue.prototype.resetForm = resetForm\r\nVue.prototype.addDateRange = addDateRange\r\nVue.prototype.selectDictLabel = selectDictLabel\r\nVue.prototype.selectDictLabels = selectDictLabels\r\nVue.prototype.download = download\r\nVue.prototype.handleTree = handleTree\r\n\r\n// 全局组件挂载\r\nVue.component('DictTag', DictTag)\r\nVue.component('Pagination', Pagination)\r\nVue.component('RightToolbar', RightToolbar)\r\nVue.component('Editor', Editor)\r\nVue.component('FileUpload', FileUpload)\r\nVue.component('ImageUpload', ImageUpload)\r\nVue.component('ImagePreview', ImagePreview)\r\n\r\nVue.use(directive)\r\nVue.use(plugins)\r\nDictData.install()\r\n\r\n/**\r\n * If you don't want to use mock-server\r\n * you want to use MockJs for mock api\r\n * you can execute: mockXHR()\r\n *\r\n * Currently MockJs will be used in the production environment,\r\n * please remove it before going online! ! !\r\n */\r\n\r\nVue.use(Element, {\r\n  size: Cookies.get('size') || 'medium' // set element-ui default size\r\n})\r\n\r\n// 全局设置Element UI对话框默认不允许点击遮罩关闭\r\nElement.Dialog.props.closeOnClickModal.default = false\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  el: '#app',\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n})\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACAA,OAAA;AAEAA,OAAA;AACAA,OAAA;AACA,IAAAG,IAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,QAAA,GAAAR,OAAA;AAEAA,OAAA;AACAA,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AAEA,IAAAY,WAAA,GAAAb,sBAAA,CAAAC,OAAA;AAEA,IAAAa,aAAA,GAAAd,sBAAA,CAAAC,OAAA;AAEA,IAAAc,OAAA,GAAAf,sBAAA,CAAAC,OAAA;AAEA,IAAAe,WAAA,GAAAhB,sBAAA,CAAAC,OAAA;AAEA,IAAAgB,YAAA,GAAAjB,sBAAA,CAAAC,OAAA;AAEA,IAAAiB,aAAA,GAAAlB,sBAAA,CAAAC,OAAA;AAEA,IAAAkB,QAAA,GAAAnB,sBAAA,CAAAC,OAAA;AAEA,IAAAmB,SAAA,GAAApB,sBAAA,CAAAC,OAAA;AA7BoC;AACA;;AAIA;AACJ;;AAGR;AACF;;AAItB;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAGA;AACAoB,YAAG,CAACC,SAAS,CAACC,QAAQ,GAAGA,cAAQ;AACjCF,YAAG,CAACC,SAAS,CAACE,YAAY,GAAGA,oBAAY;AACzCH,YAAG,CAACC,SAAS,CAACG,SAAS,GAAGA,iBAAS;AACnCJ,YAAG,CAACC,SAAS,CAACI,SAAS,GAAGA,iBAAS;AACnCL,YAAG,CAACC,SAAS,CAACK,YAAY,GAAGA,oBAAY;AACzCN,YAAG,CAACC,SAAS,CAACM,eAAe,GAAGA,uBAAe;AAC/CP,YAAG,CAACC,SAAS,CAACO,gBAAgB,GAAGA,wBAAgB;AACjDR,YAAG,CAACC,SAAS,CAACQ,QAAQ,GAAGA,iBAAQ;AACjCT,YAAG,CAACC,SAAS,CAACS,UAAU,GAAGA,kBAAU;;AAErC;AACAV,YAAG,CAACW,SAAS,CAAC,SAAS,EAAEC,gBAAO,CAAC;AACjCZ,YAAG,CAACW,SAAS,CAAC,YAAY,EAAEE,mBAAU,CAAC;AACvCb,YAAG,CAACW,SAAS,CAAC,cAAc,EAAEG,qBAAY,CAAC;AAC3Cd,YAAG,CAACW,SAAS,CAAC,QAAQ,EAAEI,eAAM,CAAC;AAC/Bf,YAAG,CAACW,SAAS,CAAC,YAAY,EAAEK,mBAAU,CAAC;AACvChB,YAAG,CAACW,SAAS,CAAC,aAAa,EAAEM,oBAAW,CAAC;AACzCjB,YAAG,CAACW,SAAS,CAAC,cAAc,EAAEO,qBAAY,CAAC;AAE3ClB,YAAG,CAACmB,GAAG,CAACC,kBAAS,CAAC;AAClBpB,YAAG,CAACmB,GAAG,CAACE,gBAAO,CAAC;AAChBC,iBAAQ,CAACC,OAAO,CAAC,CAAC;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAvB,YAAG,CAACmB,GAAG,CAACK,kBAAO,EAAE;EACfC,IAAI,EAAEC,iBAAO,CAACC,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC;AACxC,CAAC,CAAC;;AAEF;AACAH,kBAAO,CAACI,MAAM,CAACC,KAAK,CAACC,iBAAiB,CAACC,OAAO,GAAG,KAAK;AAEtD/B,YAAG,CAACgC,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIjC,YAAG,CAAC;EACNkC,EAAE,EAAE,MAAM;EACVC,MAAM,EAANA,eAAM;EACNC,KAAK,EAALA,cAAK;EACLC,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAACC,YAAG,CAAC;EAAA;AACrB,CAAC,CAAC", "ignoreList": []}]}