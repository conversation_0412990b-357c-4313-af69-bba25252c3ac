(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2253ca"],{e416:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{label:"视频标题",prop:"title"}},[i("el-input",{attrs:{placeholder:"请输入视频标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),i("el-form-item",{attrs:{label:"状态",prop:"status"}},[i("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[i("el-option",{attrs:{label:"启用",value:"0"}}),i("el-option",{attrs:{label:"禁用",value:"1"}})],1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:video:add"],expression:"['miniapp:video:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:video:edit"],expression:"['miniapp:video:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:video:remove"],expression:"['miniapp:video:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:video:export"],expression:"['miniapp:video:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),i("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.videoList},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),i("el-table-column",{attrs:{label:"视频ID",align:"center",prop:"id"}}),i("el-table-column",{attrs:{label:"视频标题",align:"center",prop:"title","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{label:"视频预览",align:"center",prop:"videoUrl",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.videoUrl?i("video",{staticStyle:{width:"150px",height:"100px"},attrs:{src:e.getVideoUrl(t.row.videoUrl),controls:"",preload:"metadata"}},[e._v(" 您的浏览器不支持视频播放 ")]):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder"}}),i("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),i("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createdAt",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.createdAt,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:video:edit"],expression:"['miniapp:video:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v("修改")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:video:remove"],expression:"['miniapp:video:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),i("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[i("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"视频标题",prop:"title"}},[i("el-input",{attrs:{placeholder:"请输入视频标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"视频文件",prop:"videoUrl"}},[i("file-upload",{attrs:{limit:1,fileSize:100,fileType:["mp4","avi","mov","wmv","flv","webm","mkv"],isShowTip:!0},model:{value:e.form.videoUrl,callback:function(t){e.$set(e.form,"videoUrl",t)},expression:"form.videoUrl"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[i("el-input-number",{attrs:{min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"状态",prop:"status"}},[i("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[i("el-radio",{attrs:{label:0}},[e._v("启用")]),i("el-radio",{attrs:{label:1}},[e._v("禁用")])],1)],1)],1)],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),i("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],l=i("5530"),n=(i("d81d"),i("d3b7"),i("2ca0"),i("0643"),i("a573"),i("b775"));function o(e){return Object(n["a"])({url:"/miniapp/video/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/miniapp/video/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/miniapp/video",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/miniapp/video",method:"put",data:e})}function d(e){return Object(n["a"])({url:"/miniapp/video/"+e,method:"delete"})}var m={name:"VideoShowcase",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,videoList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,title:null,status:null},form:{},rules:{title:[{required:!0,message:"视频标题不能为空",trigger:"blur"}],videoUrl:[{required:!0,message:"视频文件不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getVideoUrl:function(e){return e?e.startsWith("http")?e:"/prod-api"+e:""},getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.videoList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,title:null,videoUrl:null,sortOrder:0,status:0,createdAt:null,updatedAt:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加视频展播"},handleUpdate:function(e){var t=this;this.reset();var i=e.id||this.ids;s(i).then((function(e){t.form=e.data,t.open=!0,t.title="修改视频展播"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,i=e.id||this.ids;this.$modal.confirm('是否确认删除视频展播编号为"'+i+'"的数据项？').then((function(){return d(i)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/video/export",Object(l["a"])({},this.queryParams),"视频展播_".concat((new Date).getTime(),".xlsx"))}}},p=m,h=i("2877"),v=Object(h["a"])(p,a,r,!1,null,null,null);t["default"]=v.exports}}]);